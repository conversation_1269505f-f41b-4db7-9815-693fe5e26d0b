package com.qt.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

import org.springframework.web.multipart.MultipartFile;

/**
* <AUTHOR> :cuijian
* @version 创建时间：2021年6月30日 下午2:43:18
* 类说明:MultipartFile转换成File
*/
public class MultipartFileToFile {

	/**
     * MultipartFile 转 File
     *
     * @param file
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile file) throws Exception {
 
        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }
 
    //获取流文件
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
 
    /**
     * 删除本地临时文件
     * @param file
     */
    public static void delteTempFile(File file) {
	    if (file != null) {
	        File del = new File(file.toURI());
	        del.delete();
	    }
    }
    
    /** 
	  * 删除目录（文件夹）以及目录下的文件 
	  * @param   sPath 被删除目录的文件路径 
	  * @return  目录删除成功返回true，否则返回false 
	  */  
	 public static boolean deleteDirectory(String sPath) {  
	     //如果sPath不以文件分隔符结尾，自动添加文件分隔符  
	     if (!sPath.endsWith(File.separator)) {  
	         sPath = sPath + File.separator;  
	     }  
	     File dirFile = new File(sPath);  
	     //如果dir对应的文件不存在，或者不是一个目录，则退出  
	     if (!dirFile.exists() || !dirFile.isDirectory()) {  
	         return false;  
	     }  
	     boolean flag = true;  
	     //删除文件夹下的所有文件(包括子目录)  
	     File[] files = dirFile.listFiles();  
	     for (int i = 0; i < files.length; i++) {  
	         //删除子文件  
	         if (files[i].isFile()) {  
	             flag = deleteFile(files[i].getAbsolutePath());  
	             if (!flag) break;  
	         } //删除子目录  
	         else {  
	             flag = deleteDirectory(files[i].getAbsolutePath());  
	             if (!flag) break;  
	         }  
	     }  
	     if (!flag) return false;  
	     //删除当前目录  
	     if (dirFile.delete()) {  
	         return true;  
	     } else {  
	         return false;  
	     }  
	 }
	 
	 public static boolean deleteFile(String sPath) {  
		 boolean flag = false;  
		 File file = new File(sPath);  
	     // 路径为文件且不为空则进行删除  
	     if (file.isFile() && file.exists()) {  
	         file.delete();  
	         flag = true;  
	     }  
	     return flag;  
	 }

}
