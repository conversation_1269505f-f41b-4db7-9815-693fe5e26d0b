package com.qt.utils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.qt.common.utils.base.Const;

/**
 * cookie操作工具类
 * 
 * <AUTHOR>
 *
 */
public class CookieUtil {
	

	public static final String COOKIE_KEY = "SYSTEM_TYPE";
	
	/**
	 * 获取cookie中的系统类型
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	public static String getLoginCookie(HttpServletRequest request) {
		if (request.getCookies() != null) {
			for (Cookie cookie : request.getCookies()) {
				String name = cookie.getName();
				if (COOKIE_KEY.equals(name)) {
					return cookie.getValue();
				}
			}
		}
		return null;
	}

	/**
	 * 放入系统类型
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	public static void setLoginCookie(String sysName, HttpServletResponse response) {
		Cookie cookie = new Cookie(COOKIE_KEY, sysName);
		cookie.setHttpOnly(true);
		cookie.setPath("/");
		response.addCookie(cookie);
	}
	
	/**
    *
    * Map转String
    * @param map
    * @return
    */
   public static String getMapToString(Map<String,Object> map){
       Set<String> keySet = map.keySet();
       //将set集合转换为数组
       String[] keyArray = keySet.toArray(new String[keySet.size()]);
       //给数组排序(升序)
       Arrays.sort(keyArray);
       //因为String拼接效率会很低的，所以转用StringBuilder
       StringBuilder sb = new StringBuilder();
       for (int i = 0; i < keyArray.length; i++) {
           // 参数值为空，则不参与签名 这个方法trim()是去空格
           if ((String.valueOf(map.get(keyArray[i]))).trim().length() > 0) {
               sb.append(keyArray[i]).append(":").append(String.valueOf(map.get(keyArray[i])).trim());
           }
           if(i != keyArray.length-1){
               sb.append(",");
           }
       }
       return sb.toString();
   }
   
}
