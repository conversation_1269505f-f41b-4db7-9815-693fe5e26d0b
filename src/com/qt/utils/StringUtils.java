package com.qt.utils;

import java.util.HashMap;
import java.util.Map;

public class StringUtils {

	/**
     * 判断是否为空字符串最优代码
     * @param str
     * @return 如果为空，则返回true
     */
    public static boolean isEmpty(String str){
        return str == null || str.trim().length() == 0;
    }

    /**
     * 判断字符串是否非空
     * @param str 如果不为空，则返回true
     * @return
     */
    public static boolean isNotEmpty(String str){
        return !isEmpty(str);
    }
    
    /**
     * 去除字符串数组中空值
     * @param str
     * @return
     */
    public static String[] replaceNull(String[] str){
        //用StringBuffer来存放数组中的非空元素，用“;”分隔
           StringBuffer sb = new StringBuffer();
           for(int i=0; i<str.length; i++) {
               if("".equals(str[i])) {
                   continue;
               }
               sb.append(str[i]);
               if(i != str.length - 1) {
                   sb.append(";");
               }
           }
           //用String的split方法分割，得到数组
           str = sb.toString().split(";");
           return str;
    }
    
    /**
     * 
     * String转map
     * @param str
     * @return
     */
    public static Map<String,Object> getStringToMap(String str){
        //根据逗号截取字符串数组
        String[] str1 = str.split(",");
        //创建Map对象
        Map<String,Object> map = new HashMap<>();
        //循环加入map集合
        for (int i = 0; i < str1.length; i++) {
            //根据":"截取字符串数组
            String[] str2 = str1[i].split(":");
            //str2[0]为KEY,str2[1]为值
            map.put(str2[0],str2[1]);
        }
        return map;
    }

}
