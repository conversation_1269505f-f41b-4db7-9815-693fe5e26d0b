package com.qt.controller.APP.user;

import com.qt.common.utils.security.AccountShiroUtil;
import com.qt.common.utils.security.CipherUtil;
import com.qt.entity.system.account.Account;
import com.qt.service.system.account.AccountService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private AccountService accountService;

    @RequestMapping("/updatePassword")
    @ResponseBody
    public Map<String, Object> updatePassword(String userName, String oldPassword, String newPassword, @RequestHeader String token) {
        Map<String, Object> result = new HashMap<>();
        Session session = SecurityUtils.getSubject().getSession();
        String accountId = AccountShiroUtil.getCurrentUser().getAccountId();
        if (session.getAttribute(accountId).equals(token)) {
            Account perData = accountService.getUserDataById(accountId);
            //原始密码加密对比
            String oldPwdONne = CipherUtil.generatePassword(oldPassword.toUpperCase());
            String oldPwdTwo = CipherUtil.createPwdEncrypt(perData.getLoginName(), oldPwdONne.toUpperCase(), perData.getSalt());
            if (perData.getPassword().equals(oldPwdTwo)) {
                String salt = CipherUtil.createSalt();
                //新密码加密存储
                String newPwdONne = CipherUtil.generatePassword(newPassword.toUpperCase());
                String newPwdTwo = CipherUtil.createPwdEncrypt(perData.getLoginName(), newPwdONne.toUpperCase(), salt);
                perData.setSalt(salt);
                perData.setPassword(newPwdTwo);
                perData.setAccountId(accountId);
                accountService.updatePassword(perData);
                result.put("msg", "修改成功，请重新登录");
            } else {
                result.put("msg", "原始密码错误");
            }
            result.put("flag", true);
        } else {
            result.put("msg", "认证信息有误，请返回登录页");
            result.put("flag", false);
        }
        return result;
    }
}
