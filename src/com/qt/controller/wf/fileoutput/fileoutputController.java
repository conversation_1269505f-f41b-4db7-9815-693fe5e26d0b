package com.qt.controller.wf.fileoutput;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.qt.controller.file.DccInfoController;

/**
 * @date 2017-03-04 
 * <AUTHOR> worldflying
 */
@Controller
@RequestMapping("/wfoutput")
public class fileoutputController {
	private Logger logger = LoggerFactory.getLogger(DccInfoController.class);
	
	@RequestMapping("/xieboout")
	public void xieboout(HttpServletRequest request, HttpServletResponse response) throws Exception {
	
	}
}
