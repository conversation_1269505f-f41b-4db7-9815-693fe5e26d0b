package com.qt.controller.wf.yunwei;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.qt.common.utils.AjaxUtils;
import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DclLine;
import com.qt.entity.wf.Operator;
import com.qt.entity.wf.OptBsaGroup;
import com.qt.entity.wf.OptOperatGroup;
import com.qt.service.statistics.wf.OptBsaGroupSevice;
import com.qt.service.statistics.wf.OptOperatGroupService;

/**
 * @function  运维用户分组  指标码组
 */
@Controller
@RequestMapping("/OptOperatGroup")
public class OptOperatGroupController {

	private Logger logger = LoggerFactory.getLogger(OptOperatGroupController.class);
	
	
	
	
	@Resource
	private OptOperatGroupService optOperatGroupService;
	
	@Resource
	private OptBsaGroupSevice optBsaGroupSevice;
	
		@RequestMapping("/OperatGroupList")
	   @ResponseBody
	   public Map<String, Object>  GetOptList(HttpServletRequest request){
			Integer pageNum = Integer.parseInt(null == request.getParameter("offset") ? 1 + "" : request.getParameter("offset").toString());
			Integer pageSize = Integer.parseInt(null == request.getParameter("limit") ? 1000 + "" : request.getParameter("limit").toString());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("offset", ((pageNum-1)*pageSize));
			params.put("limit", pageSize);
			
			//筛选条件
			if(request.getParameter("optGroupName") != null && !request.getParameter("optGroupName").equals(""))
			{
				params.put("seacher", "1");
				params.put("optGroupName", "'%"+request.getParameter("optGroupName")+"%'");
				params.put("optBsaGroupName", "'%"+request.getParameter("optBsaGroupName")+"%'");
			}else
			{
				params.put("seacher", "0");
			}
			System.out.println("params="+params);
			return optOperatGroupService.GetOptList(params);
	   }
		
		//删除运维组
	   @RequestMapping("/DeleteOptGroup")
	   @ResponseBody
	   public Map<String, Object> DeleteOptGroup(HttpServletRequest request){
			String[] idArray=request.getParameterValues("idArray[]");
			System.out.println("idArray="+idArray);
			 if (optOperatGroupService.DeleteOptGroup(idArray)) {
				   return 	  AjaxUtils.reponseToJson("删除", true); 
			   }else{
				   return    AjaxUtils.reponseToJson("删除", false); 
			   }
	   }
	
	   //新增用户组
	   @RequestMapping("/InsertOptGroup")
	   @ResponseBody
	   public Map<String, Object> InsertOperator(HttpServletRequest request){
		   OptOperatGroup optOperatGroup = new OptOperatGroup();
		   optOperatGroup.setOptAlarmLevel(request.getParameter("optAlarmLevel"));
		   optOperatGroup.setOptGroupName(request.getParameter("optGroupName"));
		   optOperatGroup.setOptBsaGroupId(Integer.parseInt(request.getParameter("optBsaGroupId")));
		   SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		   optOperatGroup.setOptCreateTime(formatter.format(new Date())); //创建时间
		   if (optOperatGroupService.InsertOptGroup(optOperatGroup)) {
			   return 	  AjaxUtils.reponseToJson("新增", true); 
		   }else{
			   return    AjaxUtils.reponseToJson("新增", false); 
		   }
		   
	   }
	
	   
	   @RequestMapping("/UpdateOptGroup")
	   @ResponseBody
	   public Map<String, Object> UpdateOptGroup(HttpServletRequest request){
		   OptOperatGroup optOperatGroup = new OptOperatGroup();
		   optOperatGroup.setOptId(Integer.parseInt(request.getParameter("optId")));
		   optOperatGroup.setOptAlarmLevel(request.getParameter("optAlarmLevel"));
		   optOperatGroup.setOptGroupName(request.getParameter("optGroupName"));
		   optOperatGroup.setOptBsaGroupId(Integer.parseInt(request.getParameter("optBsaGroupId")));
		   if (optOperatGroupService.UpdateOptGroup(optOperatGroup)) {
			   return 	  AjaxUtils.reponseToJson("修改", true); 
		   }else{
			   return    AjaxUtils.reponseToJson("修改", false); 
		   }
	   }
	   
	   //获取指标码组 getOptBsaGroup
	   @RequestMapping("/getOptBsaGroup")
	   @ResponseBody
	   public Map<String, Object>  getOptBsaGroup(HttpServletRequest request){
			Integer pageNum = Integer.parseInt(null == request.getParameter("offset") ? 1 + "" : request.getParameter("offset").toString());
			Integer pageSize = Integer.parseInt(null == request.getParameter("limit") ? 1000 + "" : request.getParameter("limit").toString());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("offset", ((pageNum-1)*pageSize));
			params.put("limit", pageSize);
			
//			//筛选条件
//			if(request.getParameter("optGroupName") != null && !request.getParameter("optGroupName").equals(""))
//			{
//				params.put("seacher", "1");
//				params.put("optGroupName", "'%"+request.getParameter("optGroupName")+"%'");
//				params.put("optBsaGroupName", "'%"+request.getParameter("optBsaGroupName")+"%'");
//				params.put("optAlarmLevel", "'%"+request.getParameter("optAlarmLevel")+"%'");
//			}else
//			{
//				params.put("seacher", "0");
//			}
			System.out.println("params="+params);
			return optBsaGroupSevice.GetbsaList(params);
	   }
	   
	   //新增用户组
	   @RequestMapping("/InsertOptBsaGroup")
	   @ResponseBody
	   public Map<String, Object> InsertOptBsaGroup(HttpServletRequest request){
		   
		   OptBsaGroup optBsaGroup = new OptBsaGroup();
		   optBsaGroup.setOptBsaGroupName(request.getParameter("optBsaGroupName"));
		   SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		   optBsaGroup.setOptBsaText(request.getParameter("optBsaText"));
		   optBsaGroup.setOptCreateTime(formatter.format(new Date()));
		   if (optBsaGroupSevice.InsertOptGroup(optBsaGroup)) {
			   return 	  AjaxUtils.reponseToJson("新增", true); 
		   }else{
			   return    AjaxUtils.reponseToJson("新增", false); 
		   }
		   
	   }
	   
	  //删除指标组
	   @RequestMapping("/deleteOptBsaGroup")
	   @ResponseBody
	   public Map<String, Object> deleteOptBsaGroup(HttpServletRequest request){
		   Integer BsaGroupId=Integer.parseInt(request.getParameter("BsaGroupId"));
			 if (optBsaGroupSevice.DeleteOptGroup(BsaGroupId)) {
				   return 	  AjaxUtils.reponseToJson("删除", true); 
			   }else{
				   return    AjaxUtils.reponseToJson("删除", false); 
			   }
	   }
	   
	   
	   //修改指标组
	   @RequestMapping("/UpdateOptBsaGroup")
	   @ResponseBody
	   public Map<String, Object> UpdateOptBsaGroup(HttpServletRequest request){
		   OptBsaGroup optBsaGroup = new OptBsaGroup();
		   optBsaGroup.setOptBsaId(Integer.parseInt(request.getParameter("optBsaId")));
		   optBsaGroup.setOptBsaGroupName(request.getParameter("optBsaGroupName"));
		   optBsaGroup.setOptBsaText(request.getParameter("optBsaText"));
		   if (optBsaGroupSevice.UpdateOptBsaGroup(optBsaGroup)) {
			   return 	  AjaxUtils.reponseToJson("修改", true); 
		   }else{
			   return    AjaxUtils.reponseToJson("修改", false); 
		   }
	   }
}
