package com.qt.controller.wf.yunwei;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;

import com.qt.bean.file.OperatorForm;
import com.qt.bean.statistics.CacheRealData;
import com.qt.controller.base.BaseController;
//import com.qt.entity.baselib.BsaCode;
import com.qt.entity.file.DcsStation;
import com.qt.entity.system.resources.Resources;
import com.qt.entity.wf.Operator;
import com.qt.entity.wf.OptBsaGroup;
import com.qt.entity.wf.Record;
import com.qt.service.file.DcsStationService;
import com.qt.service.statistics.wf.YunweiService;
import com.qt.common.utils.AjaxUtils;
import com.qt.common.utils.MapUtil;
import com.qt.common.utils.base.Const;

/*
 * 首页跳转controller 
 * author by worldflying.cn
 */
@Controller
@RequestMapping("/yunwei")
public class yunweiController extends BaseController {
	
	private Logger logger = LoggerFactory.getLogger(yunweiController.class);
	
    @Resource
	private YunweiService YunweiService;
    
    @Resource
    private DcsStationService dcsStationService;
    
	@SuppressWarnings("unchecked")
	@RequestMapping("/main")
	public String mian(HttpServletRequest request,ModelMap map,Model model)
	{
		if(doSecurityIntercept(Const.RESOURCES_TYPE_MENU)){
			if(CacheRealData.stationArchivesList5.size()==0){
				CacheRealData.stationArchivesList5 = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				model.addAttribute("permitBtn", getPermitBtn(Const.RESOURCES_TYPE_FUNCTION));	
			}else if(CacheRealData.stationArchivesList5.size()>0 && (getPermitBtn(Const.RESOURCES_TYPE_FUNCTION).size() != 0)){
				List<Resources> list = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				CacheRealData.stationArchivesList5 = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				model.addAttribute("permitBtn", getPermitBtn(Const.RESOURCES_TYPE_FUNCTION));
			}else{
				model.addAttribute("permitBtn", CacheRealData.stationArchivesList5);
			}
			System.out.println("model="+model);
			map.addAttribute("page", request.getParameter("page"));
			return "jsp/wf/wf_zz/Operator";
		}
		return Const.NO_AUTHORIZED_URL;	
		//return ;
	}
	
	@SuppressWarnings("unchecked")
	@RequestMapping("/equipment")
	public String equipment(HttpServletRequest request,ModelMap map,Model model)
	{
		if(doSecurityIntercept(Const.RESOURCES_TYPE_MENU)){
			if(CacheRealData.stationArchivesList5.size()==0){
				CacheRealData.stationArchivesList5 = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				model.addAttribute("permitBtn", getPermitBtn(Const.RESOURCES_TYPE_FUNCTION));	
			}else if(CacheRealData.stationArchivesList5.size()>0 && (getPermitBtn(Const.RESOURCES_TYPE_FUNCTION).size() != 0)){
				List<Resources> list = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				CacheRealData.stationArchivesList5 = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				model.addAttribute("permitBtn", getPermitBtn(Const.RESOURCES_TYPE_FUNCTION));
			}else{
				model.addAttribute("permitBtn", CacheRealData.stationArchivesList5);
			}
			System.out.println("CacheRealData.stationArchivesList5.size()="+CacheRealData.stationArchivesList5.size());
			System.out.println("model="+model);
			map.addAttribute("page", request.getParameter("page"));
			return "jsp/wf/wf_zz/equipment";
		}
		return Const.NO_AUTHORIZED_URL;	
		//return ;
	}
	
	@RequestMapping("/yunwei")
	public String yunwei(HttpServletRequest request,ModelMap map,Model model)
	{
		if(doSecurityIntercept(Const.RESOURCES_TYPE_MENU)){
			if(CacheRealData.stationArchivesList5.size()==0){
				CacheRealData.stationArchivesList5 = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				model.addAttribute("permitBtn", getPermitBtn(Const.RESOURCES_TYPE_FUNCTION));	
			}else if(CacheRealData.stationArchivesList5.size()>0 && (getPermitBtn(Const.RESOURCES_TYPE_FUNCTION).size() != 0)){
				List<Resources> list = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				CacheRealData.stationArchivesList5 = getPermitBtn(Const.RESOURCES_TYPE_FUNCTION);
				model.addAttribute("permitBtn", getPermitBtn(Const.RESOURCES_TYPE_FUNCTION));
			}else{
				model.addAttribute("permitBtn", CacheRealData.stationArchivesList5);
			}
			System.out.println("model="+model);
			map.addAttribute("page", request.getParameter("page"));
			return "jsp/wf/wf_zz/yunwei";
		}
		return Const.NO_AUTHORIZED_URL;	
		//return ;
	}
	/**
	 * <AUTHOR> 李强
	 * @date 2017年3月20日15:21:45
	 * @功能 	获取操作人员列表
	 * @param request
	 * @return
	 */
	   @RequestMapping("GetOptList")
	   @ResponseBody
	   public Map<String, Object>  GetOptList(HttpServletRequest request){
			Integer pageNum = Integer.parseInt(null == request.getParameter("offset") ? 0 + "" : request.getParameter("offset").toString());
			Integer pageSize = Integer.parseInt(null == request.getParameter("limit") ? 1000 + "" : request.getParameter("limit").toString());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("offset", ((pageNum-1)*pageSize));
			params.put("limit", pageSize);
			
			if(request.getParameter("opt_company") != null && !request.getParameter("opt_company").equals(""))
			{
				params.put("seacher", "1");
				params.put("opt_company", "'%"+request.getParameter("opt_company")+"%'");
				params.put("opt_name", "'%"+request.getParameter("opt_name")+"%'");
				params.put("opt_phone", "'%"+request.getParameter("opt_phone")+"%'");
				params.put("opt_position", "'%"+request.getParameter("opt_position")+"%'");
				params.put("opt_sex", "'%"+request.getParameter("opt_sex")+"%'");
				params.put("opt_group_name", "'%"+request.getParameter("opt_group_name")+"%'");
				params.put("dcc_first_name", "'%"+request.getParameter("dcc_first_name")+"%'");
			}else
			{
				params.put("seacher", "0");
			}

			
			System.out.println("params="+params);
			return YunweiService.GetOptList(params);
	   }
	   
	   
	   @RequestMapping("InsertOperator")
	   @ResponseBody
	   public Map<String, Object> InsertOperator(HttpServletRequest request){
		   Operator operator = new Operator();
		   operator.setOpt_id(request.getParameter("optId"));
		   operator.setOpt_name(request.getParameter("oName"));
		   operator.setOpt_phone(request.getParameter("oPhone"));
		   operator.setOpt_sex(request.getParameter("oSex"));
		   operator.setOpt_dcc_id(Integer.parseInt(request.getParameter("dcbDccId"))); //公司名称
		   operator.setOpt_group(Integer.parseInt(request.getParameter("optGroupId"))); //报警分组
		   operator.setOpt_position(request.getParameter("oPosition"));
		   operator.setOpt_company(request.getParameter("oCompany"));
		   operator.setOpt_total_time(request.getParameter("oSumtime"));
		   operator.setOpt_total_frequency(request.getParameter("oSumfrequency"));
		   if (YunweiService.InsertOperator(operator)) {
			   return 	  AjaxUtils.reponseToJson("新增", true); 
		   }else{
			   return    AjaxUtils.reponseToJson("新增", false); 
		   }
		   
	   }
	   
	   @RequestMapping("UpdateOperator")
	   @ResponseBody
	   public Map<String, Object> UpdateOperator(HttpServletRequest request){
		   Operator operator = new Operator();
		   operator.setId(Integer.parseInt(request.getParameter("oId")));
		   operator.setOpt_id(request.getParameter("optId"));
		   operator.setOpt_name(request.getParameter("oName"));
		   operator.setOpt_phone(request.getParameter("oPhone"));
		   operator.setOpt_sex(request.getParameter("oSex"));
		   operator.setOpt_dcc_id(Integer.parseInt(request.getParameter("dcbDccId"))); //公司名称
		   operator.setOpt_group(Integer.parseInt(request.getParameter("optGroupId"))); //报警分组
		   operator.setOpt_position(request.getParameter("oPosition"));
		   operator.setOpt_company(request.getParameter("oCompany"));
		   operator.setOpt_total_time(request.getParameter("oSumtime"));
		   operator.setOpt_total_frequency(request.getParameter("oSumfrequency"));
		  // System.out.println(operator.getId());
		   if (YunweiService.UpdateOperator(operator)) {
			   return 	  AjaxUtils.reponseToJson("修改", true); 
		   }else{
			   return    AjaxUtils.reponseToJson("修改", false); 
		   }
	   }
	   @RequestMapping("DeleteOperator")
	   @ResponseBody
	   public Map<String, Object> DeleteOperator(HttpServletRequest request)
	   {
			String[] idArray=request.getParameterValues("idArray[]");
			System.out.println("idArray="+idArray);
			 if (YunweiService.DeleteOperator(idArray)) {
				   return 	  AjaxUtils.reponseToJson("删除", true); 
			   }else{
				   return    AjaxUtils.reponseToJson("删除", false); 
			   }
	   }
	   
	   
	   //运维人员关联站点
	  @RequestMapping("/getOptStation")
	   @ResponseBody
	   public Map<String, Object>  getOptStation(HttpServletRequest request){
			Map<String, Object> params = new HashMap<String, Object>();
			Map<String , Object> resultMap = new HashMap<String, Object>();
			Integer stationOptId=Integer.parseInt(request.getParameter("stationOptId"));
			 params.put("id", stationOptId);	
			 List<Operator> getOptLists = YunweiService.GetOptLists(params);
			 Operator operator = getOptLists.get(0);//获取运维人员
			String[] accountStation=null;
			if (operator.getOpt_dcs_id()!=null) {
				accountStation=operator.getOpt_dcs_id().split(",");
			}
			//查询站点
			params.put("accountStation", accountStation);
			List<DcsStation> toStationChecked=null;
			if(accountStation!=null){ //已绑定的站点
				toStationChecked= dcsStationService.getToStationChecked(params);
			}
			List<DcsStation> noCheckedStations = dcsStationService.getToStationNoChecked(params);//查询筛选出未绑定站点信息
			resultMap.put("checkedStations", toStationChecked);
			resultMap.put("noCheckedStations", noCheckedStations);	
			
			return resultMap;
	   }
	   
	   //保存关联人员修改
	  @RequestMapping("/updataOptStationCheck")
	   @ResponseBody
	   public Map<String, Object>  updataOptStationCheck(HttpServletRequest request){
		  Operator operator = new Operator();
		  operator.setId(Integer.parseInt(request.getParameter("id")));
		  operator.setOpt_dcs_id(request.getParameter("opt_dcs_id"));
		  if (YunweiService.updataOptStationCheck(operator)) {
			   return 	  AjaxUtils.reponseToJson("修改", true); 
		   }else{
			   return    AjaxUtils.reponseToJson("修改", false); 
		   }
	  }
	   
	   
	   
	   /**
	   * <AUTHOR> 
	   * @Description  验证人员编号的唯一性
	   * @date 2018年3月16日上午11:21:35  
	    */
	   @RequestMapping("/checkIfByOptId")
	   @ResponseBody
	   public Map<String, Object> checkIfByOptId(HttpServletRequest request, OperatorForm form,String jsonpCallback){
			Map<String, Object> retMap = new HashMap<String, Object>();
			Map<String, Object> params = new HashMap<String, Object>();
			params = MapUtil.toMap(form);
			List<Operator> list=YunweiService.GetOptLists(params);
			if(list!=null && list.size()>0){
				retMap.put("flag", false);
			}else{
				retMap.put("flag", true);
			}
			return retMap;
	   } 
	   
	   @RequestMapping("GetRecordList")
	   @ResponseBody
	   public Map<String,Object> GetRecordList(HttpServletRequest request){

			Integer pageNum = Integer.parseInt(null == request.getParameter("offset") ? 0 + "" : request.getParameter("offset").toString());
			Integer pageSize = Integer.parseInt(null == request.getParameter("limit") ? 10 + "" : request.getParameter("limit").toString());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("offset", pageNum);
			params.put("limit", pageSize);	
			params.put("startTime", request.getParameter("startTime"));
			params.put("endTime", request.getParameter("endTime"));	
			System.out.println("params1="+params);
			Map<String, Object> resultMap= new HashMap<String, Object>();
			resultMap.put("rows", YunweiService.GetRecordList(params));
			resultMap.put("total", YunweiService.GetCountRecordList(params));
			return  resultMap;
	   }
	   
	   @RequestMapping("GetCountRecordList")
	   @ResponseBody
	   public int GetCountRecordList(HttpServletRequest request){

			Integer pageNum = Integer.parseInt(null == request.getParameter("offset") ? 0 + "" : request.getParameter("offset").toString());
			Integer pageSize = Integer.parseInt(null == request.getParameter("limit") ? 10 + "" : request.getParameter("limit").toString());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("offset", pageNum);
			params.put("limit", pageSize);	
			params.put("startTime", request.getParameter("startTime"));
			params.put("endTime", request.getParameter("endTime"));					
			return  YunweiService.GetCountRecordList(params);
	   }
	   
	   
	   @RequestMapping("InsertRecord")
	   @ResponseBody
	   public String InsertRecord(HttpServletRequest request){
		   Record record = new Record();
		   record.setOpt_execute_time(request.getParameter("opt_execute_time"));
		   record.setOpt_create_name(request.getParameter("opt_create_name"));
		   record.setOpt_create_time(request.getParameter("opt_create_time"));
		   record.setOpt_title(request.getParameter("opt_title"));
		   record.setOpt_content(request.getParameter("opt_content"));
		   return YunweiService.InsertRecord(record);
//		   if(YunweiService.InsertRecord(record) > -1)
//		   {
//			   return   record.getId();
//		   }
//		   else
//		   {
//			   return   "-1";
//		   }
		     
	   }
	   
	   @RequestMapping("UpdateRecord")
	   @ResponseBody
	   public int UpdateRecord(HttpServletRequest request){
		   Record record = new Record();
		   record.setId(request.getParameter("id"));
		   record.setOpt_execute_time(request.getParameter("opt_execute_time"));
		   record.setOpt_create_name(request.getParameter("opt_create_name"));
		   record.setOpt_create_time(request.getParameter("opt_create_time"));
		   record.setOpt_title(request.getParameter("opt_title"));
		   record.setOpt_content(request.getParameter("opt_content"));
		   System.out.println(record.getId());
		   return YunweiService.UpdateRecord(record);	   
	   }
	   
	   @RequestMapping("UpdateYRecord")
	   @ResponseBody
	   public int UpdateYRecord(HttpServletRequest request){
		   Record record = new Record();
		   record.setId(request.getParameter("id"));
		   record.setOpt_execute_time(request.getParameter("opt_execute_time"));
		   record.setOpt_create_name(request.getParameter("opt_create_name"));
		   record.setOpt_create_time(request.getParameter("opt_create_time"));
		   record.setOpt_title(request.getParameter("opt_title"));
		   record.setOpt_content(request.getParameter("opt_content"));
		   System.out.println(record.getId());
		   return YunweiService.UpdateYRecord(record);	   
	   }
	   @RequestMapping("DeleteRecord")
	   @ResponseBody
	   public int DeleteRecord(HttpServletRequest request)
	   {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("rId", request.getParameter("rId"));
			return  YunweiService.DeleteRecord(params);			 
	   }
	   
	   
	   @RequestMapping("GetYRecordList")
	   @ResponseBody
	   public Map<String,Object> GetYRecordList(HttpServletRequest request){

			Integer pageNum = Integer.parseInt(null == request.getParameter("offset") ? 0 + "" : request.getParameter("offset").toString());
			Integer pageSize = Integer.parseInt(null == request.getParameter("limit") ? 10 + "" : request.getParameter("limit").toString());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("offset", pageNum);
			params.put("limit", pageSize);	
			params.put("startTime", request.getParameter("startTime"));
			params.put("endTime", request.getParameter("endTime"));
			 System.out.println("params="+params);
			if(request.getParameter("opt_execute_time") != null && !request.getParameter("opt_execute_time").equals(""))
			{
				params.put("opt_execute_time ", "'%"+request.getParameter("opt_execute_time ")+"%'");
			}else
			{
				params.put("opt_execute_time", "0");
			}
			Map<String, Object> resultMap= new HashMap<String, Object>();
			resultMap.put("rows", YunweiService.GetYRecordList(params));
			resultMap.put("total", YunweiService.GetCountYRecordList(params));
			return  resultMap;
	   }
	   
	   
	   @RequestMapping("GetCountYRecordList")
	   @ResponseBody
	   public int GetCountYRecordList(HttpServletRequest request){

			Integer pageNum = Integer.parseInt(null == request.getParameter("offset") ? 0 + "" : request.getParameter("offset").toString());
			Integer pageSize = Integer.parseInt(null == request.getParameter("limit") ? 8 + "" : request.getParameter("limit").toString());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("offset", pageNum);
			params.put("limit", pageSize);	
			params.put("startTime", request.getParameter("startTime"));
			params.put("endTime", request.getParameter("endTime"));
			 //System.out.println("params="+params);	
			return  YunweiService.GetCountYRecordList(params);
	   }
	   
	   @RequestMapping("InsertYRecord")
	   @ResponseBody
	   public String InsertYRecord(HttpServletRequest request){
		   Record record = new Record();
		   record.setOpt_execute_time(request.getParameter("opt_execute_time"));
		   record.setOpt_create_name(request.getParameter("opt_create_name"));
		   record.setOpt_create_time(request.getParameter("opt_create_time"));
		   record.setOpt_title(request.getParameter("opt_title"));
		   record.setOpt_content(request.getParameter("opt_content"));
		   return  YunweiService.InsertYRecord(record);
//		   if(YunweiService.InsertYRecord(record) > 0)
//		   {
//			   return   record.getId();
//		   }
//		   else
//		   {
//			   return   "-1";
//		   }
		     
	   }
	   @RequestMapping("DeleteYRecord")
	   @ResponseBody
	   public int DeleteYRecord(HttpServletRequest request)
	   {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("rId", request.getParameter("rId"));
			return  YunweiService.DeleteYRecord(params);			 
	   }
}
