package com.qt.controller.wf.online_monitor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.qt.common.utils.MapUtil;
import com.qt.controller.base.BaseController;
import com.qt.controller.statistics.StlDayController;
import com.qt.controller.statistics.StpMonthController;
import com.qt.entity.wf.online_monitor;
import com.qt.service.statistics.StlDayService;
import com.qt.service.statistics.wf.online_monitorService;
//import com.sun.xml.internal.bind.v2.schemagen.xmlschema.List;

/*
 * 2017年2月14日14:23:18  沃航科技  李强  
 * 在线监测控制器  
 */
@Controller
@RequestMapping("/online_monitor")
public class online_monitorController extends BaseController {
	
	private Logger logger = LoggerFactory.getLogger(online_monitorController.class);
	
    @Resource
	private online_monitorService Online_monitorService;
	/*
	 * 2017年2月14日14:23:58 沃航科技 李强
	 * 指标关联控制器
	 * @throws JSONException
	 */
    
	@RequestMapping("/indexassociation")
	public String indexassociation() {		
		return "jsp/wf_online_monitor/indexassociation";
	}
	
	
	/**
	 * <AUTHOR> 李强
	 * @data 2017年2月15日14:47:40
	 * @功能 获取指标联系图表数据
	 * @param request
	 * @return Map<String, java.util.List<online_monitor>>
	 * @throws ParseException 
	 */
	@RequestMapping("/GetIndexAssociation")
	@ResponseBody
	public  java.util.List<online_monitor>  GetIndexAssociation(HttpServletRequest request) throws ParseException 
	{
		Map<String, Object> params = new HashMap<String, Object>();  //存放查询语句参数
		//params = MapUtil.toMap(form);
		params.put("stostationid",request.getParameter("stostationid"));
		params.put("stolineid",request.getParameter("stolineid"));
		params.put("byqid",request.getParameter("byqid"));
		String time = request.getParameter("time");
	    //System.out.println("time="+time);
		//时间处理
		Calendar c = Calendar.getInstance();  
	    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	    Date Time = sdf.parse(time) ;
	    c.setTime(Time);
	    c.add(c.DATE,1);	    
 		params.put("startTime",time);
		params.put("endTime",sdf.format(c.getTime()));
		System.out.println("params="+params);
		//数据库取数据
		java.util.List<online_monitor> indexassociationList = Online_monitorService.Get_indexassociation(params , request.getParameter("target"));				
		return indexassociationList;
	}
	
	@RequestMapping("/GetDay_PowerFactory")
	@ResponseBody
	public  LinkedHashMap<String, Double>  GetDay_PowerFactory(HttpServletRequest request) throws ParseException {
		Map<String, Object> params = new HashMap<String, Object>();  //存放查询语句参数
		//params = MapUtil.toMap(form);
		params.put("stostationid",request.getParameter("stostationid"));
		String time = request.getParameter("time");
		params.put("startTime",time+" 00:00:00");
		params.put("endTime", time+" 23:59:59");
		LinkedHashMap<String, Double> resMap = Online_monitorService.GetDay_PowerFactory(params);		
		return resMap;
	}
	
	@RequestMapping("/GetDay_PowerFactory2")
	@ResponseBody
	public  LinkedHashMap<String, Double>  GetDay_PowerFactory2(HttpServletRequest request) throws ParseException {
		Map<String, Object> params = new HashMap<String, Object>();  //存放查询语句参数
		//params = MapUtil.toMap(form);
		params.put("stostationid",request.getParameter("stostationid"));
		LinkedHashMap<String, Double> resMap = Online_monitorService.GetDay_PowerFactory2(params);		
		return resMap;
	}
	
}
