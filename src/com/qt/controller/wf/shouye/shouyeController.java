package com.qt.controller.wf.shouye;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.qt.controller.base.BaseController;
import com.qt.entity.wf.device_rank;
import com.qt.service.statistics.wf.device_rankService;

/*
 * 首页跳转 /backheader/main
 * 设备排名 /backheader/effrank/device_rank/getdata
 * 时段排名 /backheader//effrank/device_rank/gettimedata
 * 班组考核 /backheader//effrank/device_rank/getbanzudata
 * author by worldflying.cn
 */
@Controller
@RequestMapping("/backheader")
public class shouyeController extends BaseController {

	private Logger logger = LoggerFactory.getLogger(shouyeController.class);
	
    @Resource
	private device_rankService Device_rankService;
	
	
	@RequestMapping("/main")
	public String shouye()
	{
		return "jsp/wf/wf_backheader/index";
	}
	
	@RequestMapping("/effrank/device_rank")
	//@ResponseBody
	public String devicerank() {
		return "jsp/wf/wf_effrank/device_rank";
	}
	
	@RequestMapping("/effrank/device_rank/getdata")
	@ResponseBody
	//Map<Float ,Float>
	private Map<String ,Float> getdata(HttpServletRequest request) {
		// TODO Auto-generated method stub
		Date d=new Date();
		Date end=new Date();
		SimpleDateFormat df=new SimpleDateFormat("yyyy-MM-dd"); 
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("stostationid",request.getParameter("stationid"));
		if(request.getParameter("time")!=null)
		{
			if(request.getParameter("time").equals("yesterday"))
			{	
//				params.put("starttime",df.format(new Date(d.getTime() - (long)1 * 24 * 60 * 60 * 1000))+" 00:00%");
//				params.put("endtime",df.format(new Date(d.getTime() - (long)1 * 24 * 60 * 60 * 1000))+" 23:45%");
				params.put("starttime",df.format(new Date(d.getTime() - (long)1 * 24 * 60 * 60 * 1000))+"%");
				params.put("endtime",df.format(new Date(d.getTime() - (long)1 * 24 * 60 * 60 * 1000))+"%");
			}
			else if(request.getParameter("time").equals("lastweek"))
			{
				Calendar cal = Calendar.getInstance();
				int n = cal.get(Calendar.DAY_OF_WEEK) - 1;
		        if (n == 0) {
		            n = 7;
		        }
			    cal.add(Calendar.DATE, -(7 + (n - 1)));
			    d = cal.getTime();
				cal.add(Calendar.DATE, 6);
				end = cal.getTime();
//				params.put("starttime",df.format(d)+" 00:00%");
//				params.put("endtime",df.format(end)+" 23:45%");
				params.put("starttime",df.format(d)+"%");
				params.put("endtime",df.format(end)+"%");
			}
			else if(request.getParameter("time").equals("lastmonth"))
			{
				Calendar cal = Calendar.getInstance();
				cal.add(Calendar.MONTH, -1);
				cal.set(Calendar.DAY_OF_MONTH,1);
				d = cal.getTime();
				Calendar cale = Calendar.getInstance();
				cale.set(Calendar.DAY_OF_MONTH,0);
				end =cale.getTime();
//				params.put("starttime",df.format(d)+" 00:00%");
//				params.put("endtime",df.format(end)+" 23:45%");
				params.put("starttime",df.format(d)+"%");
				params.put("endtime",df.format(end)+"%");
				
			}
			else if(request.getParameter("time").equals("lastthreemonth"))
			{
				Calendar cal = Calendar.getInstance();
				int year = cal.get(Calendar.YEAR);
				int months = cal.get(Calendar.MONTH)+1;
				int monthe = months+2;
				if(months>=1&&months<=3)
				{
//					params.put("starttime",year+"-10-01 00:00%");
//					params.put("endtime",year+"-12-31 23:45%");
					params.put("starttime",year+"-10-01%");
					params.put("endtime",year+"-12-31%");

				}else if(months>=4&&months<=6)
				{
//					params.put("starttime",year+"-01-01 00:00%");
//					params.put("endtime",year+"-03-31 23:45%");
					params.put("starttime",year+"-01-01%");
					params.put("endtime",year+"-03-31%");
				}
				else if(months>=7&&months<=9)
				{
//					params.put("starttime",year+"-04-01 00:00%");
//					params.put("endtime",year+"-06-30 23:45%");
					params.put("starttime",year+"-04-01%");
					params.put("endtime",year+"-06-30%");
					
				}else if (months>=10&&months<=12) 
				{
//					params.put("starttime",year+"-07-01 00:00%");
//					params.put("endtime",year+"-09-30 23:45%");
					params.put("starttime",year+"-07-01%");
					params.put("endtime",year+"-09-30%");
				}
			}
			else if(request.getParameter("time").equals("lastyear"))
			{	
				Calendar cal = Calendar.getInstance();
				int year = cal.get(Calendar.YEAR) - 1;
//				params.put("starttime",year+"-01-01 00:00%");
//				params.put("endtime",year+"-12-31 23:45%");
				params.put("starttime",year+"-01-01%");
				params.put("endtime",year+"-12-31%");
			}
			else if(request.getParameter("time").equals("byyourself"))
			{	
//				params.put("starttime",request.getParameter("start")+" 00:00%");
//				params.put("endtime",request.getParameter("end")+" 23:45%");
				params.put("starttime",request.getParameter("start")+"%");
				params.put("endtime",request.getParameter("end")+"%");
				
			}
		}
		Map<String ,Float> devicerank = new HashMap<String ,Float>();
		Map<String, java.util.List<device_rank>> dclarr = Device_rankService.Get_huilu(params);
		java.util.List<device_rank> dclnum = dclarr.get("result");
		for(int i=0;i<dclnum.size();i++)
		{
			params.put("dcln",dclnum.get(i).getDclid());
			Map<String, java.util.List<device_rank>> shuju = Device_rankService.Shuju(params);
			java.util.List<device_rank> shujun = shuju.get("result");
			if(shujun.size()>0)
			{
				devicerank.put(shujun.get(0).getLinename(),shujun.get(0).getNenghao());
			}
		}
		
		List<Map.Entry<String,Float>> list = new ArrayList<Map.Entry<String,Float>>(devicerank.entrySet());
		Collections.sort( list, new Comparator<Map.Entry<String, Float>>()  
        {  
            public int compare( Map.Entry<String, Float> o1, Map.Entry<String, Float> o2 )  
            {  
                return (o2.getValue()).compareTo( o1.getValue() );  
            }  
        } );  
		 Map<String,Float> result = new LinkedHashMap<String,Float>();  
	        for (Map.Entry<String, Float> entry : list)  
	        {  
	            result.put( entry.getKey(), entry.getValue() );  
	        }  
		return result;
		
//		Map<String, java.util.List<device_rank>> rankList = Device_rankService.Get_devicerank(params);	
//		Map<String, java.util.List<device_rank>> rankListEnd = Device_rankService.Get_devicerankEnd(params);
//		Map<String, java.util.List<device_rank>> dclname = Device_rankService.Get_linename(params);
//		Map<String ,Float> devicerank = new HashMap<String ,Float>();
//		java.util.List<device_rank> ListSt = rankList.get("result");
//		java.util.List<device_rank> ListEn = rankListEnd.get("result");
//		java.util.List<device_rank> dcln = dclname.get("result");
//		for(int i=0; i<ListSt.size();i++)
//		{
//			//System.out.println("st"+ListSt.get(i).getNenghao()+" "+ListSt.get(i).getStolinid());
//			for(int j=0; j<ListEn.size();j++)
//			{
//				if( ListSt.get(i).getStolinid() == ListEn.get(j).getStolinid() )
//				{
//					for(int h=0; h<dcln.size();h++)
//					{
//						if(ListEn.get(j).getStolinid() == dcln.get(h).getDclid())
//						{
//							devicerank.put(dcln.get(h).getLinename(),(ListEn.get(j).getNenghao() - ListSt.get(i).getNenghao() ));
//							break;
//						}
//					}
//				}
//			}
//			
//		}
//		List<Map.Entry<String,Float>> list = new ArrayList<Map.Entry<String,Float>>(devicerank.entrySet());
//		Collections.sort( list, new Comparator<Map.Entry<String, Float>>()  
//        {  
//            public int compare( Map.Entry<String, Float> o1, Map.Entry<String, Float> o2 )  
//            {  
//                return (o2.getValue()).compareTo( o1.getValue() );  
//            }  
//        } );  
//		 Map<String,Float> result = new LinkedHashMap<String,Float>();  
//	        for (Map.Entry<String, Float> entry : list)  
//	        {  
//	            result.put( entry.getKey(), entry.getValue() );  
//	        }  
//		return result;
	}


	@RequestMapping("/effrank/device_rank/gettimedata")
	@ResponseBody
	//时段排名	 
	//Map<String ,Float>
	private Map<String ,Float> gettimedata(HttpServletRequest request) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("stostationid",request.getParameter("stationid"));
//		params.put("starttime",request.getParameter("start")+" 00:00%");
//		params.put("endtime",request.getParameter("end")+" 23:45%");
		params.put("starttime",request.getParameter("start")+"%");
		params.put("endtime",request.getParameter("end")+"%");
		//将lineid集合转换成list集合
		String[] reallineid =  request.getParameter("lid").split(",");
		List<String> userList = new ArrayList<String>();
		Collections.addAll(userList, reallineid);
		Map<String ,Float> devicerank = new HashMap<String ,Float>();
		for(int i=0;i<userList.size();i++)
		{
			if(i!=0)
			{
				params.put("dcln",userList.get(i));
				Map<String, java.util.List<device_rank>> shuju = Device_rankService.Shuju(params);
				java.util.List<device_rank> shujun = shuju.get("result");
				if(shujun.size()>0)
				{
					devicerank.put(shujun.get(0).getLinename(),shujun.get(0).getNenghao());
				}
			}
		}
		
		
		//System.out.println(request.getParameter("stationid"));
		//检验device_rank.js传过来的参数
//		Map<String, java.util.List<device_rank>> rankList = Device_rankService.Get_devicerank(params);	
//		Map<String, java.util.List<device_rank>> rankListEnd = Device_rankService.Get_devicerankEnd(params);
//		Map<String, java.util.List<device_rank>> dclname = Device_rankService.Get_linename(params);
//		Map<String ,Float> devicerank = new HashMap<String ,Float>();
//		java.util.List<device_rank> ListSt = rankList.get("result");
//		java.util.List<device_rank> ListEn = rankListEnd.get("result");
//		java.util.List<device_rank> dcln = dclname.get("result");
//		for(int i=0; i<ListSt.size();i++)
//		{
//			for(int j=0; j<ListEn.size();j++)
//			{
//				if( ListSt.get(i).getStolinid() == ListEn.get(j).getStolinid() )
//				{
//					for(int h=0; h<dcln.size();h++)
//					{
//						if(ListEn.get(j).getStolinid() == dcln.get(h).getDclid())
//						{
//							if(userList.contains(Integer.toString(dcln.get(h).getDclid())))
//							{
//								devicerank.put(dcln.get(h).getLinename(),(ListEn.get(j).getNenghao() - ListSt.get(i).getNenghao() ));
//								break;			
//							}
//						}
//					}
//				}
//			}
//
//		}
		
		List<Map.Entry<String,Float>> list = new ArrayList<Map.Entry<String,Float>>(devicerank.entrySet());
		Collections.sort( list, new Comparator<Map.Entry<String, Float>>()  
        {  
            public int compare( Map.Entry<String, Float> o1, Map.Entry<String, Float> o2 )  
            {  
                return (o2.getValue()).compareTo( o1.getValue() );  
            }  
        } );  
		 Map<String,Float> result = new LinkedHashMap<String,Float>();  
	        for (Map.Entry<String, Float> entry : list)  
	        {  
	            result.put( entry.getKey(), entry.getValue() );  
	        }        
		return result;
	}
	
	@RequestMapping("/effrank/device_rank/getbanzudata")
	@ResponseBody
	//班组排名	
	//Map<String ,Float>
	private Map<String ,Float> getbanzudata(HttpServletRequest request) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("stostationid",request.getParameter("stationid"));
		params.put("byqid",request.getParameter("stabyqid"));
		params.put("starttime",request.getParameter("start")+"%");
		params.put("endtime",request.getParameter("end")+"%");
		
		//获取全部回路的起始值
		Map<String, java.util.List<device_rank>> rankList = Device_rankService.Get_devicerank(params);	
		//获取全部回路的结束值
		Map<String, java.util.List<device_rank>> rankListEnd = Device_rankService.Get_devicerankEnd(params);
		
		if(request.getParameter("stabyqid") == null)
		{
			//获取同站点下所有变压器数据
			Map<String, java.util.List<device_rank>> byqname = Device_rankService.Get_byqname(params);
			//多变压器返回map集
			Map<String ,Float> byqdata = new HashMap<String ,Float>();
			java.util.List<device_rank> ListSt = rankList.get("result");
			java.util.List<device_rank> ListEn = rankListEnd.get("result");
			java.util.List<device_rank> byqall = byqname.get("result");
			for(int i=0; i<ListSt.size();i++)
			{
				for(int j=0; j<ListEn.size();j++)
				{
					if(ListSt.get(i).getStolinid() == ListEn.get(j).getStolinid())
					{
						for(int h=0;h<byqall.size();h++)
						{
							if(byqall.get(h).getByqlid() == ListEn.get(j).getStolinid())
							{
								byqdata.put(byqall.get(h).getByqname(),(ListEn.get(j).getNenghao() - ListSt.get(i).getNenghao() ));
							}
						}
					}
				}		
			}
			
			List<Map.Entry<String,Float>> list = new ArrayList<Map.Entry<String,Float>>(byqdata.entrySet());
			Collections.sort( list, new Comparator<Map.Entry<String, Float>>()  
	        {  
	            public int compare( Map.Entry<String, Float> o1, Map.Entry<String, Float> o2 )  
	            {  
	                return (o2.getValue()).compareTo( o1.getValue() );  
	            }  
	        } );  
			 Map<String,Float> result = new LinkedHashMap<String,Float>();  
		        for (Map.Entry<String, Float> entry : list)  
		        {  
		            result.put( entry.getKey(), entry.getValue() );  
		        }  
		        
			return result;
			
			//return byqdata;
		}
		else
		{
			//获取同站点下单变压器数据
			Map<String, java.util.List<device_rank>> byqsigname = Device_rankService.Get_byqname_bybyqid(params);
			//单变压器返回map集
			Map<String ,Float> byqsigdata = new HashMap<String ,Float>();
			java.util.List<device_rank> ListSt = rankList.get("result");
			java.util.List<device_rank> ListEn = rankListEnd.get("result");
			java.util.List<device_rank> byqsigall = byqsigname.get("result");
			for(int i=0; i<ListSt.size();i++)
			{
				for(int j=0; j<ListEn.size();j++)
				{
					if(ListSt.get(i).getStolinid() == ListEn.get(j).getStolinid())
					{
						for(int h=0;h<byqsigall.size();h++)
						{
							if(byqsigall.get(h).getByqlid() == ListEn.get(j).getStolinid())
							{
								byqsigdata.put(byqsigall.get(h).getByqname(),(ListEn.get(j).getNenghao() - ListSt.get(i).getNenghao() ));
							}
						}
					}
				}		
			}
			
			List<Map.Entry<String,Float>> list = new ArrayList<Map.Entry<String,Float>>(byqsigdata.entrySet());
			Collections.sort( list, new Comparator<Map.Entry<String, Float>>()  
	        {  
	            public int compare( Map.Entry<String, Float> o1, Map.Entry<String, Float> o2 )  
	            {  
	                return (o2.getValue()).compareTo( o1.getValue() );  
	            }  
	        } );  
			 Map<String,Float> result = new LinkedHashMap<String,Float>();  
		        for (Map.Entry<String, Float> entry : list)  
		        {  
		            result.put( entry.getKey(), entry.getValue() );  
		        }  
		        
			return result;
			//return byqsigdata;
		}
				
	}
	
	
	@RequestMapping("/effrank/device_rank/getbanzudata2")
	@ResponseBody
	//班组排名（点击变压器查询更改为变压器下所有回路数据）	
	//Map<String ,Float>
	private Map<String ,Float> getbanzudata2(HttpServletRequest request) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("stostationid",request.getParameter("stationid"));
		params.put("byqid",request.getParameter("stabyqid"));
		params.put("starttime",request.getParameter("start")+"%");
		params.put("endtime",request.getParameter("end")+"%");
		
		//获取全部回路的起始值
		Map<String, java.util.List<device_rank>> rankList = Device_rankService.Get_devicerank(params);	
		//获取全部回路的结束值
		Map<String, java.util.List<device_rank>> rankListEnd = Device_rankService.Get_devicerankEnd(params);
		
		if(request.getParameter("stabyqid") == null)
		{
			//获取同站点下所有变压器数据
			Map<String, java.util.List<device_rank>> byqname = Device_rankService.Get_byqname(params);
			//多变压器返回map集
			Map<String ,Float> byqdata = new HashMap<String ,Float>();
			java.util.List<device_rank> ListSt = rankList.get("result");
			java.util.List<device_rank> ListEn = rankListEnd.get("result");
			java.util.List<device_rank> byqall = byqname.get("result");
			for(int i=0; i<ListSt.size();i++)
			{
				for(int j=0; j<ListEn.size();j++)
				{
					if(ListSt.get(i).getStolinid() == ListEn.get(j).getStolinid())
					{
						for(int h=0;h<byqall.size();h++)
						{
							if(byqall.get(h).getByqlid() == ListEn.get(j).getStolinid())
							{
								byqdata.put(byqall.get(h).getByqname(),(ListEn.get(j).getNenghao() - ListSt.get(i).getNenghao() ));
							}
						}
					}
				}		
			}
			
			List<Map.Entry<String,Float>> list = new ArrayList<Map.Entry<String,Float>>(byqdata.entrySet());
			Collections.sort( list, new Comparator<Map.Entry<String, Float>>()  
	        {  
	            public int compare( Map.Entry<String, Float> o1, Map.Entry<String, Float> o2 )  
	            {  
	                return (o2.getValue()).compareTo( o1.getValue() );  
	            }  
	        } );  
			 Map<String,Float> result = new LinkedHashMap<String,Float>();  
		        for (Map.Entry<String, Float> entry : list)  
		        {  
		            result.put( entry.getKey(), entry.getValue() );  
		        }  
		        
			return result;
			
			//return byqdata;
		}
		else
		{
			//获取同站点下单变压器下所有回路数据
			Map<String, java.util.List<device_rank>> linesname = Device_rankService.get_linesname_bybyqid(params);
			//多回路返回map集
			Map<String ,Float> linesdata = new HashMap<String ,Float>();
			java.util.List<device_rank> ListSt = rankList.get("result");
			java.util.List<device_rank> ListEn = rankListEnd.get("result");
			java.util.List<device_rank> linesall = linesname.get("result");
			for(int i=0; i<ListSt.size();i++)
			{
				for(int j=0; j<ListEn.size();j++)
				{
					if(ListSt.get(i).getStolinid() == ListEn.get(j).getStolinid())
					{
						for(int h=0;h<linesall.size();h++)
						{
							if(linesall.get(h).getDclid() == ListEn.get(j).getStolinid())
							{
								linesdata.put(linesall.get(h).getLinename(),(ListEn.get(j).getNenghao() - ListSt.get(i).getNenghao() ));
							}
						}
					}
				}		
			}
			
			List<Map.Entry<String,Float>> list = new ArrayList<Map.Entry<String,Float>>(linesdata.entrySet());
			Collections.sort( list, new Comparator<Map.Entry<String, Float>>()  
	        {  
	            public int compare( Map.Entry<String, Float> o1, Map.Entry<String, Float> o2 )  
	            {  
	                return (o2.getValue()).compareTo( o1.getValue() );  
	            }  
	        } );  
			 Map<String,Float> result = new LinkedHashMap<String,Float>();  
		        for (Map.Entry<String, Float> entry : list)  
		        {  
		            result.put( entry.getKey(), entry.getValue() );  
		        }  
		        
			return result;
			//return byqsigdata;
		}
				
	}
}
