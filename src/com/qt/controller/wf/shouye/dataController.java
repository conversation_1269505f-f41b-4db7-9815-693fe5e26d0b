package com.qt.controller.wf.shouye;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.qt.controller.base.BaseController;
import com.qt.entity.wf.ShouyeData;
import com.qt.service.statistics.wf.ShouyeDataService;

/*
 * 首页数据
 */
@Controller
@RequestMapping("/headdata")
public class dataController extends BaseController {
	
private Logger logger = LoggerFactory.getLogger(shouyeController.class);
	
    @Resource
	private ShouyeDataService ShouyeDataService;
    
    @RequestMapping("/CO2")
	@ResponseBody
	private Map<String ,Float> getCo2(HttpServletRequest request) throws ParseException {
    	String starttime = "";
    	
    	String fqd1 = ""; //峰时起点1
    	String fzd1 = ""; //峰时终点1
    	String fqd2 = ""; //峰时起点2
    	String fzd2 = ""; //峰时终点2
    	
    	String gqd1 = ""; //谷时起点1
    	String gzd1 = ""; //谷时终点1
    	String gqd2 = ""; //谷时起点2
    	String gzd2 = ""; //谷时终点2
    	
    	String pqd1 = ""; //平时起点1
    	String pzd1 = ""; //平时终点1
    	String pqd2 = ""; //平时起点2
    	String pzd2 = ""; //平时终点2
    	
    	String jqd1 = ""; //尖时起点1
    	String jzd1 = ""; //尖时终点1
    	String jqd2 = ""; //尖时起点2
    	String jzd2 = ""; //尖时终点2
    	
    	Map<String, Object> params = new HashMap<String, Object>();
		params.put("stostationid",request.getParameter("stationid"));
		starttime = request.getParameter("timenow");
		String syrq = gethuanbimonth(starttime);//上月1号日期
		String byyh = getbyyh(syrq);
		String bylast = getbylast(syrq);
//		if((syrq.split("-")[1]+"-"+syrq.split("-")[2]).equals("02-29"))
//		{
//			byyh = syrq.split("-")[0]+"-"+"02-01";
//			bylast = syrq.split("-")[0]+"-"+"02-28";
//		}
		String amonth[] = getamonth(byyh, bylast);
		System.out.println("syrq:"+syrq+" "+byyh+" "+bylast);
		Map<String, java.util.List<ShouyeData>> fgpj = ShouyeDataService.Get_fgsjtime(params); //峰谷平尖
		java.util.List<ShouyeData> Listfgpj = fgpj.get("result");
		for(int i=0;i<Listfgpj.size();i++)
		{
			fqd1 = Listfgpj.get(i).getFqd1();
			fzd1 = Listfgpj.get(i).getFzd1();
			fzd1 = getrq(fzd1);
			fqd2 = Listfgpj.get(i).getFqd2();
			fzd2 = Listfgpj.get(i).getFzd2();
			fzd2 = getrq(fzd2);
			
			gqd1 = Listfgpj.get(i).getGqd1();
			gzd1 = Listfgpj.get(i).getGzd1();
			gqd2 = Listfgpj.get(i).getGqd2();
			gzd2 = Listfgpj.get(i).getGzd2();
			gzd1 = getrq(gzd1);
			gzd2 = getrq(gzd2);
			
			pqd1 = Listfgpj.get(i).getPqd1();
			pzd1 = Listfgpj.get(i).getPzd1();
			pqd2 = Listfgpj.get(i).getPqd2();
			pzd2 = Listfgpj.get(i).getPzd2();
			pzd1 = getrq(pzd1);
			pzd2 = getrq(pzd2);
			
			jqd1 = Listfgpj.get(i).getJqd1();
			jzd1 = Listfgpj.get(i).getJzd1();
			jqd2 = Listfgpj.get(i).getJqd2();
			jzd2 = Listfgpj.get(i).getJzd2();
			jzd1 = getrq(jzd1);
			jzd2 = getrq(jzd2);
			
		}
		float yueydl = 0;
		//峰谷平尖用点量
		float yfdl = 0;
		float ygdl = 0;
		float ypdl = 0;
		float yjdl = 0;
		for(int i = 0;i<amonth.length;i++)
		{
			float rydlf = 0;
			params.put("chose_starttime", amonth[i]+" 00:00:00");
			params.put("chose_endtime", amonth[i]+" 23:45:59");
			Map<String, java.util.List<ShouyeData>> rydl = ShouyeDataService.Get_syydl(params);
			java.util.List<ShouyeData> Listry = rydl.get("result");
			if(Listry.size()>0)
			{
				rydlf = Listry.get(Listry.size()-1).getSyydl1() - Listry.get(0).getSyydl1();
				yueydl = yueydl + rydlf;
			}
			
			//峰1
			if(fqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rf1ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+fqd1+":00");
				params.put("shiduan_endtime", amonth[i]+" "+fzd1+":59");
				Map<String, java.util.List<ShouyeData>> f1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listf1 = f1.get("result");
				if(Listf1.size()>0)
				{
					rf1ydl = Listf1.get(Listf1.size()-1).getSyydl() - Listf1.get(0).getSyydl();
					yfdl = yfdl + rf1ydl;
				}
			}
			//峰2
			if(fqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rf2ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+fqd2+":00");
				params.put("shiduan_endtime", amonth[i]+" "+fzd2+":59");
				Map<String, java.util.List<ShouyeData>> f2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listf2 = f2.get("result");
				if(Listf2.size()>0)
				{
					rf2ydl = Listf2.get(Listf2.size()-1).getSyydl() - Listf2.get(0).getSyydl();
					yfdl = yfdl + rf2ydl;
				}
			}
			//谷1
			if(gqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rg1ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+gqd1+":00");
				params.put("shiduan_endtime", amonth[i]+" "+gzd1+":59");
				Map<String, java.util.List<ShouyeData>> g1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listg1 = g1.get("result");
				if(Listg1.size()>0)
				{
					rg1ydl = Listg1.get(Listg1.size()-1).getSyydl() - Listg1.get(0).getSyydl();
					ygdl = ygdl + rg1ydl;
				}
			}
			//谷2
			if(gqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rg2ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+gqd2+":00");
				params.put("shiduan_endtime", amonth[i]+" "+gzd2+":59");
				Map<String, java.util.List<ShouyeData>> g2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listg2 = g2.get("result");
				if(Listg2.size()>0)
				{
					rg2ydl = Listg2.get(Listg2.size()-1).getSyydl() - Listg2.get(0).getSyydl();
					ygdl = ygdl + rg2ydl;
				}
			}
			//平1
			if(pqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rp1ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+pqd1+":00");
				params.put("shiduan_endtime", amonth[i]+" "+pzd1+":59");
				Map<String, java.util.List<ShouyeData>> p1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listp1 = p1.get("result");
				if(Listp1.size()>0)
				{
					rp1ydl = Listp1.get(Listp1.size()-1).getSyydl() - Listp1.get(0).getSyydl();
					ypdl = ypdl + rp1ydl;
				}
			}
			//平2
			if(pqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rp2ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+pqd2+":00");
				params.put("shiduan_endtime", amonth[i]+" "+pzd2+":59");
				Map<String, java.util.List<ShouyeData>> p2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listp2 = p2.get("result");
				if(Listp2.size()>0)
				{
					rp2ydl = Listp2.get(Listp2.size()-1).getSyydl() - Listp2.get(0).getSyydl();
					ypdl = ypdl + rp2ydl;
				}
			}
			//尖1
			if(jqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rj1ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+jqd1+":00");
				params.put("shiduan_endtime", amonth[i]+" "+jzd1+":59");
				Map<String, java.util.List<ShouyeData>> j1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listj1 = j1.get("result");
				if(Listj1.size()>0)
				{
					rj1ydl = Listj1.get(Listj1.size()-1).getSyydl() - Listj1.get(0).getSyydl();
					yjdl = yjdl + rj1ydl;
				}
			}
			//尖2
			if(jqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rj2ydl = 0;
				params.put("shiduan_starttime", amonth[i]+" "+jqd2+":00");
				params.put("shiduan_endtime", amonth[i]+" "+jzd2+":59");
				Map<String, java.util.List<ShouyeData>> j2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listj2 = j2.get("result");
				if(Listj2.size()>0)
				{
					rj2ydl = Listj2.get(Listj2.size()-1).getSyydl() - Listj2.get(0).getSyydl();
					yjdl = yjdl + rj2ydl;
				}
			}
			System.out.println("yfdl:"+yfdl);
			System.out.println("ygdl:"+ygdl);
			System.out.println("ypdl:"+ypdl);
			System.out.println("yjdl:"+yjdl);
		}
		Map<String , Float> data = new TreeMap<String , Float>();
		float co2 = (float) (yueydl * 0.997)/1000;
		float bzm = (float) ((yueydl * 0.36)/1000);
		data.put("syydl", yueydl);
		data.put("CO2", co2);
		data.put("BZM", bzm);
		data.put("YF", yfdl);
		data.put("YG", ygdl);
		data.put("YP", ypdl);
		data.put("YJ", yjdl);
		String testtime[] = syrq.split("-");
		data.put("Month",Float.parseFloat(testtime[1]));
    	return data;
    }
    
    @RequestMapping("/BY_SY_DJ")
	@ResponseBody
	private Map<String ,Float> getBYSYDJ(HttpServletRequest request) throws ParseException {
    	
    	String starttime = "";
    	starttime = request.getParameter("timenow");
    	Map<String, Object> params = new HashMap<String, Object>();
		params.put("stostationid",request.getParameter("stationid"));
		Map<String, java.util.List<ShouyeData>> fgpj = ShouyeDataService.Get_fgsjtime(params); //峰谷平尖
		java.util.List<ShouyeData> Listfgpj = fgpj.get("result");
		Map<String , Float> data2 = new TreeMap<String , Float>();
		//峰谷平尖电价
		float fdj = 0;
		float gdj = 0;
		float pdj = 0;
		float jdj = 0;
		
		String fqd1 = ""; //峰时起点1
    	String fzd1 = ""; //峰时终点1
    	String fqd2 = ""; //峰时起点2
    	String fzd2 = ""; //峰时终点2
    	
    	String gqd1 = ""; //谷时起点1
    	String gzd1 = ""; //谷时终点1
    	String gqd2 = ""; //谷时起点2
    	String gzd2 = ""; //谷时终点2
    	
    	String pqd1 = ""; //平时起点1
    	String pzd1 = ""; //平时终点1
    	String pqd2 = ""; //平时起点2
    	String pzd2 = ""; //平时终点2
    	
    	String jqd1 = ""; //尖时起点1
    	String jzd1 = ""; //尖时终点1
    	String jqd2 = ""; //尖时起点2
    	String jzd2 = ""; //尖时终点2
		
		for(int i=0;i<Listfgpj.size();i++)
		{
			fqd1 = Listfgpj.get(i).getFqd1();
			fzd1 = Listfgpj.get(i).getFzd1();
			fzd1 = getrq(fzd1);
			fqd2 = Listfgpj.get(i).getFqd2();
			fzd2 = Listfgpj.get(i).getFzd2();
			fzd2 = getrq(fzd2);
			
			gqd1 = Listfgpj.get(i).getGqd1();
			gzd1 = Listfgpj.get(i).getGzd1();
			gqd2 = Listfgpj.get(i).getGqd2();
			gzd2 = Listfgpj.get(i).getGzd2();
			gzd1 = getrq(gzd1);
			gzd2 = getrq(gzd2);
			
			pqd1 = Listfgpj.get(i).getPqd1();
			pzd1 = Listfgpj.get(i).getPzd1();
			pqd2 = Listfgpj.get(i).getPqd2();
			pzd2 = Listfgpj.get(i).getPzd2();
			pzd1 = getrq(pzd1);
			pzd2 = getrq(pzd2);
			
			jqd1 = Listfgpj.get(i).getJqd1();
			jzd1 = Listfgpj.get(i).getJzd1();
			jqd2 = Listfgpj.get(i).getJqd2();
			jzd2 = Listfgpj.get(i).getJzd2();
			jzd1 = getrq(jzd1);
			jzd2 = getrq(jzd2);
			
			fdj = Listfgpj.get(i).getFdj();
			gdj = Listfgpj.get(i).getGdj();
			pdj = Listfgpj.get(i).getPdj();
			jdj = Listfgpj.get(i).getJdj();
		}
		
		//峰谷平尖用点量
		float yfdl = 0;
		float ygdl = 0;
		float ypdl = 0;
		float yjdl = 0;
		String tonowday[] = getbynowday(starttime);
		for(int i=0;i<tonowday.length;i++)
		{
			float rz = 0;
			float rfdl = 0;
			float rgdl = 0;
			float rpdl = 0;
			float rjdl = 0;
			//峰1
			if(fqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rf1ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+fqd1+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+fzd1+":59");
				Map<String, java.util.List<ShouyeData>> f1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listf1 = f1.get("result");
				if(Listf1.size()>0)
				{
					rf1ydl = Listf1.get(Listf1.size()-1).getSyydl() - Listf1.get(0).getSyydl();
					yfdl = yfdl + rf1ydl;
					rfdl = rfdl + rf1ydl;
				}
			}
			//峰2
			if(fqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rf2ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+fqd2+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+fzd2+":59");
				Map<String, java.util.List<ShouyeData>> f2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listf2 = f2.get("result");
				if(Listf2.size()>0)
				{
					rf2ydl = Listf2.get(Listf2.size()-1).getSyydl() - Listf2.get(0).getSyydl();
					yfdl = yfdl + rf2ydl;
					rfdl = rfdl + rf2ydl;
				}
			}
			//谷1
			if(gqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rg1ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+gqd1+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+gzd1+":59");
				Map<String, java.util.List<ShouyeData>> g1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listg1 = g1.get("result");
				if(Listg1.size()>0)
				{
					rg1ydl = Listg1.get(Listg1.size()-1).getSyydl() - Listg1.get(0).getSyydl();
					ygdl = ygdl + rg1ydl;
					rgdl = rgdl + rg1ydl;
				}
			}
			//谷2
			if(gqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rg2ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+gqd2+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+gzd2+":59");
				Map<String, java.util.List<ShouyeData>> g2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listg2 = g2.get("result");
				if(Listg2.size()>0)
				{
					rg2ydl = Listg2.get(Listg2.size()-1).getSyydl() - Listg2.get(0).getSyydl();
					ygdl = ygdl + rg2ydl;
					rgdl = rgdl + rg2ydl;
				}
			}
			//平1
			if(pqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rp1ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+pqd1+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+pzd1+":59");
				Map<String, java.util.List<ShouyeData>> p1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listp1 = p1.get("result");
				if(Listp1.size()>0)
				{
					rp1ydl = Listp1.get(Listp1.size()-1).getSyydl() - Listp1.get(0).getSyydl();
					ypdl = ypdl + rp1ydl;
					rpdl = rpdl + rp1ydl;
				}
			}
			//平2
			if(pqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rp2ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+pqd2+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+pzd2+":59");
				Map<String, java.util.List<ShouyeData>> p2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listp2 = p2.get("result");
				if(Listp2.size()>0)
				{
					rp2ydl = Listp2.get(Listp2.size()-1).getSyydl() - Listp2.get(0).getSyydl();
					ypdl = ypdl + rp2ydl;
					rpdl = rpdl + rp2ydl;
				}
			}
			//尖1
			if(jqd1.equals(""))
			{
				//不做处理
			}
			else
			{
				float rj1ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+jqd1+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+jzd1+":59");
				Map<String, java.util.List<ShouyeData>> j1 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listj1 = j1.get("result");
				if(Listj1.size()>0)
				{
					rj1ydl = Listj1.get(Listj1.size()-1).getSyydl() - Listj1.get(0).getSyydl();
					yjdl = yjdl + rj1ydl;
					rjdl = rjdl + rj1ydl;
				}
			}
			//尖2
			if(jqd2.equals(""))
			{
				//不做处理
			}
			else
			{
				float rj2ydl = 0;
				params.put("shiduan_starttime", tonowday[i]+" "+jqd2+":00");
				params.put("shiduan_endtime", tonowday[i]+" "+jzd2+":59");
				Map<String, java.util.List<ShouyeData>> j2 = ShouyeDataService.Get_fgsjydl(params);
				java.util.List<ShouyeData> Listj2 = j2.get("result");
				if(Listj2.size()>0)
				{
					rj2ydl = Listj2.get(Listj2.size()-1).getSyydl() - Listj2.get(0).getSyydl();
					yjdl = yjdl + rj2ydl;
					rjdl = rjdl + rj2ydl;
				}
			}
			
			rz = rfdl + rgdl + rpdl +rjdl;
			if(i+7>=tonowday.length)
			{
				data2.put(tonowday[i]+","+rfdl+","+rgdl+","+rpdl+","+rjdl, rz);
			}
			else
			{
				data2.put(tonowday[i], rz);
			}
		}
		String j30[] = get30(starttime);
		int cha = j30.length - tonowday.length;
		if(cha > 0)
		{
			int hcha = 7 - tonowday.length;
			for(int i=tonowday.length;i<j30.length;i++)
			{
				float rz = 0;
				float rfdl = 0;
				float rgdl = 0;
				float rpdl = 0;
				float rjdl = 0;
				//峰1
				if(fqd1.equals(""))
				{
					//不做处理
				}
				else
				{
					float rf1ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+fqd1+":00");
					params.put("shiduan_endtime", j30[i]+" "+fzd1+":59");
					Map<String, java.util.List<ShouyeData>> f1 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listf1 = f1.get("result");
					if(Listf1.size()>0)
					{
						rf1ydl = Listf1.get(Listf1.size()-1).getSyydl() - Listf1.get(0).getSyydl();
						//yfdl = yfdl + rf1ydl;
						rfdl = rfdl + rf1ydl;
					}
				}
				//峰2
				if(fqd2.equals(""))
				{
					//不做处理
				}
				else
				{
					float rf2ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+fqd2+":00");
					params.put("shiduan_endtime", j30[i]+" "+fzd2+":59");
					Map<String, java.util.List<ShouyeData>> f2 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listf2 = f2.get("result");
					if(Listf2.size()>0)
					{
						rf2ydl = Listf2.get(Listf2.size()-1).getSyydl() - Listf2.get(0).getSyydl();
						//yfdl = yfdl + rf2ydl;
						rfdl = rfdl + rf2ydl;
					}
				}
				//谷1
				if(gqd1.equals(""))
				{
					//不做处理
				}
				else
				{
					float rg1ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+gqd1+":00");
					params.put("shiduan_endtime", j30[i]+" "+gzd1+":59");
					Map<String, java.util.List<ShouyeData>> g1 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listg1 = g1.get("result");
					if(Listg1.size()>0)
					{
						rg1ydl = Listg1.get(Listg1.size()-1).getSyydl() - Listg1.get(0).getSyydl();
						//ygdl = ygdl + rg1ydl;
						rgdl = rgdl + rg1ydl;
					}
				}
				//谷2
				if(gqd2.equals(""))
				{
					//不做处理
				}
				else
				{
					float rg2ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+gqd2+":00");
					params.put("shiduan_endtime", j30[i]+" "+gzd2+":59");
					Map<String, java.util.List<ShouyeData>> g2 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listg2 = g2.get("result");
					if(Listg2.size()>0)
					{
						rg2ydl = Listg2.get(Listg2.size()-1).getSyydl() - Listg2.get(0).getSyydl();
						//ygdl = ygdl + rg2ydl;
						rgdl = rgdl + rg2ydl;
					}
				}
				//平1
				if(pqd1.equals(""))
				{
					//不做处理
				}
				else
				{
					float rp1ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+pqd1+":00");
					params.put("shiduan_endtime", j30[i]+" "+pzd1+":59");
					Map<String, java.util.List<ShouyeData>> p1 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listp1 = p1.get("result");
					if(Listp1.size()>0)
					{
						rp1ydl = Listp1.get(Listp1.size()-1).getSyydl() - Listp1.get(0).getSyydl();
						//ypdl = ypdl + rp1ydl;
						rpdl = rpdl + rp1ydl;
					}
				}
				//平2
				if(pqd2.equals(""))
				{
					//不做处理
				}
				else
				{
					float rp2ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+pqd2+":00");
					params.put("shiduan_endtime", j30[i]+" "+pzd2+":59");
					Map<String, java.util.List<ShouyeData>> p2 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listp2 = p2.get("result");
					if(Listp2.size()>0)
					{
						rp2ydl = Listp2.get(Listp2.size()-1).getSyydl() - Listp2.get(0).getSyydl();
						//ypdl = ypdl + rp2ydl;
						rpdl = rpdl + rp2ydl;
					}
				}
				//尖1
				if(jqd1.equals(""))
				{
					//不做处理
				}
				else
				{
					float rj1ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+jqd1+":00");
					params.put("shiduan_endtime", j30[i]+" "+jzd1+":59");
					Map<String, java.util.List<ShouyeData>> j1 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listj1 = j1.get("result");
					if(Listj1.size()>0)
					{
						rj1ydl = Listj1.get(Listj1.size()-1).getSyydl() - Listj1.get(0).getSyydl();
						//yjdl = yjdl + rj1ydl;
						rjdl = rjdl + rj1ydl;
					}
				}
				//尖2
				if(jqd2.equals(""))
				{
					//不做处理
				}
				else
				{
					float rj2ydl = 0;
					params.put("shiduan_starttime", j30[i]+" "+jqd2+":00");
					params.put("shiduan_endtime", j30[i]+" "+jzd2+":59");
					Map<String, java.util.List<ShouyeData>> j2 = ShouyeDataService.Get_fgsjydl(params);
					java.util.List<ShouyeData> Listj2 = j2.get("result");
					if(Listj2.size()>0)
					{
						rj2ydl = Listj2.get(Listj2.size()-1).getSyydl() - Listj2.get(0).getSyydl();
						//yjdl = yjdl + rj2ydl;
						rjdl = rjdl + rj2ydl;
					}
				}
				
				rz = rfdl + rgdl + rpdl +rjdl;
				if(hcha > 0)
				{
					data2.put(j30[i]+","+rfdl+","+rgdl+","+rpdl+","+rjdl, rz);
					hcha--;
				}
				else
				{
					data2.put(j30[i], rz);
				}
			}
		}
		
		
		data2.put("TONOWYF", yfdl);
		data2.put("TONOWYG", ygdl);
		data2.put("TONOWYP", ypdl);
		data2.put("TONOWYJ", yjdl);
		data2.put("FDJ", fdj);
		data2.put("GDJ", gdj);
		data2.put("PDJ", pdj);
		data2.put("JDJ", jdj);
    	return data2;
    }
    
    private String[] getbynowday(String start){
    	String stesttime[] = start.split("-");
    	String tonowday[] = new String[Integer.parseInt(stesttime[2])];
    	for(int i=0;i<Integer.parseInt(stesttime[2]);i++)
    	{
    		int day = i+1;
    		String returnday = "";
    		if(day>=1 && day<=9)
    		{
    			returnday = "0"+day;
    		}
    		else
    		{
    			returnday = ""+day;
    		}
    		tonowday[i] = stesttime[0] + "-" + stesttime[1] + "-" + returnday;
    	}
    	return tonowday;
    }
    
    private String getrq(String test){
    	String testtime[] = test.split(":");
    	if(testtime[0].equals("23"))
    	{
    		return testtime[0]+":"+testtime[1];
    	}
    	else
    	{
    		return testtime[0]+":14";
    	}
    }
    
    private String gethuanbimonth(String test){
		String testtime[] = test.split("-");
		int huanbimonth = Integer.parseInt(testtime[1]) - 1;
		if(huanbimonth == 0) huanbimonth = 12;
		if(huanbimonth >=1 && huanbimonth <=9 )
		{
			return testtime[0] + "-0" + huanbimonth + "-" + "01";
		}
		else
		{
			return testtime[0] + "-" + huanbimonth + "-" + "01";
		}
	}
    
    private String getbyyh(String starttime) throws ParseException{
		SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
		Calendar cal = Calendar.getInstance();
		Date date = formatter.parse(starttime);
        cal.setTime(date);
        cal.add(Calendar.MONTH, 0);
		cal.set(Calendar.DAY_OF_MONTH,1);
		Date d = cal.getTime();
		return formatter.format(d);
	}
	
	private String getbylast(String starttime) throws ParseException{
		SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
		Calendar cal = Calendar.getInstance();
		Date date = formatter.parse(starttime);
        cal.setTime(date);
        cal.add(Calendar.MONTH, 1);
		cal.set(Calendar.DAY_OF_MONTH,0);  
		Date d = cal.getTime();
		return formatter.format(d);
	}
	private String[] getamonth(String starttime,String endtime) {
		String testendtime[] = endtime.split("-");
		int sum = Integer.parseInt(testendtime[2]);
		String month[] = new String[sum];
		String testtime[] = starttime.split("-");
		for(int i=0;i<month.length;i++)
		{
			int day = Integer.parseInt(testtime[2])+i;
			String returnday = "";
			if(day >= 1 && day <= 9)
			{
				returnday = "0"+day;
			}
			else
			{
				returnday = ""+day;
			}
			month[i] = testtime[0] + "-" + testtime[1] + "-" + returnday;
		}
		return month;
	}
	
	private String[] get7(String starttime)
	{
		String testtime[] = starttime.split("-");
		int year = Integer.parseInt(testtime[0]);
		int month = Integer.parseInt(testtime[1]);
		int day = Integer.parseInt(testtime[2]);
		String j7[] = new String[7];
		if(day < 7)
		{
			int sydaynum = daynum(month-1,year);
			int symonth = month-1;
			String symonth1 = "";
			int newday=0;
			if(symonth>=1&&symonth<=9)
			{
				symonth1 = "0"+symonth;
			}
			else
			{
				symonth1=""+symonth;
			}
			newday = sydaynum;
			for(int i =0;i<j7.length;i++)
			{
				if(day <=0 )
				{
					j7[i] = year +"-"+ symonth1 +"-"+newday;
					newday--;
				}
				else if(day <=9)
				{
					j7[i] = year +"-"+ testtime[1] +"-0"+day;
					day--;
				}
				else if(day >=10)
				{
					j7[i] =	year +"-"+ testtime[1] +"-"+day;
					day--;
				}
			}
		}
		else if(day >= 7)
		{
			for(int i =0;i<j7.length;i++)
			{
				if(day <=9)
				{
					j7[i] = year +"-"+ testtime[1] +"-0"+day;
					day--;
				}
				else if(day >=10)
				{
					j7[i] =	year +"-"+ testtime[1] +"-"+day;
					day--;
				}
			}
		}
		
		return j7;
	}
	
	private String[] get30(String starttime)
	{
		String testtime[] = starttime.split("-");
		int year = Integer.parseInt(testtime[0]);
		int month = Integer.parseInt(testtime[1]);
		int day = Integer.parseInt(testtime[2]);
		int symonth = month-1;
		String symonth1 = "";
		if(symonth>=1&&symonth<=9)
		{
			symonth1 = "0"+symonth;
		}
		String j30[] = new String[30];
		int newday = daynum(month-1,year);
		int newmonth = month-1;
		String newmonth1 ="";
		for(int i=0;i<j30.length;i++)
		{
			
			if(day>=1 && day<10)
			{
				j30[i] = testtime[0]+"-"+testtime[1]+"-0"+day;
				day--;
			}
			else if(day == 0)
			{
				if(newmonth>=1 &&newmonth<10)
				{
					newmonth1 = "0"+newmonth;
				}
				else
				{
					newmonth1 = ""+newmonth;
				}
				if(newday>=1 && newday<10)
				{
					j30[i] = testtime[0]+"-"+newmonth1+"-0"+newday;
					newday--;
				}
				else
				{
					j30[i] = testtime[0]+"-"+newmonth1+"-"+newday;
					newday--;
				}
			}
			else
			{
				j30[i] = testtime[0]+"-"+testtime[1]+"-"+day;
				day--;
			}
		}
		return j30;
	}
	
	private boolean isrunnian(int year) {
		
        if(year%4==0&&year%100!=0||year%400==0) {
        return true;
        }else{
        return false;
        }
	}
	
	private int daynum(int i,int year){
		if((i == 1) ||(i == 3)||(i == 5)||(i == 7)||(i==8)||(i==10)||(i == 12))
		{
			return 31;
		}
		else if((i==4)||(i==6)||(i==9)||(i==11))
		{
			return 30;
		}
		else
		{
			if(isrunnian(year))
				return 29;
			else
				return 28;
		}
	}
}
