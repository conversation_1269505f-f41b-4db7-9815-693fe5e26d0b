package com.qt.entity.file;

import java.util.Date;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2018-04-24 11:42:37
*/
public class AppOrder{

    // Fields

    private Integer orderId;
    private Integer dcsId;
    private String description;
    private Date createTime;
    private Integer createby;
    private Date updateTime;
    private Integer updateby;
    private Integer orderType;
    private Integer orderStatus;
    private Integer using;
    private Integer handler;
    private Integer fromId;
    private Date acceptTime;
    
    
    private String dcsName;   //站点名称
    private String createName;	//派单人员名称
    private String handleName;	//维修人名称
    
    private String start_time;
    
    private String end_time;
    
    
    
    // Constructors

    public String getStart_time() {
		return start_time;
	}

	public void setStart_time(String start_time) {
		this.start_time = start_time;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public String getHandleName() {
		return handleName;
	}

	public void setHandleName(String handleName) {
		this.handleName = handleName;
	}

	/** default constructor */
    public AppOrder() {
    }

    // Property accessors

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderId() {
        return this.orderId;
    }

    public void setDcsId(Integer dcsId) {
        this.dcsId = dcsId;
    }

    public Integer getDcsId() {
        return this.dcsId;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return this.description;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateby(Integer createby) {
        this.createby = createby;
    }

    public Integer getCreateby() {
        return this.createby;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateby(Integer updateby) {
        this.updateby = updateby;
    }

    public Integer getUpdateby() {
        return this.updateby;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderType() {
        return this.orderType;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderStatus() {
        return this.orderStatus;
    }

    public void setUsing(Integer using) {
        this.using = using;
    }

    public Integer getUsing() {
        return this.using;
    }

    public void setHandler(Integer handler) {
        this.handler = handler;
    }

    public Integer getHandler() {
        return this.handler;
    }

    public void setFromId(Integer fromId) {
        this.fromId = fromId;
    }

    public Integer getFromId() {
        return this.fromId;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public Date getAcceptTime() {
        return this.acceptTime;
    }

}