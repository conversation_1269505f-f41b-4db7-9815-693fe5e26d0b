package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-06 15:24:26
 */
    @Alias("DcsStation")
	public class DcsStation {
    	private String equname;
		private Integer dcsIndexId;
		private int day;

	public String getEquName() {
		return equName;
	}

	public void setEquName(String equName) {
		this.equName = equName;
	}

	private String equName;

	public String getRY() {
		return RY;
	}

	public void setRY(String RY) {
		this.RY = RY;
	}

	private int moth;
		private int year;
		private String  RY;

	public int getAdianya() {
		return Adianya;
	}

	public void setAdianya(int adianya) {
		Adianya = adianya;
	}

	public String getAya() {
		return Aya;
	}

	public void setAya(String aya) {
		Aya = aya;
	}

	private int count;
		private int Adianya;
		private String Aya;

	public int getBya() {
		return Bya;
	}

	public void setBya(int bya) {
		Bya = bya;
	}

	public int getCya() {
		return Cya;
	}

	public void setCya(int cya) {
		Cya = cya;
	}

	private int Bya;
		private int Cya;
		private String devsnId;
		private  String syntinme;
		private String symcTimeYue;
	private String symcTimeNian;
	private String evrTime;
	private int evrLevel;
	private  String evrDesc;
	private  double pvElec;
	private  double buyElec;
	private  double useElec;
	private  double selfElec;
	private  double devsnid;
	private String dcsId;//站号
	private int StationId;//站号
	private  String stoSyncTime;//智慧运维器，当前时间秒
	private Integer dcsDccId;//
	private String dcsName;//站名(客户名称)
	private Integer dcsLineId;//回路编号
	private String lng;//经度
	private String lat;//维度
	private String icoimg;//站点图标
	private Integer dcsTraNum;//主变台数
	private Integer dcsTraSize;//主变容量
	private String dcsPersonName;//联系人姓名
	private String dcsPersonPhone;//联系电话
	private String dcsUserCharacter;//用户性质
	private String dcsUserType;//用户类别
	private String dcsPowersupplyCompany;//供电单位
	private String dcsCompanyId;//客户营业户号
	private String dcsRegisterDate;//抄表日期
	private String dcsChargeModeXl;//计费方式-需量
	private String dcsChargeModeRl;//计费方式-容量
	private String dcsJlfs;//计量方式
	private String dcsAdress;//用电地址
	private String dcsSshy;//所属行业
	private String dcsPdfAdress;//配电房地址
	private String dcsPdglDepartment;//配电管理部门
	private String dcsPdglDepartmentPhone;//配电管理部门电话
	private String dcsPdglZgld;//主管领导
	private String dcsPdglZgldPhone;//主管领导电话
	private String dcsPdglZgldWechat;//主管领导微信号
	private String dcsPdglYdzg;//用电主管
	private String dcsPdglYdzgPhone;//用电主管电话
	private String dcsPdglYdzgWechat;//用电主管微信号
	private String dcsPdglDgbz;//电工班长
	private String dcsPdglDgbzPhone;//电工班长电话
	private String dcsPdglDgbzWechat;//电工班长微信号
	private String dcsCxbds1;//出线变电所
	private String dcsCxbds2;//出线变电所
	private String dcsPowerNature1;//电源性质（主/备）
	private String dcsPowerNature2;//电源性质（主/备）
	private String dcsGdxlmc1;//供电线路名称
	private String dcsGdxlmc2;//供电线路名称
	private String dcsZx1;//专线/T接
	private String dcsZx2;//专线/T接
	private String dcsGddy1;//供电电压
	private String dcsGddy2;//供电电压
	private String dcsSdrl1;//受电容量
	private String dcsSdrl2;//受电容量
	private String fileUrl;//上传svg图片的路径

	private String dccFullName;//企业名称
	private String dclName;
	private String dcsType;

	private String dcsPhotoUrl;//上传站点图片的路径
	private Float dcsElecPrice;//站点发电电价

	private String dcsLogo; //上传ogo图片途径

	private String dcsProvince; //站点省份
	private String dcsCity; //站点城市
	private String dcsArea; //站点区

	private Integer bindWeatherStationId; //气象站Id
	private String bindWeatherName; //气象站名称

	private String stationId;
	private String counts;
	private String realTimePower;
	private String todayElec;
	private String allElec;
	private String accountId;
	private String isFollow;
	private String isBenchmark;
	private String runState;
	private int equId;

	private double InstallCapacity;
	private String	electrovalencePlan;
	private String stationAddress;

	private  String stoSyncTimeDate;//智慧运维器，当前时间天

	private String stationName;


	public String getSymcTimeYue() {
		return symcTimeYue;
	}

	public void setSymcTimeYue(String symcTimeYue) {
		this.symcTimeYue = symcTimeYue;
	}

	public String getSymcTimeNian() {
		return symcTimeNian;
	}

	public void setSymcTimeNian(String symcTimeNian) {
		this.symcTimeNian = symcTimeNian;
	}


	public String getEquname() {
		return equname;
	}
	public void setEquname(String equname) {
		this.equname = equname;
	}
	public String getEvrTime() {
		return evrTime;
	}
	public void setEvrTime(String evrTime) {
		this.evrTime = evrTime;
	}
	public int getEvrLevel() {
		return evrLevel;
	}

	public void setEvrLevel(int evrLevel) {
		this.evrLevel = evrLevel;
	}

	public String getEvrDesc() {
		return evrDesc;
	}

	public void setEvrDesc(String evrDesc) {
		this.evrDesc = evrDesc;
	}

	public double getPvElec() {
		return pvElec;
	}

	public void setPvElec(double pvElec) {
		this.pvElec = pvElec;
	}

	public double getBuyElec() {
		return buyElec;
	}

	public void setBuyElec(double buyElec) {
		this.buyElec = buyElec;
	}

	public double getUseElec() {
		return useElec;
	}

	public void setUseElec(double useElec) {
		this.useElec = useElec;
	}

	public double getSelfElec() {
		return selfElec;
	}

	public void setSelfElec(double selfElec) {
		this.selfElec = selfElec;
	}

	public double getDevsnid() {
		return devsnid;
	}

	public void setDevsnid(double devsnid) {
		this.devsnid = devsnid;
	}

	public String getSyntinme() {
		return syntinme;
	}

	public void setSyntinme(String syntinme) {
		this.syntinme = syntinme;
	}

	public String getDevsnId() {
		return devsnId;
	}

	public void setDevsnId(String devsnId) {
		this.devsnId = devsnId;
	}

	public int getDay() {
		return day;
	}

	public void setDay(int day) {
		this.day = day;
	}

	public int getMoth() {
		return moth;
	}

	public void setMoth(int moth) {
		this.moth = moth;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public String getStoSyncTime() {
		return stoSyncTime;
	}

	public void setStoSyncTime(String stoSyncTime) {
		this.stoSyncTime = stoSyncTime;
	}



	public String getStoSyncTimeDate() {
		return stoSyncTimeDate;
	}

	public void setStoSyncTimeDate(String stoSyncTimeDate) {
		this.stoSyncTimeDate = stoSyncTimeDate;
	}


	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public double getInstallCapacity() {
		return InstallCapacity;
	}

	public void setInstallCapacity(double installCapacity) {
		InstallCapacity = installCapacity;
	}


	public String getStationAddress() {
		return stationAddress;
	}

	public String getElectrovalencePlan() {
		return electrovalencePlan;
	}

	public void setElectrovalencePlan(String electrovalencePlan) {
		this.electrovalencePlan = electrovalencePlan;
	}

	public void setStationAddress(String stationAddress) {
		this.stationAddress = stationAddress;
	}

	public void setStationId(int stationId) {
		StationId = stationId;
	}

	public int getEquId() {
		return equId;
	}

	public void setEquId(int equId) {
		this.equId = equId;
	}

	public DcsStation(){

		}

		public String getIsBenchmark() {
			return isBenchmark;
		}

		public void setIsBenchmark(String isBenchmark) {
			this.isBenchmark = isBenchmark;
		}

		public String getAccountId() {
			return accountId;
		}

		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

		public String getIsFollow() {
			return isFollow;
		}

		public void setIsFollow(String isFollow) {
			this.isFollow = isFollow;
		}

		public String getStationId() {
			return stationId;
		}

		public void setStationId(String stationId) {
			this.stationId = stationId;
		}

		public String getCounts() {
			return counts;
		}

		public void setCounts(String counts) {
			this.counts = counts;
		}

		public String getRealTimePower() {
			return realTimePower;
		}

		public void setRealTimePower(String realTimePower) {
			this.realTimePower = realTimePower;
		}

		public String getTodayElec() {
			return todayElec;
		}

		public void setTodayElec(String todayElec) {
			this.todayElec = todayElec;
		}

		public String getAllElec() {
			return allElec;
		}

		public void setAllElec(String allElec) {
			this.allElec = allElec;
		}

		public String getDcsPhotoUrl() {
			return dcsPhotoUrl;
		}

		public void setDcsPhotoUrl(String dcsPhotoUrl) {
			this.dcsPhotoUrl = dcsPhotoUrl;
		}

		public Float getDcsElecPrice() {
			return dcsElecPrice;
		}

		public void setDcsElecPrice(Float dcsElecPrice) {
			this.dcsElecPrice = dcsElecPrice;
		}

		public String getDccFullName() {
			return dccFullName;
		}

		public void setDccFullName(String dccFullName) {
			this.dccFullName = dccFullName;
		}

		public Integer getDcsIndexId() {
			return dcsIndexId;
		}

		public void setDcsIndexId(Integer dcsIndexId) {
			this.dcsIndexId = dcsIndexId;
		}

		public String getDcsId() {
			return dcsId;
		}

		public void setDcsId(String dcsId) {
			this.dcsId = dcsId;
		}

		public Integer getDcsDccId() {
			return dcsDccId;
		}

		public void setDcsDccId(Integer dcsDccId) {
			this.dcsDccId = dcsDccId;
		}

		public String getDcsName() {
			return dcsName;
		}

		public void setDcsName(String dcsName) {
			this.dcsName = dcsName;
		}

		public Integer getDcsLineId() {
			return dcsLineId;
		}

		public void setDcsLineId(Integer dcsLineId) {
			this.dcsLineId = dcsLineId;
		}

		public String getLng() {
			return lng;
		}

		public void setLng(String lng) {
			this.lng = lng;
		}

		public String getLat() {
			return lat;
		}

		public void setLat(String lat) {
			this.lat = lat;
		}

		public String getIcoimg() {
			return icoimg;
		}

		public void setIcoimg(String icoimg) {
			this.icoimg = icoimg;
		}

		public Integer getDcsTraNum() {
			return dcsTraNum;
		}

		public void setDcsTraNum(Integer dcsTraNum) {
			this.dcsTraNum = dcsTraNum;
		}

		public Integer getDcsTraSize() {
			return dcsTraSize;
		}

		public void setDcsTraSize(Integer dcsTraSize) {
			this.dcsTraSize = dcsTraSize;
		}

		public String getDcsPersonName() {
			return dcsPersonName;
		}

		public void setDcsPersonName(String dcsPersonName) {
			this.dcsPersonName = dcsPersonName;
		}

		public String getDcsPersonPhone() {
			return dcsPersonPhone;
		}

		public void setDcsPersonPhone(String dcsPersonPhone) {
			this.dcsPersonPhone = dcsPersonPhone;
		}

		public String getDcsUserCharacter() {
			return dcsUserCharacter;
		}

		public void setDcsUserCharacter(String dcsUserCharacter) {
			this.dcsUserCharacter = dcsUserCharacter;
		}

		public String getDcsUserType() {
			return dcsUserType;
		}

		public void setDcsUserType(String dcsUserType) {
			this.dcsUserType = dcsUserType;
		}

		public String getDcsPowersupplyCompany() {
			return dcsPowersupplyCompany;
		}

		public void setDcsPowersupplyCompany(String dcsPowersupplyCompany) {
			this.dcsPowersupplyCompany = dcsPowersupplyCompany;
		}

		public String getDcsCompanyId() {
			return dcsCompanyId;
		}

		public void setDcsCompanyId(String dcsCompanyId) {
			this.dcsCompanyId = dcsCompanyId;
		}

		public String getDcsRegisterDate() {
			return dcsRegisterDate;
		}

		public void setDcsRegisterDate(String dcsRegisterDate) {
			this.dcsRegisterDate = dcsRegisterDate;
		}

		public String getDcsChargeModeXl() {
			return dcsChargeModeXl;
		}

		public void setDcsChargeModeXl(String dcsChargeModeXl) {
			this.dcsChargeModeXl = dcsChargeModeXl;
		}

		public String getDcsChargeModeRl() {
			return dcsChargeModeRl;
		}

		public void setDcsChargeModeRl(String dcsChargeModeRl) {
			this.dcsChargeModeRl = dcsChargeModeRl;
		}

		public String getDcsJlfs() {
			return dcsJlfs;
		}

		public void setDcsJlfs(String dcsJlfs) {
			this.dcsJlfs = dcsJlfs;
		}

		public String getDcsAdress() {
			return dcsAdress;
		}

		public void setDcsAdress(String dcsAdress) {
			this.dcsAdress = dcsAdress;
		}

		public String getDcsSshy() {
			return dcsSshy;
		}

		public void setDcsSshy(String dcsSshy) {
			this.dcsSshy = dcsSshy;
		}

		public String getDcsPdfAdress() {
			return dcsPdfAdress;
		}

		public void setDcsPdfAdress(String dcsPdfAdress) {
			this.dcsPdfAdress = dcsPdfAdress;
		}

		public String getDcsPdglDepartment() {
			return dcsPdglDepartment;
		}

		public void setDcsPdglDepartment(String dcsPdglDepartment) {
			this.dcsPdglDepartment = dcsPdglDepartment;
		}

		public String getDcsPdglDepartmentPhone() {
			return dcsPdglDepartmentPhone;
		}

		public void setDcsPdglDepartmentPhone(String dcsPdglDepartmentPhone) {
			this.dcsPdglDepartmentPhone = dcsPdglDepartmentPhone;
		}

		public String getDcsPdglZgld() {
			return dcsPdglZgld;
		}

		public void setDcsPdglZgld(String dcsPdglZgld) {
			this.dcsPdglZgld = dcsPdglZgld;
		}

		public String getDcsPdglZgldPhone() {
			return dcsPdglZgldPhone;
		}

		public void setDcsPdglZgldPhone(String dcsPdglZgldPhone) {
			this.dcsPdglZgldPhone = dcsPdglZgldPhone;
		}

		public String getDcsPdglZgldWechat() {
			return dcsPdglZgldWechat;
		}

		public void setDcsPdglZgldWechat(String dcsPdglZgldWechat) {
			this.dcsPdglZgldWechat = dcsPdglZgldWechat;
		}

		public String getDcsPdglYdzg() {
			return dcsPdglYdzg;
		}

		public void setDcsPdglYdzg(String dcsPdglYdzg) {
			this.dcsPdglYdzg = dcsPdglYdzg;
		}

		public String getDcsPdglYdzgPhone() {
			return dcsPdglYdzgPhone;
		}

		public void setDcsPdglYdzgPhone(String dcsPdglYdzgPhone) {
			this.dcsPdglYdzgPhone = dcsPdglYdzgPhone;
		}

		public String getDcsPdglYdzgWechat() {
			return dcsPdglYdzgWechat;
		}

		public void setDcsPdglYdzgWechat(String dcsPdglYdzgWechat) {
			this.dcsPdglYdzgWechat = dcsPdglYdzgWechat;
		}

		public String getDcsPdglDgbz() {
			return dcsPdglDgbz;
		}

		public void setDcsPdglDgbz(String dcsPdglDgbz) {
			this.dcsPdglDgbz = dcsPdglDgbz;
		}

		public String getDcsPdglDgbzPhone() {
			return dcsPdglDgbzPhone;
		}

		public void setDcsPdglDgbzPhone(String dcsPdglDgbzPhone) {
			this.dcsPdglDgbzPhone = dcsPdglDgbzPhone;
		}

		public String getDcsPdglDgbzWechat() {
			return dcsPdglDgbzWechat;
		}

		public void setDcsPdglDgbzWechat(String dcsPdglDgbzWechat) {
			this.dcsPdglDgbzWechat = dcsPdglDgbzWechat;
		}

		public String getDcsCxbds1() {
			return dcsCxbds1;
		}

		public void setDcsCxbds1(String dcsCxbds1) {
			this.dcsCxbds1 = dcsCxbds1;
		}

		public String getDcsCxbds2() {
			return dcsCxbds2;
		}

		public void setDcsCxbds2(String dcsCxbds2) {
			this.dcsCxbds2 = dcsCxbds2;
		}

		public String getDcsPowerNature1() {
			return dcsPowerNature1;
		}

		public void setDcsPowerNature1(String dcsPowerNature1) {
			this.dcsPowerNature1 = dcsPowerNature1;
		}

		public String getDcsPowerNature2() {
			return dcsPowerNature2;
		}

		public void setDcsPowerNature2(String dcsPowerNature2) {
			this.dcsPowerNature2 = dcsPowerNature2;
		}

		public String getDcsGdxlmc1() {
			return dcsGdxlmc1;
		}

		public void setDcsGdxlmc1(String dcsGdxlmc1) {
			this.dcsGdxlmc1 = dcsGdxlmc1;
		}

		public String getDcsGdxlmc2() {
			return dcsGdxlmc2;
		}

		public void setDcsGdxlmc2(String dcsGdxlmc2) {
			this.dcsGdxlmc2 = dcsGdxlmc2;
		}

		public String getDcsZx1() {
			return dcsZx1;
		}

		public void setDcsZx1(String dcsZx1) {
			this.dcsZx1 = dcsZx1;
		}

		public String getDcsZx2() {
			return dcsZx2;
		}

		public void setDcsZx2(String dcsZx2) {
			this.dcsZx2 = dcsZx2;
		}

		public String getDcsGddy1() {
			return dcsGddy1;
		}

		public void setDcsGddy1(String dcsGddy1) {
			this.dcsGddy1 = dcsGddy1;
		}

		public String getDcsGddy2() {
			return dcsGddy2;
		}

		public void setDcsGddy2(String dcsGddy2) {
			this.dcsGddy2 = dcsGddy2;
		}

		public String getDcsSdrl1() {
			return dcsSdrl1;
		}

		public void setDcsSdrl1(String dcsSdrl1) {
			this.dcsSdrl1 = dcsSdrl1;
		}

		public String getDcsSdrl2() {
			return dcsSdrl2;
		}

		public void setDcsSdrl2(String dcsSdrl2) {
			this.dcsSdrl2 = dcsSdrl2;
		}

		public String getFileUrl() {
			return fileUrl;
		}

		public void setFileUrl(String fileUrl) {
			this.fileUrl = fileUrl;
		}

		public String getDclName() {
			return dclName;
		}

		public void setDclName(String dclName) {
			this.dclName = dclName;
		}

		public String getDcsType() {
			return dcsType;
		}

		public void setDcsType(String dcsType) {
			this.dcsType = dcsType;
		}

		public String getDcsLogo() {
			return dcsLogo;
		}

		public void setDcsLogo(String dcsLogo) {
			this.dcsLogo = dcsLogo;
		}

		public String getDcsProvince() {
			return dcsProvince;
		}

		public void setDcsProvince(String dcsProvince) {
			this.dcsProvince = dcsProvince;
		}

		public String getDcsCity() {
			return dcsCity;
		}

		public void setDcsCity(String dcsCity) {
			this.dcsCity = dcsCity;
		}

		public String getDcsArea() {
			return dcsArea;
		}

		public void setDcsArea(String dcsArea) {
			this.dcsArea = dcsArea;
		}

		public Integer getBindWeatherStationId() {
			return bindWeatherStationId;
		}

		public void setBindWeatherStationId(Integer bindWeatherStationId) {
			this.bindWeatherStationId = bindWeatherStationId;
		}

		public String getBindWeatherName() {
			return bindWeatherName;
		}

		public void setBindWeatherName(String bindWeatherName) {
			this.bindWeatherName = bindWeatherName;
		}

	public String getRunState() {
		return runState;
	}

	public void setRunState(String runState) {
		this.runState = runState;
	}
}
