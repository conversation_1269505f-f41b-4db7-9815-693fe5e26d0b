package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2020-01-16 11:35:17
*/
@Alias("DcsWeatherStation")
public class DcsWeatherStation{

    // Fields

    private Integer dcsIndexId;
    private Integer dcsId;
    private Integer dcsDccId;
    private String dcsWeatherName;
    private Integer dcsWeatherLineId;
    private String lng;
    private String lat;
    private String dcsPersonName;
    private String dcsPersonPhone;
    private String dcsAdress;
    private String dccFullName;
    
    
    // Constructors

    /** default constructor */
    public DcsWeatherStation() {
    }

    // Property accessors

    public void setDcsIndexId(Integer dcsIndexId) {
        this.dcsIndexId = dcsIndexId;
    }

    public Integer getDcsIndexId() {
        return this.dcsIndexId;
    }

    public void setDcsId(Integer dcsId) {
        this.dcsId = dcsId;
    }

    public Integer getDcsId() {
        return this.dcsId;
    }

    public void setDcsDccId(Integer dcsDccId) {
        this.dcsDccId = dcsDccId;
    }

    public Integer getDcsDccId() {
        return this.dcsDccId;
    }

    public void setDcsWeatherName(String dcsWeatherName) {
        this.dcsWeatherName = dcsWeatherName;
    }

    public String getDcsWeatherName() {
        return this.dcsWeatherName;
    }

    public void setDcsWeatherLineId(Integer dcsWeatherLineId) {
        this.dcsWeatherLineId = dcsWeatherLineId;
    }

    public Integer getDcsWeatherLineId() {
        return this.dcsWeatherLineId;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLng() {
        return this.lng;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLat() {
        return this.lat;
    }

    public void setDcsPersonName(String dcsPersonName) {
        this.dcsPersonName = dcsPersonName;
    }

    public String getDcsPersonName() {
        return this.dcsPersonName;
    }

    public void setDcsPersonPhone(String dcsPersonPhone) {
        this.dcsPersonPhone = dcsPersonPhone;
    }

    public String getDcsPersonPhone() {
        return this.dcsPersonPhone;
    }

    public void setDcsAdress(String dcsAdress) {
        this.dcsAdress = dcsAdress;
    }

    public String getDcsAdress() {
        return this.dcsAdress;
    }

	public String getDccFullName() {
		return dccFullName;
	}

	public void setDccFullName(String dccFullName) {
		this.dccFullName = dccFullName;
	}

}