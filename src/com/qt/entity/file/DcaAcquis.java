package com.qt.entity.file;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-06 15:25:22
 */
@Alias("DcaAcquis")
public class DcaAcquis {

	// Fields
	// 采集设备编号
	private String dcaId;
	// 站号
	private Integer dcaDcsId;
	private String dcsName;// dccFullName
	// 采集设备IP地址
	private String dcaIp;
	// 设备第一次使用时间
	private Date dcaTime;
	// 是否启用设备（1：启用；0：不启用）
	private Integer dcaFlag;
	private String dcaFlagName;
	// 回路编号
	private Integer dclId;
	// 回路名称
	private String dclName;
	// 回路设备文档
	private String dclRecord;

	/** default constructor */
	public DcaAcquis() {
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDcaFlagName() {
		return dcaFlagName;
	}

	public void setDcaFlagName(String dcaFlagName) {
		this.dcaFlagName = dcaFlagName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public String getDclRecord() {
		return dclRecord;
	}

	public void setDclRecord(String dclRecord) {
		this.dclRecord = dclRecord;
	}

	public String getDcaId() {
		return dcaId;
	}

	public void setDcaId(String dcaId) {
		this.dcaId = dcaId;
	}

	public Integer getDcaDcsId() {
		return dcaDcsId;
	}

	public void setDcaDcsId(Integer dcaDcsId) {
		this.dcaDcsId = dcaDcsId;
	}

	public String getDcaIp() {
		return dcaIp;
	}

	public void setDcaIp(String dcaIp) {
		this.dcaIp = dcaIp;
	}

	public Date getDcaTime() {
		return dcaTime;
	}

	public void setDcaTime(Date dcaTime) {
		this.dcaTime = dcaTime;
	}

	public Integer getDcaFlag() {
		return dcaFlag;
	}

	public void setDcaFlag(Integer dcaFlag) {
		this.dcaFlag = dcaFlag;
	}

	public Integer getDclId() {
		return dclId;
	}

	public void setDclId(Integer dclId) {
		this.dclId = dclId;
	}

}