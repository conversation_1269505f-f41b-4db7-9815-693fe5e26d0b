package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

@Alias("DlUserBaseInfo")
public class DlUserBaseInfo {
	
	private Integer dlIndex;//序号
	
	private Integer dlUserStationId;//站点
	
	private Integer dlUserLineId;//回路
	
	private String dlUserBh;//编号
	
	private String dlUserXh;//型号
	
	private String dlUserLx;//类型
	
	private String dlUserLen;//长度
	
	private String dlUserCz;//材质
	
	private String dlUserBz;//包装
	
	private String dlUserXs;//芯数
	
	private String dlUserTx;//特性
	
	private String dlUserFsfs;//敷设方式
	
	private String dlUserSysm;//使用寿命
	
	private String dlUserYynx;//已用年限
	
	private String dlUserSggy;//施工工艺描述
	
	
	private String dcsName;//站点名称
	
	private String dclName;//回路名称

	public Integer getDlIndex() {
		return dlIndex;
	}

	public void setDlIndex(Integer dlIndex) {
		this.dlIndex = dlIndex;
	}

	public Integer getDlUserStationId() {
		return dlUserStationId;
	}

	public void setDlUserStationId(Integer dlUserStationId) {
		this.dlUserStationId = dlUserStationId;
	}

	public Integer getDlUserLineId() {
		return dlUserLineId;
	}

	public void setDlUserLineId(Integer dlUserLineId) {
		this.dlUserLineId = dlUserLineId;
	}

	public String getDlUserBh() {
		return dlUserBh;
	}

	public void setDlUserBh(String dlUserBh) {
		this.dlUserBh = dlUserBh;
	}

	public String getDlUserXh() {
		return dlUserXh;
	}

	public void setDlUserXh(String dlUserXh) {
		this.dlUserXh = dlUserXh;
	}

	public String getDlUserLx() {
		return dlUserLx;
	}

	public void setDlUserLx(String dlUserLx) {
		this.dlUserLx = dlUserLx;
	}

	public String getDlUserLen() {
		return dlUserLen;
	}

	public void setDlUserLen(String dlUserLen) {
		this.dlUserLen = dlUserLen;
	}

	public String getDlUserCz() {
		return dlUserCz;
	}

	public void setDlUserCz(String dlUserCz) {
		this.dlUserCz = dlUserCz;
	}

	public String getDlUserBz() {
		return dlUserBz;
	}

	public void setDlUserBz(String dlUserBz) {
		this.dlUserBz = dlUserBz;
	}

	public String getDlUserXs() {
		return dlUserXs;
	}

	public void setDlUserXs(String dlUserXs) {
		this.dlUserXs = dlUserXs;
	}

	public String getDlUserTx() {
		return dlUserTx;
	}

	public void setDlUserTx(String dlUserTx) {
		this.dlUserTx = dlUserTx;
	}

	public String getDlUserFsfs() {
		return dlUserFsfs;
	}

	public void setDlUserFsfs(String dlUserFsfs) {
		this.dlUserFsfs = dlUserFsfs;
	}

	public String getDlUserSysm() {
		return dlUserSysm;
	}

	public void setDlUserSysm(String dlUserSysm) {
		this.dlUserSysm = dlUserSysm;
	}

	public String getDlUserYynx() {
		return dlUserYynx;
	}

	public void setDlUserYynx(String dlUserYynx) {
		this.dlUserYynx = dlUserYynx;
	}

	public String getDlUserSggy() {
		return dlUserSggy;
	}

	public void setDlUserSggy(String dlUserSggy) {
		this.dlUserSggy = dlUserSggy;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	
	
}
