package com.qt.entity.file;

import java.util.Date;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2018-04-23 15:40:17
*/
public class SysUser{

    // Fields

    private Integer userid;
    private String username;
    private String userpassword;
    private String userfullname;
    private String usermobile;
    private String usermail;
    private String userextra;
    private String userremark;
    private String userconfig;
    private Integer userlogincount;
    private String userloginip;
    private Date userlogintime;
    private Integer isusing;
    private String createuser;
    private Date createtime;
    private String updateuser;
    private Date updatetime;
    private Integer dccId;
    private String clientId;
    
    // Constructors

    /** default constructor */
    public SysUser() {
    }

    // Property accessors

    public void setUserid(Integer userid) {
        this.userid = userid;
    }

    public Integer getUserid() {
        return this.userid;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUserpassword(String userpassword) {
        this.userpassword = userpassword;
    }

    public String getUserpassword() {
        return this.userpassword;
    }

    public void setUserfullname(String userfullname) {
        this.userfullname = userfullname;
    }

    public String getUserfullname() {
        return this.userfullname;
    }

    public void setUsermobile(String usermobile) {
        this.usermobile = usermobile;
    }

    public String getUsermobile() {
        return this.usermobile;
    }

    public void setUsermail(String usermail) {
        this.usermail = usermail;
    }

    public String getUsermail() {
        return this.usermail;
    }

    public void setUserextra(String userextra) {
        this.userextra = userextra;
    }

    public String getUserextra() {
        return this.userextra;
    }

    public void setUserremark(String userremark) {
        this.userremark = userremark;
    }

    public String getUserremark() {
        return this.userremark;
    }

    public void setUserconfig(String userconfig) {
        this.userconfig = userconfig;
    }

    public String getUserconfig() {
        return this.userconfig;
    }

    public void setUserlogincount(Integer userlogincount) {
        this.userlogincount = userlogincount;
    }

    public Integer getUserlogincount() {
        return this.userlogincount;
    }

    public void setUserloginip(String userloginip) {
        this.userloginip = userloginip;
    }

    public String getUserloginip() {
        return this.userloginip;
    }

    public void setUserlogintime(Date userlogintime) {
        this.userlogintime = userlogintime;
    }

    public Date getUserlogintime() {
        return this.userlogintime;
    }

    public void setIsusing(Integer isusing) {
        this.isusing = isusing;
    }

    public Integer getIsusing() {
        return this.isusing;
    }

    public void setCreateuser(String createuser) {
        this.createuser = createuser;
    }

    public String getCreateuser() {
        return this.createuser;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getCreatetime() {
        return this.createtime;
    }

    public void setUpdateuser(String updateuser) {
        this.updateuser = updateuser;
    }

    public String getUpdateuser() {
        return this.updateuser;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public Date getUpdatetime() {
        return this.updatetime;
    }

    public void setDccId(Integer dccId) {
        this.dccId = dccId;
    }

    public Integer getDccId() {
        return this.dccId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientId() {
        return this.clientId;
    }

}