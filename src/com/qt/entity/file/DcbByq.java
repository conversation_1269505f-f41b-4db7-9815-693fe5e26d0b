package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

@Alias("DcbByq")
public class DcbByq {
	private Integer dcbIndex;
	private Integer dcbId;
	private Integer dcbDccId;
	private Integer dcbDcsId;
	private String dcbName;
	
	private String dcsName;
	private String dccFullName;
	public DcbByq() {
		super();
	}
	public DcbByq(Integer dcbIndex, Integer dcbId, Integer dcbDcsId, String dcbName) {
		super();
		this.dcbIndex = dcbIndex;
		this.dcbId = dcbId;
		this.dcbDcsId = dcbDcsId;
		this.dcbName = dcbName;
	}
	public Integer getDcbIndex() {
		return dcbIndex;
	}
	public void setDcbIndex(Integer dcbIndex) {
		this.dcbIndex = dcbIndex;
	}
	public Integer getDcbId() {
		return dcbId;
	}
	public void setDcbId(Integer dcbId) {
		this.dcbId = dcbId;
	}
	public Integer getDcbDcsId() {
		return dcbDcsId;
	}
	public void setDcbDcsId(Integer dcbDcsId) {
		this.dcbDcsId = dcbDcsId;
	}
	public String getDcbName() {
		return dcbName;
	}
	public void setDcbName(String dcbName) {
		this.dcbName = dcbName;
	}
	public String getDcsName() {
		return dcsName;
	}
	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}
	public Integer getDcbDccId() {
		return dcbDccId;
	}
	public void setDcbDccId(Integer dcbDccId) {
		this.dcbDccId = dcbDccId;
	}
	public String getDccFullName() {
		return dccFullName;
	}
	public void setDccFullName(String dccFullName) {
		this.dccFullName = dccFullName;
	}
	
	
	
	
	
	
	

}
