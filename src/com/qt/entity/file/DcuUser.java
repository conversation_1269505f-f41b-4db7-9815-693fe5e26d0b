package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-06 09:49:20
 */
@Alias("DcuUser")
public class DcuUser {

	// Fields
	// 企业编号
	private Integer dcuDccId;
	private String dccFullName;// 企业名称
	// 编号
	private Integer dcuId;
	// 名称
	private String dcuName;
	// 职务
	private String dcuPosts;
	// 企业类型编号
	private Integer dcuBscId;
	private String bscName;// 企业类型名称

	public DcuUser() {
	}

	public String getDccFullName() {
		return dccFullName;
	}

	public void setDccFullName(String dccFullName) {
		this.dccFullName = dccFullName;
	}

	public String getBscName() {
		return bscName;
	}

	public void setBscName(String bscName) {
		this.bscName = bscName;
	}

	public void setDcuDccId(Integer dcuDccId) {
		this.dcuDccId = dcuDccId;
	}

	public Integer getDcuDccId() {
		return this.dcuDccId;
	}

	public void setDcuId(Integer dcuId) {
		this.dcuId = dcuId;
	}

	public Integer getDcuId() {
		return this.dcuId;
	}

	public void setDcuName(String dcuName) {
		this.dcuName = dcuName;
	}

	public String getDcuName() {
		return this.dcuName;
	}

	public void setDcuPosts(String dcuPosts) {
		this.dcuPosts = dcuPosts;
	}

	public String getDcuPosts() {
		return this.dcuPosts;
	}

	public void setDcuBscId(Integer dcuBscId) {
		this.dcuBscId = dcuBscId;
	}

	public Integer getDcuBscId() {
		return this.dcuBscId;
	}

}