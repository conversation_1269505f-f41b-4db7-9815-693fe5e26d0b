package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-04 14:28:45
 */
@Alias("DccInfo")
public class DccInfo {

	// Fields
	// 企业编号
	private Integer dccId;
	// 企业全称
	private String dccFullName;
	// 企业简称
	private String dccFirstName;
	// 企业类型编号
	private Integer dccBscId;
	// 地区编号
	private Integer dccAreaId;
	private String bscName;// 企业类型名称
	private String areaDesc;// 地区名称
	// 纬度
	private String dccLat;
	// 经度
	private String dccLng;
	// 企业地址
	private String dccAddr;
	// 企业电话
	private String dccTel;
	// 企业性质
	private String dccNature;
	// 企业用电户号
	private String dccUserCode;
	// 合同容量
	private Integer dccContractCapacity;
	// 运行容量
	private Integer dccRunCapacity;
	// 建筑面积
	private float dccArea;
	// 员工数
	private Integer dccWorkerNum;
	// 评价系数
	private float dccCoef;
	// 周工作时间，小时计算
	private Integer dccWorkTime;

	// Constructors
	

	/** default constructor */
	
	public DccInfo() {
	}

	public String getAreaDesc() {
		return areaDesc;
	}

	public void setAreaDesc(String areaDesc) {
		this.areaDesc = areaDesc;
	}

	public Integer getDccAreaId() {
		return dccAreaId;
	}

	public void setDccAreaId(Integer dccAreaId) {
		this.dccAreaId = dccAreaId;
	}


	public String getBscName() {
		return bscName;
	}

	public void setBscName(String bscName) {
		this.bscName = bscName;
	}

	public Integer getDccId() {
		return dccId;
	}

	public void setDccId(Integer dccId) {
		this.dccId = dccId;
	}

	public String getDccFullName() {
		return dccFullName;
	}

	public void setDccFullName(String dccFullName) {
		this.dccFullName = dccFullName;
	}

	public String getDccFirstName() {
		return dccFirstName;
	}

	public void setDccFirstName(String dccFirstName) {
		this.dccFirstName = dccFirstName;
	}

	public Integer getDccBscId() {
		return dccBscId;
	}

	public void setDccBscId(Integer dccBscId) {
		this.dccBscId = dccBscId;
	}

	public String getDccLat() {
		return dccLat;
	}

	public void setDccLat(String dccLat) {
		this.dccLat = dccLat;
	}

	public String getDccLng() {
		return dccLng;
	}

	public void setDccLng(String dccLng) {
		this.dccLng = dccLng;
	}

	public String getDccAddr() {
		return dccAddr;
	}

	public void setDccAddr(String dccAddr) {
		this.dccAddr = dccAddr;
	}

	public String getDccTel() {
		return dccTel;
	}

	public void setDccTel(String dccTel) {
		this.dccTel = dccTel;
	}

	public String getDccNature() {
		return dccNature;
	}

	public void setDccNature(String dccNature) {
		this.dccNature = dccNature;
	}

	public String getDccUserCode() {
		return dccUserCode;
	}

	public void setDccUserCode(String dccUserCode) {
		this.dccUserCode = dccUserCode;
	}

	public Integer getDccContractCapacity() {
		return dccContractCapacity;
	}

	public void setDccContractCapacity(Integer dccContractCapacity) {
		this.dccContractCapacity = dccContractCapacity;
	}

	public Integer getDccRunCapacity() {
		return dccRunCapacity;
	}

	public void setDccRunCapacity(Integer dccRunCapacity) {
		this.dccRunCapacity = dccRunCapacity;
	}

	public float getDccArea() {
		return dccArea;
	}

	public void setDccArea(float dccArea) {
		this.dccArea = dccArea;
	}

	public Integer getDccWorkerNum() {
		return dccWorkerNum;
	}

	public void setDccWorkerNum(Integer dccWorkerNum) {
		this.dccWorkerNum = dccWorkerNum;
	}

	public float getDccCoef() {
		return dccCoef;
	}

	public void setDccCoef(float dccCoef) {
		this.dccCoef = dccCoef;
	}

	public Integer getDccWorkTime() {
		return dccWorkTime;
	}

	public void setDccWorkTime(Integer dccWorkTime) {
		this.dccWorkTime = dccWorkTime;
	}

}