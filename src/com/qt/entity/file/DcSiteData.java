package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

	public class DcSiteData {

		private String dcsAdress;//用电地址
		private String dcsType;
		private Integer dcsTraNum;//主变台数
		private String dcsSdrl1;//受电容量
		private String dcsName;//站名(客户名称)
	    private String stationId;
	    private String counts;
	    private String realTimePower;
	    private String todayElec;
	    private String allElec;
	    private String accountId;
	    private String isFollow;
	    private String isBenchmark;
	    private String runState;

	    public DcSiteData(){
		    	
		}
	    
		public String getIsBenchmark() {
			return isBenchmark;
		}

		public void setIsBenchmark(String isBenchmark) {
			this.isBenchmark = isBenchmark;
		}

		public String getAccountId() {
			return accountId;
		}

		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

		public String getIsFollow() {
			return isFollow;
		}

		public void setIsFollow(String isFollow) {
			this.isFollow = isFollow;
		}

		public String getStationId() {
			return stationId;
		}

		public void setStationId(String stationId) {
			this.stationId = stationId;
		}

		public String getCounts() {
			return counts;
		}

		public void setCounts(String counts) {
			this.counts = counts;
		}

		public String getRealTimePower() {
			return realTimePower;
		}

		public void setRealTimePower(String realTimePower) {
			this.realTimePower = realTimePower;
		}

		public String getTodayElec() {
			return todayElec;
		}

		public void setTodayElec(String todayElec) {
			this.todayElec = todayElec;
		}

		public String getAllElec() {
			return allElec;
		}

		public void setAllElec(String allElec) {
			this.allElec = allElec;
		}


	public String getRunState() {
		return runState;
	}

	public void setRunState(String runState) {
		this.runState = runState;
	}

		public String getDcsAdress() {
			return dcsAdress;
		}

		public void setDcsAdress(String dcsAdress) {
			this.dcsAdress = dcsAdress;
		}

		public String getDcsType() {
			return dcsType;
		}

		public void setDcsType(String dcsType) {
			this.dcsType = dcsType;
		}

		public Integer getDcsTraNum() {
			return dcsTraNum;
		}

		public void setDcsTraNum(Integer dcsTraNum) {
			this.dcsTraNum = dcsTraNum;
		}

		public String getDcsSdrl1() {
			return dcsSdrl1;
		}

		public void setDcsSdrl1(String dcsSdrl1) {
			this.dcsSdrl1 = dcsSdrl1;
		}

		public String getDcsName() {
			return dcsName;
		}

		public void setDcsName(String dcsName) {
			this.dcsName = dcsName;
		}
	}
