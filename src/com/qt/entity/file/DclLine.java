package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-10 09:49:39
 */
@Alias("DclLine")
public class DclLine {

	private Integer dclId;
	private String equId;
	private Integer dclIndex;
	private Integer dclDcsId;
	private String dclDcbId;
	private Integer dclParentId;
	private String dclName;
	private String dclRecord;
	private float dclJp;
	private float dclFp;
	private float dclPp;
	private float dclGp;
	private String dcsName;
	private String dcbName;
	private String dclCapacity;
	private String dclInverter;
	private float dclPowerRating;//20180510新增额定输出功率
	
	
	//新增字段
	private String dclDeviceAddress;
	private String dclDevicePattern;
	private String dclInstallLocation;
	private String dclInstallSlope;
	private String dclDeviceCompany;
	private Integer dclCollectorNum;
	private Integer dclOutputNum;
	private Integer dclInputNum;
	private Float dclTransferMultiple;
	private Integer dclWarn;
	private Integer dclType;

	public String getEquId() {
		return equId;
	}

	public void setEquId(String equId) {
		this.equId = equId;
	}

	public String getDclCapacity() {
		return dclCapacity;
	}

	public void setDclCapacity(String dclCapacity) {
		this.dclCapacity = dclCapacity;
	}

	public String getDclInverter() {
		return dclInverter;
	}

	public void setDclInverter(String dclInverter) {
		this.dclInverter = dclInverter;
	}

	/** default constructor */
	
	public DclLine() {
	}
	
	public String getDcbName() {
		return dcbName;
	}

	public void setDcbName(String dcbName) {
		this.dcbName = dcbName;
	}

	public Integer getDclIndex() {
		return dclIndex;
	}

	public void setDclIndex(Integer dclIndex) {
		this.dclIndex = dclIndex;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public Integer getDclId() {
		return dclId;
	}

	public void setDclId(Integer dclId) {
		this.dclId = dclId;
	}

	public Integer getDclDcsId() {
		return dclDcsId;
	}

	public void setDclDcsId(Integer dclDcsId) {
		this.dclDcsId = dclDcsId;
	}



	public String getDclDcbId() {
		return dclDcbId;
	}

	public void setDclDcbId(String dclDcbId) {
		this.dclDcbId = dclDcbId;
	}

	public Integer getDclParentId() {
		return dclParentId;
	}

	public void setDclParentId(Integer dclParentId) {
		this.dclParentId = dclParentId;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public String getDclRecord() {
		return dclRecord;
	}

	public void setDclRecord(String dclRecord) {
		this.dclRecord = dclRecord;
	}

	public float getDclJp() {
		return dclJp;
	}

	public void setDclJp(float dclJp) {
		this.dclJp = dclJp;
	}

	public float getDclFp() {
		return dclFp;
	}

	public void setDclFp(float dclFp) {
		this.dclFp = dclFp;
	}

	public float getDclPp() {
		return dclPp;
	}

	public void setDclPp(float dclPp) {
		this.dclPp = dclPp;
	}

	public float getDclGp() {
		return dclGp;
	}

	public void setDclGp(float dclGp) {
		this.dclGp = dclGp;
	}

	public float getDclPowerRating() {
		return dclPowerRating;
	}

	public void setDclPowerRating(float dclPowerRating) {
		this.dclPowerRating = dclPowerRating;
	}

	public String getDclDeviceAddress() {
		return dclDeviceAddress;
	}

	public void setDclDeviceAddress(String dclDeviceAddress) {
		this.dclDeviceAddress = dclDeviceAddress;
	}

	public String getDclDevicePattern() {
		return dclDevicePattern;
	}

	public void setDclDevicePattern(String dclDevicePattern) {
		this.dclDevicePattern = dclDevicePattern;
	}

	public String getDclInstallLocation() {
		return dclInstallLocation;
	}

	public void setDclInstallLocation(String dclInstallLocation) {
		this.dclInstallLocation = dclInstallLocation;
	}

	public String getDclInstallSlope() {
		return dclInstallSlope;
	}

	public void setDclInstallSlope(String dclInstallSlope) {
		this.dclInstallSlope = dclInstallSlope;
	}

	public String getDclDeviceCompany() {
		return dclDeviceCompany;
	}

	public void setDclDeviceCompany(String dclDeviceCompany) {
		this.dclDeviceCompany = dclDeviceCompany;
	}

	public Integer getDclCollectorNum() {
		return dclCollectorNum;
	}

	public void setDclCollectorNum(Integer dclCollectorNum) {
		this.dclCollectorNum = dclCollectorNum;
	}

	public Integer getDclOutputNum() {
		return dclOutputNum;
	}

	public void setDclOutputNum(Integer dclOutputNum) {
		this.dclOutputNum = dclOutputNum;
	}

	public Integer getDclInputNum() {
		return dclInputNum;
	}

	public void setDclInputNum(Integer dclInputNum) {
		this.dclInputNum = dclInputNum;
	}

	public Float getDclTransferMultiple() {
		return dclTransferMultiple;
	}

	public void setDclTransferMultiple(Float dclTransferMultiple) {
		this.dclTransferMultiple = dclTransferMultiple;
	}

	public Integer getDclWarn() {
		return dclWarn;
	}

	public void setDclWarn(Integer dclWarn) {
		this.dclWarn = dclWarn;
	}

	public Integer getDclType() {
		return dclType;
	}

	public void setDclType(Integer dclType) {
		this.dclType = dclType;
	}


}