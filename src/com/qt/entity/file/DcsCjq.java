package com.qt.entity.file;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2020-01-20 09:32:26
*/
@Alias("/DcsCjq")
public class DcsCjq{

    // Fields

    private Integer dcsCjqIndex;
    private Integer dcsCjqStationId;
    private Integer dcsCjqId;
    private String dcsCjqName;
    private Integer dcsCjqNumber;
    private String dcsCjqIp1;
    private String dcsCjqIp2;
    private String dcsCjqWg1;
    private String dcsCjqWg2;
    private String dcsCjqOnlinetime;
    private String dcsCjqSim;
    private String dcsCjqPlace;
    private Integer dcsCjqInvalid;
    private Integer dcsCjqCondition;
    private String dcsCjqLasttime;
    private String dcsName;
    private String searchName;
    
    // Constructors

    /** default constructor */
    public DcsCjq() {
    }

    // Property accessors

    public void setDcsCjqIndex(Integer dcsCjqIndex) {
        this.dcsCjqIndex = dcsCjqIndex;
    }

    public Integer getDcsCjqIndex() {
        return this.dcsCjqIndex;
    }

    public void setDcsCjqStationId(Integer dcsCjqStationId) {
        this.dcsCjqStationId = dcsCjqStationId;
    }

    public Integer getDcsCjqStationId() {
        return this.dcsCjqStationId;
    }

    public void setDcsCjqId(Integer dcsCjqId) {
        this.dcsCjqId = dcsCjqId;
    }

    public Integer getDcsCjqId() {
        return this.dcsCjqId;
    }

    public void setDcsCjqName(String dcsCjqName) {
        this.dcsCjqName = dcsCjqName;
    }

    public String getDcsCjqName() {
        return this.dcsCjqName;
    }

    public void setDcsCjqNumber(Integer dcsCjqNumber) {
        this.dcsCjqNumber = dcsCjqNumber;
    }

    public Integer getDcsCjqNumber() {
        return this.dcsCjqNumber;
    }

    public void setDcsCjqIp1(String dcsCjqIp1) {
        this.dcsCjqIp1 = dcsCjqIp1;
    }

    public String getDcsCjqIp1() {
        return this.dcsCjqIp1;
    }

    public void setDcsCjqIp2(String dcsCjqIp2) {
        this.dcsCjqIp2 = dcsCjqIp2;
    }

    public String getDcsCjqIp2() {
        return this.dcsCjqIp2;
    }

    public void setDcsCjqWg1(String dcsCjqWg1) {
        this.dcsCjqWg1 = dcsCjqWg1;
    }

    public String getDcsCjqWg1() {
        return this.dcsCjqWg1;
    }

    public void setDcsCjqWg2(String dcsCjqWg2) {
        this.dcsCjqWg2 = dcsCjqWg2;
    }

    public String getDcsCjqWg2() {
        return this.dcsCjqWg2;
    }

    public void setDcsCjqOnlinetime(String dcsCjqOnlinetime) {
        this.dcsCjqOnlinetime = dcsCjqOnlinetime;
    }

    public String getDcsCjqOnlinetime() {
        return this.dcsCjqOnlinetime;
    }

    public void setDcsCjqSim(String dcsCjqSim) {
        this.dcsCjqSim = dcsCjqSim;
    }

    public String getDcsCjqSim() {
        return this.dcsCjqSim;
    }

    public void setDcsCjqPlace(String dcsCjqPlace) {
        this.dcsCjqPlace = dcsCjqPlace;
    }

    public String getDcsCjqPlace() {
        return this.dcsCjqPlace;
    }

    public void setDcsCjqInvalid(Integer dcsCjqInvalid) {
        this.dcsCjqInvalid = dcsCjqInvalid;
    }

    public Integer getDcsCjqInvalid() {
        return this.dcsCjqInvalid;
    }

    public void setDcsCjqCondition(Integer dcsCjqCondition) {
        this.dcsCjqCondition = dcsCjqCondition;
    }

    public Integer getDcsCjqCondition() {
        return this.dcsCjqCondition;
    }

    public void setDcsCjqLasttime(String dcsCjqLasttime) {
        this.dcsCjqLasttime = dcsCjqLasttime;
    }

    public String getDcsCjqLasttime() {
        return this.dcsCjqLasttime;
    }

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

}