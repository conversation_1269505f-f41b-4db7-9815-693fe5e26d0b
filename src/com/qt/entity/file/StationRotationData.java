package com.qt.entity.file;

import org.apache.ibatis.type.Alias;

/**
 * 分类名称
 * 分类备注/描述
 *
 * <AUTHOR>
 * @module 归属项目
 * @date Created in 10:44 2022/3/10
 */
@Alias("StationRotationData")
public class StationRotationData {
    private String dcsAdress;//用电地址
    private String dcsSdrl1;//受电容量
    private String dcsName;//站名(客户名称)
    private String stationId;
    private String realTimePower;
    private String todayElec;
    private String allElec;
    private String runState;
    private String stationContacts;
    private String createTime;

    public String getDcsAdress() {
        return dcsAdress;
    }

    public void setDcsAdress(String dcsAdress) {
        this.dcsAdress = dcsAdress;
    }

    public String getDcsSdrl1() {
        return dcsSdrl1;
    }

    public void setDcsSdrl1(String dcsSdrl1) {
        this.dcsSdrl1 = dcsSdrl1;
    }

    public String getDcsName() {
        return dcsName;
    }

    public void setDcsName(String dcsName) {
        this.dcsName = dcsName;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getRealTimePower() {
        return realTimePower;
    }

    public void setRealTimePower(String realTimePower) {
        this.realTimePower = realTimePower;
    }

    public String getTodayElec() {
        return todayElec;
    }

    public void setTodayElec(String todayElec) {
        this.todayElec = todayElec;
    }

    public String getAllElec() {
        return allElec;
    }

    public void setAllElec(String allElec) {
        this.allElec = allElec;
    }

    public String getRunState() {
        return runState;
    }

    public void setRunState(String runState) {
        this.runState = runState;
    }

    public String getStationContacts() {
        return stationContacts;
    }

    public void setStationContacts(String stationContacts) {
        this.stationContacts = stationContacts;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
