package com.qt.entity.baselib;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * shaobo
 */
@Alias("BscType")
public class BscType {

	// Fields

	private Integer bscId;
	private String bscName;
	private Integer bscBspId;
	private Date bscCreateTime;

	// Constructors

	/** default constructor */
	public BscType() {
	}

	public Integer getBscId() {
		return bscId;
	}

	public void setBscId(Integer bscId) {
		this.bscId = bscId;
	}

	public String getBscName() {
		return bscName;
	}

	public void setBscName(String bscName) {
		this.bscName = bscName;
	}

	public Integer getBscBspId() {
		return bscBspId;
	}

	public void setBscBspId(Integer bscBspId) {
		this.bscBspId = bscBspId;
	}

	public Date getBscCreateTime() {
		return bscCreateTime;
	}

	public void setBscCreateTime(Date bscCreateTime) {
		this.bscCreateTime = bscCreateTime;
	}

}