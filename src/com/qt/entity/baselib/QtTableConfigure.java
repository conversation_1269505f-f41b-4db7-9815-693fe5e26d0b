package com.qt.entity.baselib;

import org.apache.ibatis.type.Alias;

@Alias("QtTableConfigure")
public class QtTableConfigure {
	
	private Integer id;
	
	private String codeConfigure;
	
	private String module;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCodeConfigure() {
		return codeConfigure;
	}

	public void setCodeConfigure(String codeConfigure) {
		this.codeConfigure = codeConfigure;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}
	
}
