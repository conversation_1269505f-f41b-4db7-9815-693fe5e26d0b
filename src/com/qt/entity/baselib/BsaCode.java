package com.qt.entity.baselib;

import org.apache.ibatis.type.Alias;

/**
 * <AUTHOR>
 * 指标码
 */
@Alias("BsaCode")
public class BsaCode {

	// Fields

	private Integer bsaId;       //指标编码
	private Integer bsaTypeId;   //指标类型
	private String bsaDesc;      //指标描述
	private String bsaGroupId;   //指标码分组id
	private Integer bsaType;     //是否启用
	private String bsaTime;        //添加时间
    
	private String bsaGroupName; //指标码分组名称
	private String bsaUnit;    //指标码单位

	private String yType; //1-遥信，2-遥测

	// Constructors

	/** default constructor */
	public BsaCode() {
	}
	
	public String getBsaGroupId() {
		return bsaGroupId;
	}

	public void setBsaGroupId(String bsaGroupId) {
		this.bsaGroupId = bsaGroupId;
	}

	public String getBsaUnit() {
		return bsaUnit;
	}

	public void setBsaUnit(String bsaUnit) {
		this.bsaUnit = bsaUnit;
	}

	public String getyType() {
		return yType;
	}

	public void setyType(String yType) {
		this.yType = yType;
	}

	public Integer getBsaId() {
		return bsaId;
	}

	public void setBsaId(Integer bsaId) {
		this.bsaId = bsaId;
	}

	public Integer getBsaTypeId() {
		return bsaTypeId;
	}

	public void setBsaTypeId(Integer bsaTypeId) {
		this.bsaTypeId = bsaTypeId;
	}

	public String getBsaDesc() {
		return bsaDesc;
	}

	public void setBsaDesc(String bsaDesc) {
		this.bsaDesc = bsaDesc;
	}

	/*public String getBsaGroupId() {
		return bsaGroupId;
	}

	public void setBsaGroupId(String bsaGroupId) {
		this.bsaGroupId = bsaGroupId;
	}*/

	public Integer getBsaType() {
		return bsaType;
	}

	public void setBsaType(Integer bsaType) {
		this.bsaType = bsaType;
	}

	public String getBsaTime() {
		return bsaTime;
	}

	public void setBsaTime(String bsaTime) {
		this.bsaTime = bsaTime;
	}

	public String getBsaGroupName() {
		return bsaGroupName;
	}

	public void setBsaGroupName(String bsaGroupName) {
		this.bsaGroupName = bsaGroupName;
	}
	
	

	

}