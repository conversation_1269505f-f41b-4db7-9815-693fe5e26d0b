package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-15 18:35:48
 */
@Alias("StpDay")
public class StpDay {

	// Fields

	private Integer stpId;
	private String stpStationId;
	private String stpLineId;
	private float stpElecValue;
	private Date stpElecTime;
	private Integer stpSyncDc;
	private String stpSyncTime;

	private String dcsName;
	private String dclName;

	private String stationId;
	private String realtionExprId;
	private String address;
	private String dayVal;
	private String monthVal;
	private String yearVal;

	// Constructors

	/** default constructor */
	public StpDay() {
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getDayVal() {
		return dayVal;
	}

	public void setDayVal(String dayVal) {
		this.dayVal = dayVal;
	}

	public String getMonthVal() {
		return monthVal;
	}

	public void setMonthVal(String monthVal) {
		this.monthVal = monthVal;
	}

	public String getYearVal() {
		return yearVal;
	}

	public void setYearVal(String yearVal) {
		this.yearVal = yearVal;
	}

	public String getRealtionExprId() {
		return realtionExprId;
	}

	public void setRealtionExprId(String realtionExprId) {
		this.realtionExprId = realtionExprId;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public Integer getStpId() {
		return stpId;
	}

	public void setStpId(Integer stpId) {
		this.stpId = stpId;
	}

	public String getStpStationId() {
		return stpStationId;
	}

	public void setStpStationId(String stpStationId) {
		this.stpStationId = stpStationId;
	}

	public String getStpLineId() {
		return stpLineId;
	}

	public void setStpLineId(String stpLineId) {
		this.stpLineId = stpLineId;
	}

	public float getStpElecValue() {
		return stpElecValue;
	}

	public void setStpElecValue(float stpElecValue) {
		this.stpElecValue = stpElecValue;
	}

	public Date getStpElecTime() {
		return stpElecTime;
	}

	public void setStpElecTime(Date stpElecTime) {
		this.stpElecTime = stpElecTime;
	}

	public Integer getStpSyncDc() {
		return stpSyncDc;
	}

	public void setStpSyncDc(Integer stpSyncDc) {
		this.stpSyncDc = stpSyncDc;
	}

	public String getStpSyncTime() {
		return stpSyncTime;
	}

	public void setStpSyncTime(String stpSyncTime) {
		this.stpSyncTime = stpSyncTime;
	}
}