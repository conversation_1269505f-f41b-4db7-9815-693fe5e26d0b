package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * 电量年报表  行转列实体（month作为列名）
 * <AUTHOR>
 * @date 2018年7月3日上午11:54:02
 */
@Alias("StpYearCol")
public class StpYearCol {

	// Fields

	private String dclName;
	private String stpElecValue1;////因float类型会致使报表将0和无数据null均显示“——”问题，所以定义为String类型
	private String stpElecValue2;
	private String stpElecValue3;
	private String stpElecValue4;
	private String stpElecValue5;
	private String stpElecValue6;
	private String stpElecValue7;
	private String stpElecValue8;
	private String stpElecValue9;
	private String stpElecValue10;
	private String stpElecValue11;
	private String stpElecValue12;
	private String stpStationId;
	private String stpLineId;
	private String dcsName;
	

	// Constructors

	/** default constructor */
	public StpYearCol() {
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	
	public String getStpElecValue1() {
		return stpElecValue1;
	}

	public void setStpElecValue1(String stpElecValue1) {
		this.stpElecValue1 = stpElecValue1;
	}

	public String getStpElecValue2() {
		return stpElecValue2;
	}

	public void setStpElecValue2(String stpElecValue2) {
		this.stpElecValue2 = stpElecValue2;
	}

	public String getStpElecValue3() {
		return stpElecValue3;
	}

	public void setStpElecValue3(String stpElecValue3) {
		this.stpElecValue3 = stpElecValue3;
	}

	public String getStpElecValue4() {
		return stpElecValue4;
	}

	public void setStpElecValue4(String stpElecValue4) {
		this.stpElecValue4 = stpElecValue4;
	}

	public String getStpElecValue5() {
		return stpElecValue5;
	}

	public void setStpElecValue5(String stpElecValue5) {
		this.stpElecValue5 = stpElecValue5;
	}

	public String getStpElecValue6() {
		return stpElecValue6;
	}

	public void setStpElecValue6(String stpElecValue6) {
		this.stpElecValue6 = stpElecValue6;
	}

	public String getStpElecValue7() {
		return stpElecValue7;
	}

	public void setStpElecValue7(String stpElecValue7) {
		this.stpElecValue7 = stpElecValue7;
	}

	public String getStpElecValue8() {
		return stpElecValue8;
	}

	public void setStpElecValue8(String stpElecValue8) {
		this.stpElecValue8 = stpElecValue8;
	}

	public String getStpElecValue9() {
		return stpElecValue9;
	}

	public void setStpElecValue9(String stpElecValue9) {
		this.stpElecValue9 = stpElecValue9;
	}

	public String getStpElecValue10() {
		return stpElecValue10;
	}

	public void setStpElecValue10(String stpElecValue10) {
		this.stpElecValue10 = stpElecValue10;
	}

	public String getStpElecValue11() {
		return stpElecValue11;
	}

	public void setStpElecValue11(String stpElecValue11) {
		this.stpElecValue11 = stpElecValue11;
	}

	public String getStpElecValue12() {
		return stpElecValue12;
	}

	public void setStpElecValue12(String stpElecValue12) {
		this.stpElecValue12 = stpElecValue12;
	}

	public String getStpStationId() {
		return stpStationId;
	}

	public void setStpStationId(String stpStationId) {
		this.stpStationId = stpStationId;
	}

	public String getStpLineId() {
		return stpLineId;
	}

	public void setStpLineId(String stpLineId) {
		this.stpLineId = stpLineId;
	}
	
	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}


	// Property accessors

}