package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;
@Alias("StpOriginal")
public class StpOriginal {

	// Fields

	private Integer stpId;
	private Integer stpStationId;
	private Integer stpLineId;
	private float stpElecValue;
	private Date stpElecTime;
	private Integer stpSyncDc;
	private Date stpSyncTime;

	private String dcsName;// 站名
	private String dclName;// 回路名

	// Constructors

	/** default constructor */
	public StpOriginal() {
	}

	public Integer getStpId() {
		return stpId;
	}

	public void setStpId(Integer stpId) {
		this.stpId = stpId;
	}

	public Integer getStpStationId() {
		return stpStationId;
	}

	public void setStpStationId(Integer stpStationId) {
		this.stpStationId = stpStationId;
	}

	public Integer getStpLineId() {
		return stpLineId;
	}

	public void setStpLineId(Integer stpLineId) {
		this.stpLineId = stpLineId;
	}

	public float getStpElecValue() {
		return stpElecValue;
	}

	public void setStpElecValue(float stpElecValue) {
		this.stpElecValue = stpElecValue;
	}

	public Date getStpElecTime() {
		return stpElecTime;
	}

	public void setStpElecTime(Date stpElecTime) {
		this.stpElecTime = stpElecTime;
	}

	public Integer getStpSyncDc() {
		return stpSyncDc;
	}

	public void setStpSyncDc(Integer stpSyncDc) {
		this.stpSyncDc = stpSyncDc;
	}

	public Date getStpSyncTime() {
		return stpSyncTime;
	}

	public void setStpSyncTime(Date stpSyncTime) {
		this.stpSyncTime = stpSyncTime;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

}