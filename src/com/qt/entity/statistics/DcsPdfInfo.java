package com.qt.entity.statistics;


import java.util.List;

import org.apache.ibatis.type.Alias;

@Alias("/DcsPdfInfo")
public class DcsPdfInfo {
	private Integer dcsPdfInfoId;
	private Integer dcsPdfDcsId;//站点id
	private Integer dcsPdfDccId;//公司id
	private String dcsPdfName;//配电房及变压器名称
	private Integer dcsPdfSize;//容量
	private String dcsPdfType;//型号
	private String dcsPdfJxzb;//接线组别
	private String dcsPdfVoltageOne;//电压一次侧
	private String dcsPdfVoltageTwo;//电压二次侧
	private String dcsPdfCurrentOne;//电流一次侧
	private String dcsPdfCurrentTwo;//电流二次侧
	private String dcsPdfYxzt;//运行状态
	private String dcsPdfByqYgsh;//变压器有功损耗
	private String dcsPdfByqWgsh;//变压器无功损耗
	private String dcsPdfByqK;//变压器K值
	private String dcsPdfBz;//备注
	
	public DcsPdfInfo(){
		
	}
	public Integer getDcsPdfInfoId() {
		return dcsPdfInfoId;
	}
	public void setDcsPdfInfoId(Integer dcsPdfInfoId) {
		this.dcsPdfInfoId = dcsPdfInfoId;
	}
	public Integer getDcsPdfDcsId() {
		return dcsPdfDcsId;
	}
	public void setDcsPdfDcsId(Integer dcsPdfDcsId) {
		this.dcsPdfDcsId = dcsPdfDcsId;
	}
	public Integer getDcsPdfDccId() {
		return dcsPdfDccId;
	}
	public void setDcsPdfDccId(Integer dcsPdfDccId) {
		this.dcsPdfDccId = dcsPdfDccId;
	}
	public String getDcsPdfName() {
		return dcsPdfName;
	}
	public void setDcsPdfName(String dcsPdfName) {
		this.dcsPdfName = dcsPdfName;
	}
	public Integer getDcsPdfSize() {
		return dcsPdfSize;
	}
	public void setDcsPdfSize(Integer dcsPdfSize) {
		this.dcsPdfSize = dcsPdfSize;
	}
	public String getDcsPdfType() {
		return dcsPdfType;
	}
	public void setDcsPdfType(String dcsPdfType) {
		this.dcsPdfType = dcsPdfType;
	}
	public String getDcsPdfJxzb() {
		return dcsPdfJxzb;
	}
	public void setDcsPdfJxzb(String dcsPdfJxzb) {
		this.dcsPdfJxzb = dcsPdfJxzb;
	}
	public String getDcsPdfVoltageOne() {
		return dcsPdfVoltageOne;
	}
	public void setDcsPdfVoltageOne(String dcsPdfVoltageOne) {
		this.dcsPdfVoltageOne = dcsPdfVoltageOne;
	}
	public String getDcsPdfVoltageTwo() {
		return dcsPdfVoltageTwo;
	}
	public void setDcsPdfVoltageTwo(String dcsPdfVoltageTwo) {
		this.dcsPdfVoltageTwo = dcsPdfVoltageTwo;
	}
	public String getDcsPdfCurrentOne() {
		return dcsPdfCurrentOne;
	}
	public void setDcsPdfCurrentOne(String dcsPdfCurrentOne) {
		this.dcsPdfCurrentOne = dcsPdfCurrentOne;
	}
	public String getDcsPdfCurrentTwo() {
		return dcsPdfCurrentTwo;
	}
	public void setDcsPdfCurrentTwo(String dcsPdfCurrentTwo) {
		this.dcsPdfCurrentTwo = dcsPdfCurrentTwo;
	}
	public String getDcsPdfYxzt() {
		return dcsPdfYxzt;
	}
	public void setDcsPdfYxzt(String dcsPdfYxzt) {
		this.dcsPdfYxzt = dcsPdfYxzt;
	}
	public String getDcsPdfByqYgsh() {
		return dcsPdfByqYgsh;
	}
	public void setDcsPdfByqYgsh(String dcsPdfByqYgsh) {
		this.dcsPdfByqYgsh = dcsPdfByqYgsh;
	}
	public String getDcsPdfByqWgsh() {
		return dcsPdfByqWgsh;
	}
	public void setDcsPdfByqWgsh(String dcsPdfByqWgsh) {
		this.dcsPdfByqWgsh = dcsPdfByqWgsh;
	}
	public String getDcsPdfByqK() {
		return dcsPdfByqK;
	}
	public void setDcsPdfByqK(String dcsPdfByqK) {
		this.dcsPdfByqK = dcsPdfByqK;
	}
	public String getDcsPdfBz() {
		return dcsPdfBz;
	}
	public void setDcsPdfBz(String dcsPdfBz) {
		this.dcsPdfBz = dcsPdfBz;
	}
	
}
