package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

@<PERSON>as("StoTemp")
public class StoTemp {
	private Integer stoId;
	// 站id
	private Integer stoStationId;
	// 设备id
	private Integer stoDeviceId;
	// 回路id
	private Integer stoLineId; 
	//
	private Integer stoAcCode;
	//
	private float stoOrignalValue;
	//
	private Date stoOrignalTime;
	private Integer stoSyncDc;
	private Date stoSyncTime;

	private String bsaDesc;// 采集因子
	private String bsaDesc1;// 采集因子1
	private String bsaDesc2;// 采集因子2
	private String bsaDesc3;// 采集因子3
	private String bsaDesc4;// 采集因子1
	private String bsaDesc5;// 采集因子2
	private String bsaDesc6;

	private String dcsName;// 站名
	private String dclName;// 回路名

	public StoTemp(){
		
	}

	public Integer getStoId() {
		return stoId;
	}

	public void setStoId(Integer stoId) {
		this.stoId = stoId;
	}

	public Integer getStoStationId() {
		return stoStationId;
	}

	public void setStoStationId(Integer stoStationId) {
		this.stoStationId = stoStationId;
	}

	public Integer getStoDeviceId() {
		return stoDeviceId;
	}

	public void setStoDeviceId(Integer stoDeviceId) {
		this.stoDeviceId = stoDeviceId;
	}

	public Integer getStoLineId() {
		return stoLineId;
	}

	public void setStoLineId(Integer stoLineId) {
		this.stoLineId = stoLineId;
	}

	public Integer getStoAcCode() {
		return stoAcCode;
	}

	public void setStoAcCode(Integer stoAcCode) {
		this.stoAcCode = stoAcCode;
	}

	public float getStoOrignalValue() {
		return stoOrignalValue;
	}

	public void setStoOrignalValue(float stoOrignalValue) {
		this.stoOrignalValue = stoOrignalValue;
	}

	public Date getStoOrignalTime() {
		return stoOrignalTime;
	}

	public void setStoOrignalTime(Date stoOrignalTime) {
		this.stoOrignalTime = stoOrignalTime;
	}

	public Integer getStoSyncDc() {
		return stoSyncDc;
	}

	public void setStoSyncDc(Integer stoSyncDc) {
		this.stoSyncDc = stoSyncDc;
	}

	public Date getStoSyncTime() {
		return stoSyncTime;
	}

	public void setStoSyncTime(Date stoSyncTime) {
		this.stoSyncTime = stoSyncTime;
	}

	public String getBsaDesc() {
		return bsaDesc;
	}

	public void setBsaDesc(String bsaDesc) {
		this.bsaDesc = bsaDesc;
	}

	public String getBsaDesc1() {
		return bsaDesc1;
	}

	public void setBsaDesc1(String bsaDesc1) {
		this.bsaDesc1 = bsaDesc1;
	}

	public String getBsaDesc2() {
		return bsaDesc2;
	}

	public void setBsaDesc2(String bsaDesc2) {
		this.bsaDesc2 = bsaDesc2;
	}

	public String getBsaDesc3() {
		return bsaDesc3;
	}

	public void setBsaDesc3(String bsaDesc3) {
		this.bsaDesc3 = bsaDesc3;
	}

	public String getBsaDesc4() {
		return bsaDesc4;
	}

	public void setBsaDesc4(String bsaDesc4) {
		this.bsaDesc4 = bsaDesc4;
	}

	public String getBsaDesc5() {
		return bsaDesc5;
	}

	public void setBsaDesc5(String bsaDesc5) {
		this.bsaDesc5 = bsaDesc5;
	}

	public String getBsaDesc6() {
		return bsaDesc6;
	}

	public void setBsaDesc6(String bsaDesc6) {
		this.bsaDesc6 = bsaDesc6;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	
}
