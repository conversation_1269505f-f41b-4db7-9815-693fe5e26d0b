package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-09 17:54:05
 */
@Alias("StoValue")
public class StoValue {

	// Fields

	private Integer stoId;
	// 站id
	private Integer stoStationId;
	// 设备id
	private Integer stoDeviceId;
	// 回路id
	private Integer stoLineId;
	//
	private Integer stoAcCode;
	//
	private float stoOrignalValue;
	//
	private Date stoOrignalTime;
	private Integer stoSyncDc;
	private Date stoSyncTime;

	private String bsaDesc;// 采集因子
	private String dcsName;// 站名
	private String dclName;// 站名
	
	private String accountId;
	private String stationId;
	private String zjrl;//装机容量
	private String fddl;//发电电量
	private String bwdl;//并网电量
	private String maxSrgl;//最大输入功率
	private String maxScgl;//最大输出功率
	private String zhxl;//转换效率
	private String glys;//功率因数
	private String isBenchmark;//是否是标杆
	private String dxxs;//等效小时

	// Constructors

	/** default constructor */
	public StoValue() {
	}
	
	public String getIsBenchmark() {
		return isBenchmark;
	}

	public void setIsBenchmark(String isBenchmark) {
		this.isBenchmark = isBenchmark;
	}

	public String getZjrl() {
		return zjrl;
	}

	public void setZjrl(String zjrl) {
		this.zjrl = zjrl;
	}

	public String getFddl() {
		return fddl;
	}

	public void setFddl(String fddl) {
		this.fddl = fddl;
	}

	public String getBwdl() {
		return bwdl;
	}

	public void setBwdl(String bwdl) {
		this.bwdl = bwdl;
	}

	public String getMaxSrgl() {
		return maxSrgl;
	}

	public void setMaxSrgl(String maxSrgl) {
		this.maxSrgl = maxSrgl;
	}

	public String getMaxScgl() {
		return maxScgl;
	}

	public void setMaxScgl(String maxScgl) {
		this.maxScgl = maxScgl;
	}

	public String getZhxl() {
		return zhxl;
	}

	public void setZhxl(String zhxl) {
		this.zhxl = zhxl;
	}

	public String getGlys() {
		return glys;
	}

	public void setGlys(String glys) {
		this.glys = glys;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public Integer getStoId() {
		return stoId;
	}

	public void setStoId(Integer stoId) {
		this.stoId = stoId;
	}

	public Integer getStoStationId() {
		return stoStationId;
	}

	public void setStoStationId(Integer stoStationId) {
		this.stoStationId = stoStationId;
	}

	public Integer getStoDeviceId() {
		return stoDeviceId;
	}

	public void setStoDeviceId(Integer stoDeviceId) {
		this.stoDeviceId = stoDeviceId;
	}

	public Integer getStoLineId() {
		return stoLineId;
	}

	public void setStoLineId(Integer stoLineId) {
		this.stoLineId = stoLineId;
	}

	public Integer getStoAcCode() {
		return stoAcCode;
	}

	public void setStoAcCode(Integer stoAcCode) {
		this.stoAcCode = stoAcCode;
	}

	public float getStoOrignalValue() {
		return stoOrignalValue;
	}

	public void setStoOrignalValue(float stoOrignalValue) {
		this.stoOrignalValue = stoOrignalValue;
	}

	public Date getStoOrignalTime() {
		return stoOrignalTime;
	}

	public void setStoOrignalTime(Date stoOrignalTime) {
		this.stoOrignalTime = stoOrignalTime;
	}

	public Integer getStoSyncDc() {
		return stoSyncDc;
	}

	public void setStoSyncDc(Integer stoSyncDc) {
		this.stoSyncDc = stoSyncDc;
	}

	public Date getStoSyncTime() {
		return stoSyncTime;
	}

	public void setStoSyncTime(Date stoSyncTime) {
		this.stoSyncTime = stoSyncTime;
	}

	public String getBsaDesc() {
		return bsaDesc;
	}

	public void setBsaDesc(String bsaDesc) {
		this.bsaDesc = bsaDesc;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public String getDxxs() {
		return dxxs;
	}

	public void setDxxs(String dxxs) {
		this.dxxs = dxxs;
	}
}