/**
 * 
 */
package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * <AUTHOR>
 *
 */
@Alias("/EvhAlarm")
public class EvhAlarm {
	
	
	private String evhAlarmId;
	private String evhStationId;
	private String evhLineId;
	private Integer evhBsaCode;
	private String evhDesc;
	private Date evhOcTime;
	private String evhAlarmBianhao;
	private String evhTime;
	private Integer evhLevel;
	private String evhNowValue;
	private String evhAlarmValue;
	private String evhReturnValue;
	
	private Integer evhStatus;
	private String dcsName;
	private String dclName;
	private String bsaDesc;

	private String countNum;
	private String yxNum;
	private String ycNum;
	private String ycl;

	public String getCountNum() {
		return countNum;
	}

	public void setCountNum(String countNum) {
		this.countNum = countNum;
	}

	public String getYxNum() {
		return yxNum;
	}

	public void setYxNum(String yxNum) {
		this.yxNum = yxNum;
	}

	public String getYcNum() {
		return ycNum;
	}

	public void setYcNum(String ycNum) {
		this.ycNum = ycNum;
	}

	public String getYcl() {
		return ycl;
	}

	public void setYcl(String ycl) {
		this.ycl = ycl;
	}

	public String getEvhStationId() {
		return evhStationId;
	}

	public void setEvhStationId(String evhStationId) {
		this.evhStationId = evhStationId;
	}

	public String getEvhLineId() {
		return evhLineId;
	}

	public void setEvhLineId(String evhLineId) {
		this.evhLineId = evhLineId;
	}

	public String getEvhNowValue() {
		return evhNowValue;
	}
	public void setEvhNowValue(String evhNowValue) {
		this.evhNowValue = evhNowValue;
	}
	public String getEvhAlarmValue() {
		return evhAlarmValue;
	}
	public void setEvhAlarmValue(String evhAlarmValue) {
		this.evhAlarmValue = evhAlarmValue;
	}
	public String getEvhReturnValue() {
		return evhReturnValue;
	}
	public void setEvhReturnValue(String evhReturnValue) {
		this.evhReturnValue = evhReturnValue;
	}
	public String getEvhAlarmBianhao() {
		return evhAlarmBianhao;
	}
	public void setEvhAlarmBianhao(String evhAlarmBianhao) {
		this.evhAlarmBianhao = evhAlarmBianhao;
	}
	public String getBsaDesc() {
		return bsaDesc;
	}
	public void setBsaDesc(String bsaDesc) {
		this.bsaDesc = bsaDesc;
	}
	public String getDcsName() {
		return dcsName;
	}
	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}
	public String getDclName() {
		return dclName;
	}
	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	public String getEvhAlarmId() {
		return evhAlarmId;
	}
	public void setEvhAlarmId(String evhAlarmId) {
		this.evhAlarmId = evhAlarmId;
	}
	public Integer getEvhBsaCode() {
		return evhBsaCode;
	}
	public void setEvhBsaCode(Integer evhBsaCode) {
		this.evhBsaCode = evhBsaCode;
	}
	public String getEvhDesc() {
		return evhDesc;
	}
	public void setEvhDesc(String evhDesc) {
		this.evhDesc = evhDesc;
	}
	public Date getEvhOcTime() {
		return evhOcTime;
	}
	public void setEvhOcTime(Date evhOcTime) {
		this.evhOcTime = evhOcTime;
	}
	public String getEvhTime() {
		return evhTime;
	}
	public void setEvhTime(String evhTime) {
		this.evhTime = evhTime;
	}
	public Integer getEvhLevel() {
		return evhLevel;
	}
	public void setEvhLevel(Integer evhLevel) {
		this.evhLevel = evhLevel;
	}
	public Integer getEvhStatus() {
		return evhStatus;
	}
	public void setEvhStatus(Integer evhStatus) {
		this.evhStatus = evhStatus;
	}
	

}
