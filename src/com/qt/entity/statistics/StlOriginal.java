package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;
@Alias("StlOriginal")
public class StlOriginal {

	// Fields

	private Integer stlId;
	// 站id
	private Integer stlStationId;
	// 回路id
	private Integer stlLineId;
	//
	private Integer stlAcCode;
	//
	private float stlOrignalValue;
	//
	private Date stlOrignalTime;
	private Integer stlSyncDc;
	private Date stlSyncTime;

	private String bsaDesc;// 采集因子
	private String bsaDesc1;// 采集因子1
	private String bsaDesc2;// 采集因子2
	private String bsaDesc3;// 采集因子3

	private String dcsName;// 站名
	private String dclName;// 回路名

	public Integer getStlId() {
		return stlId;
	}

	public void setStlId(Integer stlId) {
		this.stlId = stlId;
	}

	public Integer getStlStationId() {
		return stlStationId;
	}

	public void setStlStationId(Integer stlStationId) {
		this.stlStationId = stlStationId;
	}

	public Integer getStlLineId() {
		return stlLineId;
	}

	public void setStlLineId(Integer stlLineId) {
		this.stlLineId = stlLineId;
	}

	public Integer getStlAcCode() {
		return stlAcCode;
	}

	public void setStlAcCode(Integer stlAcCode) {
		this.stlAcCode = stlAcCode;
	}

	public float getStlOrignalValue() {
		return stlOrignalValue;
	}

	public void setStlOrignalValue(float stlOrignalValue) {
		this.stlOrignalValue = stlOrignalValue;
	}

	public Date getStlOrignalTime() {
		return stlOrignalTime;
	}

	public void setStlOrignalTime(Date stlOrignalTime) {
		this.stlOrignalTime = stlOrignalTime;
	}

	public Integer getStlSyncDc() {
		return stlSyncDc;
	}

	public void setStlSyncDc(Integer stlSyncDc) {
		this.stlSyncDc = stlSyncDc;
	}

	public Date getStlSyncTime() {
		return stlSyncTime;
	}

	public void setStlSyncTime(Date stlSyncTime) {
		this.stlSyncTime = stlSyncTime;
	}

	public String getBsaDesc() {
		return bsaDesc;
	}

	public void setBsaDesc(String bsaDesc) {
		this.bsaDesc = bsaDesc;
	}

	public String getBsaDesc1() {
		return bsaDesc1;
	}

	public void setBsaDesc1(String bsaDesc1) {
		this.bsaDesc1 = bsaDesc1;
	}

	public String getBsaDesc2() {
		return bsaDesc2;
	}

	public void setBsaDesc2(String bsaDesc2) {
		this.bsaDesc2 = bsaDesc2;
	}

	public String getBsaDesc3() {
		return bsaDesc3;
	}

	public void setBsaDesc3(String bsaDesc3) {
		this.bsaDesc3 = bsaDesc3;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

}