package com.qt.entity.statistics;

import org.apache.ibatis.type.Alias;

@Alias("ValueStatistics")
public class ValueStatistics {

	private Integer id;
	private Integer valueStaionId;
	private Integer valueLineId;
	private String time;// 时间段
	private String dclName;// 监测点

	// 负荷
	private String stlOrignalValueA;// A相负荷
	private String stlOrignalValueB;// B相负荷
	private String stlOrignalValueC;// C相负荷
	private String stlOrignalValueTotal;// 总负荷

	// 电量
	private String stpElecValue;// 电量

	// 示数
	private String stoOrignalValueZY;// 电量
	private String stoOrignalValueFY;// 电量
	private String stoOrignalValueZW;// 电量
	private String stoOrignalValueFW;// 电量

	// 电压
	private String stoOrignalValueDYA;// A电压
	private String stoOrignalValueDYB;// B电压
	private String stoOrignalValueDYC;// C电压

	// 电流
	private String stoOrignalValueDLA;// A电压
	private String stoOrignalValueDLB;// B电压
	private String stoOrignalValueDLC;// C电压
	private String stoOrignalValueDLTotal;// C电压

	// 功率因素
	private String stoOrignalValueGLA;// A电压
	private String stoOrignalValueGLB;// B电压
	private String stoOrignalValueGLC;// C电压
	private String stoOrignalValueGLTotal;// C电压

	// 温度
	private String stoOrignalValueWDA;// A电压
	private String stoOrignalValueWDB;// B电压
	private String stoOrignalValueWDC;// C电压

	// 谐波
	private String stoOrignalValueXBDLA;// B电压
	private String stoOrignalValueXBDYA;// C电压
	private String stoOrignalValueXBDLB;// B电压
	private String stoOrignalValueXBDYB;// C电压
	private String stoOrignalValueXBDLC;// B电压
	private String stoOrignalValueXBDYC;// C电压
	
	// 回路电流
	private String stoOrignalValueLDL1;
	private String stoOrignalValueLDL2;
	private String stoOrignalValueLDL3;
	private String stoOrignalValueLDL4;
	private String stoOrignalValueLDL5;
	private String stoOrignalValueLDL6;
	private String stoOrignalValueLDL7;
	private String stoOrignalValueLDL8;
	private String stoOrignalValueLDL9;
	
	private String dayTime;
	
	public String getDayTime() {
		return dayTime;
	}
	public void setDayTime(String dayTime) {
		this.dayTime = dayTime;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getValueStaionId() {
		return valueStaionId;
	}
	public void setValueStaionId(Integer valueStaionId) {
		this.valueStaionId = valueStaionId;
	}
	public Integer getValueLineId() {
		return valueLineId;
	}
	public void setValueLineId(Integer valueLineId) {
		this.valueLineId = valueLineId;
	}
	public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	public String getDclName() {
		return dclName;
	}
	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	public String getStlOrignalValueA() {
		return stlOrignalValueA;
	}
	public void setStlOrignalValueA(String stlOrignalValueA) {
		this.stlOrignalValueA = stlOrignalValueA;
	}
	public String getStlOrignalValueB() {
		return stlOrignalValueB;
	}
	public void setStlOrignalValueB(String stlOrignalValueB) {
		this.stlOrignalValueB = stlOrignalValueB;
	}
	public String getStlOrignalValueC() {
		return stlOrignalValueC;
	}
	public void setStlOrignalValueC(String stlOrignalValueC) {
		this.stlOrignalValueC = stlOrignalValueC;
	}
	public String getStlOrignalValueTotal() {
		return stlOrignalValueTotal;
	}
	public void setStlOrignalValueTotal(String stlOrignalValueTotal) {
		this.stlOrignalValueTotal = stlOrignalValueTotal;
	}
	public String getStpElecValue() {
		return stpElecValue;
	}
	public void setStpElecValue(String stpElecValue) {
		this.stpElecValue = stpElecValue;
	}
	public String getStoOrignalValueZY() {
		return stoOrignalValueZY;
	}
	public void setStoOrignalValueZY(String stoOrignalValueZY) {
		this.stoOrignalValueZY = stoOrignalValueZY;
	}
	public String getStoOrignalValueFY() {
		return stoOrignalValueFY;
	}
	public void setStoOrignalValueFY(String stoOrignalValueFY) {
		this.stoOrignalValueFY = stoOrignalValueFY;
	}
	public String getStoOrignalValueZW() {
		return stoOrignalValueZW;
	}
	public void setStoOrignalValueZW(String stoOrignalValueZW) {
		this.stoOrignalValueZW = stoOrignalValueZW;
	}
	public String getStoOrignalValueFW() {
		return stoOrignalValueFW;
	}
	public void setStoOrignalValueFW(String stoOrignalValueFW) {
		this.stoOrignalValueFW = stoOrignalValueFW;
	}
	public String getStoOrignalValueDYA() {
		return stoOrignalValueDYA;
	}
	public void setStoOrignalValueDYA(String stoOrignalValueDYA) {
		this.stoOrignalValueDYA = stoOrignalValueDYA;
	}
	public String getStoOrignalValueDYB() {
		return stoOrignalValueDYB;
	}
	public void setStoOrignalValueDYB(String stoOrignalValueDYB) {
		this.stoOrignalValueDYB = stoOrignalValueDYB;
	}
	public String getStoOrignalValueDYC() {
		return stoOrignalValueDYC;
	}
	public void setStoOrignalValueDYC(String stoOrignalValueDYC) {
		this.stoOrignalValueDYC = stoOrignalValueDYC;
	}
	public String getStoOrignalValueDLA() {
		return stoOrignalValueDLA;
	}
	public void setStoOrignalValueDLA(String stoOrignalValueDLA) {
		this.stoOrignalValueDLA = stoOrignalValueDLA;
	}
	public String getStoOrignalValueDLB() {
		return stoOrignalValueDLB;
	}
	public void setStoOrignalValueDLB(String stoOrignalValueDLB) {
		this.stoOrignalValueDLB = stoOrignalValueDLB;
	}
	public String getStoOrignalValueDLC() {
		return stoOrignalValueDLC;
	}
	public void setStoOrignalValueDLC(String stoOrignalValueDLC) {
		this.stoOrignalValueDLC = stoOrignalValueDLC;
	}
	public String getStoOrignalValueDLTotal() {
		return stoOrignalValueDLTotal;
	}
	public void setStoOrignalValueDLTotal(String stoOrignalValueDLTotal) {
		this.stoOrignalValueDLTotal = stoOrignalValueDLTotal;
	}
	public String getStoOrignalValueGLA() {
		return stoOrignalValueGLA;
	}
	public void setStoOrignalValueGLA(String stoOrignalValueGLA) {
		this.stoOrignalValueGLA = stoOrignalValueGLA;
	}
	public String getStoOrignalValueGLB() {
		return stoOrignalValueGLB;
	}
	public void setStoOrignalValueGLB(String stoOrignalValueGLB) {
		this.stoOrignalValueGLB = stoOrignalValueGLB;
	}
	public String getStoOrignalValueGLC() {
		return stoOrignalValueGLC;
	}
	public void setStoOrignalValueGLC(String stoOrignalValueGLC) {
		this.stoOrignalValueGLC = stoOrignalValueGLC;
	}
	public String getStoOrignalValueGLTotal() {
		return stoOrignalValueGLTotal;
	}
	public void setStoOrignalValueGLTotal(String stoOrignalValueGLTotal) {
		this.stoOrignalValueGLTotal = stoOrignalValueGLTotal;
	}
	public String getStoOrignalValueWDA() {
		return stoOrignalValueWDA;
	}
	public void setStoOrignalValueWDA(String stoOrignalValueWDA) {
		this.stoOrignalValueWDA = stoOrignalValueWDA;
	}
	public String getStoOrignalValueWDB() {
		return stoOrignalValueWDB;
	}
	public void setStoOrignalValueWDB(String stoOrignalValueWDB) {
		this.stoOrignalValueWDB = stoOrignalValueWDB;
	}
	public String getStoOrignalValueWDC() {
		return stoOrignalValueWDC;
	}
	public void setStoOrignalValueWDC(String stoOrignalValueWDC) {
		this.stoOrignalValueWDC = stoOrignalValueWDC;
	}
	public String getStoOrignalValueXBDLA() {
		return stoOrignalValueXBDLA;
	}
	public void setStoOrignalValueXBDLA(String stoOrignalValueXBDLA) {
		this.stoOrignalValueXBDLA = stoOrignalValueXBDLA;
	}
	public String getStoOrignalValueXBDYA() {
		return stoOrignalValueXBDYA;
	}
	public void setStoOrignalValueXBDYA(String stoOrignalValueXBDYA) {
		this.stoOrignalValueXBDYA = stoOrignalValueXBDYA;
	}
	public String getStoOrignalValueXBDLB() {
		return stoOrignalValueXBDLB;
	}
	public void setStoOrignalValueXBDLB(String stoOrignalValueXBDLB) {
		this.stoOrignalValueXBDLB = stoOrignalValueXBDLB;
	}
	public String getStoOrignalValueXBDYB() {
		return stoOrignalValueXBDYB;
	}
	public void setStoOrignalValueXBDYB(String stoOrignalValueXBDYB) {
		this.stoOrignalValueXBDYB = stoOrignalValueXBDYB;
	}
	public String getStoOrignalValueXBDLC() {
		return stoOrignalValueXBDLC;
	}
	public void setStoOrignalValueXBDLC(String stoOrignalValueXBDLC) {
		this.stoOrignalValueXBDLC = stoOrignalValueXBDLC;
	}
	public String getStoOrignalValueXBDYC() {
		return stoOrignalValueXBDYC;
	}
	public void setStoOrignalValueXBDYC(String stoOrignalValueXBDYC) {
		this.stoOrignalValueXBDYC = stoOrignalValueXBDYC;
	}
	public String getStoOrignalValueLDL1() {
		return stoOrignalValueLDL1;
	}
	public void setStoOrignalValueLDL1(String stoOrignalValueLDL1) {
		this.stoOrignalValueLDL1 = stoOrignalValueLDL1;
	}
	public String getStoOrignalValueLDL2() {
		return stoOrignalValueLDL2;
	}
	public void setStoOrignalValueLDL2(String stoOrignalValueLDL2) {
		this.stoOrignalValueLDL2 = stoOrignalValueLDL2;
	}
	public String getStoOrignalValueLDL3() {
		return stoOrignalValueLDL3;
	}
	public void setStoOrignalValueLDL3(String stoOrignalValueLDL3) {
		this.stoOrignalValueLDL3 = stoOrignalValueLDL3;
	}
	public String getStoOrignalValueLDL4() {
		return stoOrignalValueLDL4;
	}
	public void setStoOrignalValueLDL4(String stoOrignalValueLDL4) {
		this.stoOrignalValueLDL4 = stoOrignalValueLDL4;
	}
	public String getStoOrignalValueLDL5() {
		return stoOrignalValueLDL5;
	}
	public void setStoOrignalValueLDL5(String stoOrignalValueLDL5) {
		this.stoOrignalValueLDL5 = stoOrignalValueLDL5;
	}
	public String getStoOrignalValueLDL6() {
		return stoOrignalValueLDL6;
	}
	public void setStoOrignalValueLDL6(String stoOrignalValueLDL6) {
		this.stoOrignalValueLDL6 = stoOrignalValueLDL6;
	}
	public String getStoOrignalValueLDL7() {
		return stoOrignalValueLDL7;
	}
	public void setStoOrignalValueLDL7(String stoOrignalValueLDL7) {
		this.stoOrignalValueLDL7 = stoOrignalValueLDL7;
	}
	public String getStoOrignalValueLDL8() {
		return stoOrignalValueLDL8;
	}
	public void setStoOrignalValueLDL8(String stoOrignalValueLDL8) {
		this.stoOrignalValueLDL8 = stoOrignalValueLDL8;
	}
	public String getStoOrignalValueLDL9() {
		return stoOrignalValueLDL9;
	}
	public void setStoOrignalValueLDL9(String stoOrignalValueLDL9) {
		this.stoOrignalValueLDL9 = stoOrignalValueLDL9;
	}
	
}
