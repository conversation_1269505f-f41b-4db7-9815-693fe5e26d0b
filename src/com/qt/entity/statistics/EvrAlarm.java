/**
 * 
 */
package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * <AUTHOR>
 *
 */
@Alias("/EvrAlarm")
public class EvrAlarm {
	
	
	private String evrAlarmId;
	private String evrStationId;
	private String evrLineId;
	private Integer evrBsaCode;
	private String evrDesc;
	private Date evrOcTime;
	private Integer evrLevel;
	private String evrTime;
	private String evrNowValue;
	private String evrAlarmValue;
	private String evrReturnValue;
	
	private String dcsName;
	private String dclName;
	private String bsaDesc;
	private String evrAlarmBianhao;
	private Integer evrStatus;
	
	private String times;
	private String counts;
	private String yType;

	private String dclType;//回路类型
	
	private String lng;
    private String lat;
    private String stationContacts;
    
	public String getStationContacts() {
		return stationContacts;
	}

	public void setStationContacts(String stationContacts) {
		this.stationContacts = stationContacts;
	}

	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	public String getDclType() {
		return dclType;
	}

	public void setDclType(String dclType) {
		this.dclType = dclType;
	}

	public String getCounts() {
		return counts;
	}

	public void setCounts(String counts) {
		this.counts = counts;
	}

	public String getyType() {
		return yType;
	}

	public void setyType(String yType) {
		this.yType = yType;
	}

	public String getEvrNowValue() {
		return evrNowValue;
	}
	public void setEvrNowValue(String evrNowValue) {
		this.evrNowValue = evrNowValue;
	}
	public String getEvrAlarmValue() {
		return evrAlarmValue;
	}
	public void setEvrAlarmValue(String evrAlarmValue) {
		this.evrAlarmValue = evrAlarmValue;
	}
	public String getEvrReturnValue() {
		return evrReturnValue;
	}
	public void setEvrReturnValue(String evrReturnValue) {
		this.evrReturnValue = evrReturnValue;
	}
	public String getEvrTime() {
		return evrTime;
	}
	public void setEvrTime(String evrTime) {
		this.evrTime = evrTime;
	}
	public String getEvrAlarmBianhao() {
		return evrAlarmBianhao;
	}
	public void setEvrAlarmBianhao(String evrAlarmBianhao) {
		this.evrAlarmBianhao = evrAlarmBianhao;
	}
	public String getBsaDesc() {
		return bsaDesc;
	}
	public void setBsaDesc(String bsaDesc) {
		this.bsaDesc = bsaDesc;
	}
	public String getDcsName() {
		return dcsName;
	}
	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}
	public String getDclName() {
		return dclName;
	}
	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	public String getEvrAlarmId() {
		return evrAlarmId;
	}
	public void setEvrAlarmId(String evrAlarmId) {
		this.evrAlarmId = evrAlarmId;
	}
	public Integer getEvrBsaCode() {
		return evrBsaCode;
	}
	public void setEvrBsaCode(Integer evrBsaCode) {
		this.evrBsaCode = evrBsaCode;
	}
	public String getEvrDesc() {
		return evrDesc;
	}
	public void setEvrDesc(String evrDesc) {
		this.evrDesc = evrDesc;
	}
	public Date getEvrOcTime() {
		return evrOcTime;
	}
	public void setEvrOcTime(Date evrOcTime) {
		this.evrOcTime = evrOcTime;
	}
	public Integer getEvrLevel() {
		return evrLevel;
	}
	public void setEvrLevel(Integer evrLevel) {
		this.evrLevel = evrLevel;
	}
	public Integer getEvrStatus() {
		return evrStatus;
	}
	public void setEvrStatus(Integer evrStatus) {
		this.evrStatus = evrStatus;
	}
	public String getTimes() {
		return times;
	}
	public void setTimes(String times) {
		this.times = times;
	}

	public String getEvrStationId() {
		return evrStationId;
	}

	public void setEvrStationId(String evrStationId) {
		this.evrStationId = evrStationId;
	}

	public String getEvrLineId() {
		return evrLineId;
	}

	public void setEvrLineId(String evrLineId) {
		this.evrLineId = evrLineId;
	}
}
