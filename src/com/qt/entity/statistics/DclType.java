package com.qt.entity.statistics;

public enum DclType {
		AMMETER("电表","1"),
		EXCHANGE_CABINET("交流柜","2"),
		DC_CABINET("变压器","3"),
		INVERTER("逆变器","4"),
		CONFLUENCE_BOX("汇流箱","5");
	
	    private  String value;
	    
	    private  String index;
	    
	    DclType(String value,String index) {
	        this.value = value;
	        this.index = index;
	    }

	    public String getValue() {
			return value;
		}

		public void setValue(String value) {
			this.value = value;
		}

		public String getIndex() {
			return index;
		}

		public void setIndex(String index) {
			this.index = index;
		}

		public static String getStatuName(String index) {
	        for (DclType c: DclType.values()) {
	            if (c.getIndex().equals(index)) {
	                return c.value;
	            }
	        }
	        return null;
	    }
	
}
