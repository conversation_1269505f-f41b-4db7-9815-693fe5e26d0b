package com.qt.entity.statistics;

import org.apache.ibatis.type.Alias;

@Alias("DlUserReport")
public class DlUserReport {
	private Integer dlIndex;
	private Integer dlStationId;
	private Integer dlLineId;
	private String dlLineName;
	private String dlBh;
	private String dlXxbs;
	private String dlXw;
	private Integer dlAlarmCount;
	private String dlAcName;
	private String dlAcUnit;
	private float dlGbValue;
	private float dlSdValue;
	private float dlJcValue;
	private float dlDbValue;
	private String dlJjpj;
	private Integer dlSjsm;
	private Integer dlYynx;
	private Integer dlSynx;
	private String dlAqpj;
	private String dlAqfx;
	private String dlZgyj;
	private String dlJjfx;
	
	private String dcsName;//站点名称
	
	private String dclName;//回路名称
	

	public DlUserReport(){
		
	}

	
	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	public Integer getDlIndex() {
		return dlIndex;
	}

	public void setDlIndex(Integer dlIndex) {
		this.dlIndex = dlIndex;
	}

	public Integer getDlStationId() {
		return dlStationId;
	}

	public void setDlStationId(Integer dlStationId) {
		this.dlStationId = dlStationId;
	}

	public Integer getDlLineId() {
		return dlLineId;
	}

	public void setDlLineId(Integer dlLineId) {
		this.dlLineId = dlLineId;
	}

	public String getDlLineName() {
		return dlLineName;
	}

	public void setDlLineName(String dlLineName) {
		this.dlLineName = dlLineName;
	}

	public String getDlBh() {
		return dlBh;
	}

	public void setDlBh(String dlBh) {
		this.dlBh = dlBh;
	}

	public String getDlXxbs() {
		return dlXxbs;
	}

	public void setDlXxbs(String dlXxbs) {
		this.dlXxbs = dlXxbs;
	}

	public String getDlXw() {
		return dlXw;
	}

	public void setDlXw(String dlXw) {
		this.dlXw = dlXw;
	}

	public Integer getDlAlarmCount() {
		return dlAlarmCount;
	}

	public void setDlAlarmCount(Integer dlAlarmCount) {
		this.dlAlarmCount = dlAlarmCount;
	}

	public String getDlAcName() {
		return dlAcName;
	}

	public void setDlAcName(String dlAcName) {
		this.dlAcName = dlAcName;
	}

	public String getDlAcUnit() {
		return dlAcUnit;
	}

	public void setDlAcUnit(String dlAcUnit) {
		this.dlAcUnit = dlAcUnit;
	}

	public float getDlGbValue() {
		return dlGbValue;
	}

	public void setDlGbValue(float dlGbValue) {
		this.dlGbValue = dlGbValue;
	}

	public float getDlSdValue() {
		return dlSdValue;
	}

	public void setDlSdValue(float dlSdValue) {
		this.dlSdValue = dlSdValue;
	}

	public float getDlJcValue() {
		return dlJcValue;
	}

	public void setDlJcValue(float dlJcValue) {
		this.dlJcValue = dlJcValue;
	}

	public float getDlDbValue() {
		return dlDbValue;
	}

	public void setDlDbValue(float dlDbValue) {
		this.dlDbValue = dlDbValue;
	}

	public String getDlJjpj() {
		return dlJjpj;
	}

	public void setDlJjpj(String dlJjpj) {
		this.dlJjpj = dlJjpj;
	}

	public Integer getDlSjsm() {
		return dlSjsm;
	}

	public void setDlSjsm(Integer dlSjsm) {
		this.dlSjsm = dlSjsm;
	}

	public Integer getDlYynx() {
		return dlYynx;
	}

	public void setDlYynx(Integer dlYynx) {
		this.dlYynx = dlYynx;
	}

	public Integer getDlSynx() {
		return dlSynx;
	}

	public void setDlSynx(Integer dlSynx) {
		this.dlSynx = dlSynx;
	}

	public String getDlAqpj() {
		return dlAqpj;
	}

	public void setDlAqpj(String dlAqpj) {
		this.dlAqpj = dlAqpj;
	}

	public String getDlAqfx() {
		return dlAqfx;
	}

	public void setDlAqfx(String dlAqfx) {
		this.dlAqfx = dlAqfx;
	}

	public String getDlZgyj() {
		return dlZgyj;
	}

	public void setDlZgyj(String dlZgyj) {
		this.dlZgyj = dlZgyj;
	}

	public String getDlJjfx() {
		return dlJjfx;
	}

	public void setDlJjfx(String dlJjfx) {
		this.dlJjfx = dlJjfx;
	}
	
}
