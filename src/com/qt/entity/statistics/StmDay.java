package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;
@Alias("/StmDay")
public class StmDay {
	private Integer stmId;
	// 站id
	private Integer stmStationId;
	// 设备id
	private Integer stmDeviceId;
	// 回路id
	private Integer stmLineId; 
	//
	private Integer stmAcCode;
	//
	private float stmMaxValue;
	private Date stmMaxTime;
	
	private float stmMinValue;
	private Date stmMinTime;
	
	private float stmAverValue;
	
	private Integer stmSyncDc;
	private Date stmSyncTime;
	
	private String bsaDesc;// 采集因子
	private String bsaDesc1;// 采集因子1
	private String bsaDesc2;// 采集因子2
	private String bsaDesc3;// 采集因子3
	private String bsaDesc4;// 采集因子1
	private String bsaDesc5;// 采集因子2
	private String bsaDesc6;

	private String dcsName;// 站名
	private String dclName;// 回路名
	public Integer getStmId() {
		return stmId;
	}
	public void setStmId(Integer stmId) {
		this.stmId = stmId;
	}
	public Integer getStmStationId() {
		return stmStationId;
	}
	public void setStmStationId(Integer stmStationId) {
		this.stmStationId = stmStationId;
	}
	public Integer getStmDeviceId() {
		return stmDeviceId;
	}
	public void setStmDeviceId(Integer stmDeviceId) {
		this.stmDeviceId = stmDeviceId;
	}
	public Integer getStmLineId() {
		return stmLineId;
	}
	public void setStmLineId(Integer stmLineId) {
		this.stmLineId = stmLineId;
	}
	public Integer getStmAcCode() {
		return stmAcCode;
	}
	public void setStmAcCode(Integer stmAcCode) {
		this.stmAcCode = stmAcCode;
	}
	public float getStmMaxValue() {
		return stmMaxValue;
	}
	public void setStmMaxValue(float stmMaxValue) {
		this.stmMaxValue = stmMaxValue;
	}
	public Date getStmMaxTime() {
		return stmMaxTime;
	}
	public void setStmMaxTime(Date stmMaxTime) {
		this.stmMaxTime = stmMaxTime;
	}
	public float getStmMinValue() {
		return stmMinValue;
	}
	public void setStmMinValue(float stmMinValue) {
		this.stmMinValue = stmMinValue;
	}
	public Date getStmMinTime() {
		return stmMinTime;
	}
	public void setStmMinTime(Date stmMinTime) {
		this.stmMinTime = stmMinTime;
	}
	public float getStmAverValue() {
		return stmAverValue;
	}
	public void setStmAverValue(float stmAverValue) {
		this.stmAverValue = stmAverValue;
	}
	public Integer getStmSyncDc() {
		return stmSyncDc;
	}
	public void setStmSyncDc(Integer stmSyncDc) {
		this.stmSyncDc = stmSyncDc;
	}
	public Date getStmSyncTime() {
		return stmSyncTime;
	}
	public void setStmSyncTime(Date stmSyncTime) {
		this.stmSyncTime = stmSyncTime;
	}
	public String getBsaDesc() {
		return bsaDesc;
	}
	public void setBsaDesc(String bsaDesc) {
		this.bsaDesc = bsaDesc;
	}
	public String getBsaDesc1() {
		return bsaDesc1;
	}
	public void setBsaDesc1(String bsaDesc1) {
		this.bsaDesc1 = bsaDesc1;
	}
	public String getBsaDesc2() {
		return bsaDesc2;
	}
	public void setBsaDesc2(String bsaDesc2) {
		this.bsaDesc2 = bsaDesc2;
	}
	public String getBsaDesc3() {
		return bsaDesc3;
	}
	public void setBsaDesc3(String bsaDesc3) {
		this.bsaDesc3 = bsaDesc3;
	}
	public String getBsaDesc4() {
		return bsaDesc4;
	}
	public void setBsaDesc4(String bsaDesc4) {
		this.bsaDesc4 = bsaDesc4;
	}
	public String getBsaDesc5() {
		return bsaDesc5;
	}
	public void setBsaDesc5(String bsaDesc5) {
		this.bsaDesc5 = bsaDesc5;
	}
	public String getBsaDesc6() {
		return bsaDesc6;
	}
	public void setBsaDesc6(String bsaDesc6) {
		this.bsaDesc6 = bsaDesc6;
	}
	public String getDcsName() {
		return dcsName;
	}
	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}
	public String getDclName() {
		return dclName;
	}
	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	
	
	
	
}
