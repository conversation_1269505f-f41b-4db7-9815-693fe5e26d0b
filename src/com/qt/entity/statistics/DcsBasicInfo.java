package com.qt.entity.statistics;

import org.apache.ibatis.type.Alias;

@Alias("/DcsBasicInfo")
public class DcsBasicInfo {
	private Integer dcsBasicId;
    private Integer dcsBasicStationId;//站号
    private Integer dcsBasicDcsId;//站点id
    private Integer dcsBasicDccId;//公司id
    private String dcsUserCharacter;//用户性质
    private String dcsUserType;//用户类别
    private String dcsUserName;//客户名称
    private String dcsPowersupplyCompany;//供电单位
    private String dcsCompanyId;//客户营业户号
    private String dcsRegisterDate;//抄表日期
    private String dcsChargeModeXl;//计费方式-需量
    private String dcsChargeModeRl;//计费方式-容量
    private String dcsJlfs;//计量方式
    private String dcsAdress;//用电地址
    private String dcsSshy;//所属行业
    private String dcsPdfAdress;//配电房地址
    private String dcsPdfLongitude;//配电房经度
    private String dcsPdfLatitude;//配电房纬度
    private String dcsPdglDepartment;//配电管理部门
    private String dcsPdglDepartmentPhone;//配电管理部门电话
    private String dcsPdglZgld;//主管领导
    private String dcsPdglZgldPhone;//主管领导电话
    private String dcsPdglZgldWechat;//主管领导微信号
    private String dcsPdglYdzg;//用电主管
    private String dcsPdglYdzgPhone;//用电主管电话
    private String dcsPdglYdzgWechat;//用电主管微信号
    private String dcsPdglDgbz;//电工班长
    private String dcsPdglDgbzPhone;//电工班长电话
    private String dcsPdglDgbzWechat;//电工班长微信号
    private String dcsCxbds;//出线变电所
    private String dcsPowerNature;//电源性质（主/备）
    private String dcsGdxlmc;//供电线路名称
    private String dcsZx;//专线/T接
    private String dcsGddy;//供电电压
    private String dcsSdrl;//受电容量
	    public DcsBasicInfo(){
	    	
	    }
		public Integer getDcsBasicId() {
			return dcsBasicId;
		}
		public void setDcsBasicId(Integer dcsBasicId) {
			this.dcsBasicId = dcsBasicId;
		}
		public Integer getDcsBasicStationId() {
			return dcsBasicStationId;
		}
		public void setDcsBasicStationId(Integer dcsBasicStationId) {
			this.dcsBasicStationId = dcsBasicStationId;
		}
		public Integer getDcsBasicDcsId() {
			return dcsBasicDcsId;
		}
		public void setDcsBasicDcsId(Integer dcsBasicDcsId) {
			this.dcsBasicDcsId = dcsBasicDcsId;
		}
		public Integer getDcsBasicDccId() {
			return dcsBasicDccId;
		}
		public void setDcsBasicDccId(Integer dcsBasicDccId) {
			this.dcsBasicDccId = dcsBasicDccId;
		}
		public String getDcsUserCharacter() {
			return dcsUserCharacter;
		}
		public void setDcsUserCharacter(String dcsUserCharacter) {
			this.dcsUserCharacter = dcsUserCharacter;
		}
		public String getDcsUserType() {
			return dcsUserType;
		}
		public void setDcsUserType(String dcsUserType) {
			this.dcsUserType = dcsUserType;
		}
		public String getDcsUserName() {
			return dcsUserName;
		}
		public void setDcsUserName(String dcsUserName) {
			this.dcsUserName = dcsUserName;
		}
		public String getDcsPowersupplyCompany() {
			return dcsPowersupplyCompany;
		}
		public void setDcsPowersupplyCompany(String dcsPowersupplyCompany) {
			this.dcsPowersupplyCompany = dcsPowersupplyCompany;
		}
		public String getDcsCompanyId() {
			return dcsCompanyId;
		}
		public void setDcsCompanyId(String dcsCompanyId) {
			this.dcsCompanyId = dcsCompanyId;
		}
		public String getDcsRegisterDate() {
			return dcsRegisterDate;
		}
		public void setDcsRegisterDate(String dcsRegisterDate) {
			this.dcsRegisterDate = dcsRegisterDate;
		}
		public String getDcsChargeModeXl() {
			return dcsChargeModeXl;
		}
		public void setDcsChargeModeXl(String dcsChargeModeXl) {
			this.dcsChargeModeXl = dcsChargeModeXl;
		}
		public String getDcsChargeModeRl() {
			return dcsChargeModeRl;
		}
		public void setDcsChargeModeRl(String dcsChargeModeRl) {
			this.dcsChargeModeRl = dcsChargeModeRl;
		}
		public String getDcsJlfs() {
			return dcsJlfs;
		}
		public void setDcsJlfs(String dcsJlfs) {
			this.dcsJlfs = dcsJlfs;
		}
		public String getDcsAdress() {
			return dcsAdress;
		}
		public void setDcsAdress(String dcsAdress) {
			this.dcsAdress = dcsAdress;
		}
		public String getDcsSshy() {
			return dcsSshy;
		}
		public void setDcsSshy(String dcsSshy) {
			this.dcsSshy = dcsSshy;
		}
		public String getDcsPdfAdress() {
			return dcsPdfAdress;
		}
		public void setDcsPdfAdress(String dcsPdfAdress) {
			this.dcsPdfAdress = dcsPdfAdress;
		}
		public String getDcsPdfLongitude() {
			return dcsPdfLongitude;
		}
		public void setDcsPdfLongitude(String dcsPdfLongitude) {
			this.dcsPdfLongitude = dcsPdfLongitude;
		}
		public String getDcsPdfLatitude() {
			return dcsPdfLatitude;
		}
		public void setDcsPdfLatitude(String dcsPdfLatitude) {
			this.dcsPdfLatitude = dcsPdfLatitude;
		}
		public String getDcsPdglDepartment() {
			return dcsPdglDepartment;
		}
		public void setDcsPdglDepartment(String dcsPdglDepartment) {
			this.dcsPdglDepartment = dcsPdglDepartment;
		}
		public String getDcsPdglDepartmentPhone() {
			return dcsPdglDepartmentPhone;
		}
		public void setDcsPdglDepartmentPhone(String dcsPdglDepartmentPhone) {
			this.dcsPdglDepartmentPhone = dcsPdglDepartmentPhone;
		}
		public String getDcsPdglZgld() {
			return dcsPdglZgld;
		}
		public void setDcsPdglZgld(String dcsPdglZgld) {
			this.dcsPdglZgld = dcsPdglZgld;
		}
		public String getDcsPdglZgldPhone() {
			return dcsPdglZgldPhone;
		}
		public void setDcsPdglZgldPhone(String dcsPdglZgldPhone) {
			this.dcsPdglZgldPhone = dcsPdglZgldPhone;
		}
		public String getDcsPdglZgldWechat() {
			return dcsPdglZgldWechat;
		}
		public void setDcsPdglZgldWechat(String dcsPdglZgldWechat) {
			this.dcsPdglZgldWechat = dcsPdglZgldWechat;
		}
		public String getDcsPdglYdzg() {
			return dcsPdglYdzg;
		}
		public void setDcsPdglYdzg(String dcsPdglYdzg) {
			this.dcsPdglYdzg = dcsPdglYdzg;
		}
		public String getDcsPdglYdzgPhone() {
			return dcsPdglYdzgPhone;
		}
		public void setDcsPdglYdzgPhone(String dcsPdglYdzgPhone) {
			this.dcsPdglYdzgPhone = dcsPdglYdzgPhone;
		}
		public String getDcsPdglYdzgWechat() {
			return dcsPdglYdzgWechat;
		}
		public void setDcsPdglYdzgWechat(String dcsPdglYdzgWechat) {
			this.dcsPdglYdzgWechat = dcsPdglYdzgWechat;
		}
		public String getDcsPdglDgbz() {
			return dcsPdglDgbz;
		}
		public void setDcsPdglDgbz(String dcsPdglDgbz) {
			this.dcsPdglDgbz = dcsPdglDgbz;
		}
		public String getDcsPdglDgbzPhone() {
			return dcsPdglDgbzPhone;
		}
		public void setDcsPdglDgbzPhone(String dcsPdglDgbzPhone) {
			this.dcsPdglDgbzPhone = dcsPdglDgbzPhone;
		}
		public String getDcsPdglDgbzWechat() {
			return dcsPdglDgbzWechat;
		}
		public void setDcsPdglDgbzWechat(String dcsPdglDgbzWechat) {
			this.dcsPdglDgbzWechat = dcsPdglDgbzWechat;
		}
		public String getDcsCxbds() {
			return dcsCxbds;
		}
		public void setDcsCxbds(String dcsCxbds) {
			this.dcsCxbds = dcsCxbds;
		}
		public String getDcsPowerNature() {
			return dcsPowerNature;
		}
		public void setDcsPowerNature(String dcsPowerNature) {
			this.dcsPowerNature = dcsPowerNature;
		}
		public String getDcsGdxlmc() {
			return dcsGdxlmc;
		}
		public void setDcsGdxlmc(String dcsGdxlmc) {
			this.dcsGdxlmc = dcsGdxlmc;
		}
		public String getDcsZx() {
			return dcsZx;
		}
		public void setDcsZx(String dcsZx) {
			this.dcsZx = dcsZx;
		}
		public String getDcsGddy() {
			return dcsGddy;
		}
		public void setDcsGddy(String dcsGddy) {
			this.dcsGddy = dcsGddy;
		}
		public String getDcsSdrl() {
			return dcsSdrl;
		}
		public void setDcsSdrl(String dcsSdrl) {
			this.dcsSdrl = dcsSdrl;
		}
	    
}
