package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-16 22:07:34
 */
@Alias("/StlYear")
public class StlYear {

	// Fields

	private Integer stlId;
	  
	private Integer stlStationId;
	public Integer getStlStationId() {
		return stlStationId;
	}

	public void setStlStationId(Integer stlStationId) {
		this.stlStationId = stlStationId;
	}

	private Integer stlLineId;
	

	private Integer stlAcCode;
	private float stlMax;
	private Date stlMaxTime;
	private float stlMin;
	private Date stlMinTime;
	private float stlAvar;
	private float stlFgc;
	private float stlFgl;
	
	private String dclName;
	
	
	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public Date getStlSyncTime() {
		return stlSyncTime;
	}

	public void setStlSyncTime(Date stlSyncTime) {
		this.stlSyncTime = stlSyncTime;
	}

	private float stlFhl;
	private Integer stlSyncDc;
	private Date stlSyncTime;


	private String dcsName;
	// Constructors

	public Integer getStlAcCode() {
		return stlAcCode;
	}

	public void setStlAcCode(Integer stlAcCode) {
		this.stlAcCode = stlAcCode;
	}

	/** default constructor */
	public StlYear() {
	}

	public Integer getStlId() {
		return stlId;
	}

	public void setStlId(Integer stlId) {
		this.stlId = stlId;
	}

	public Integer getStlLineId() {
		return stlLineId;
	}

	public void setStlLineId(Integer stlLineId) {
		this.stlLineId = stlLineId;
	}

	public float getStlMax() {
		return stlMax;
	}

	public void setStlMax(float stlMax) {
		this.stlMax = stlMax;
	}

	public Date getStlMaxTime() {
		return stlMaxTime;
	}

	public void setStlMaxTime(Date stlMaxTime) {
		this.stlMaxTime = stlMaxTime;
	}

	public float getStlMin() {
		return stlMin;
	}

	public void setStlMin(float stlMin) {
		this.stlMin = stlMin;
	}

	public Date getStlMinTime() {
		return stlMinTime;
	}

	public void setStlMinTime(Date stlMinTime) {
		this.stlMinTime = stlMinTime;
	}

	public float getStlAvar() {
		return stlAvar;
	}

	public void setStlAvar(float stlAvar) {
		this.stlAvar = stlAvar;
	}

	public float getStlFgc() {
		return stlFgc;
	}

	public void setStlFgc(float stlFgc) {
		this.stlFgc = stlFgc;
	}

	public float getStlFgl() {
		return stlFgl;
	}

	public void setStlFgl(float stlFgl) {
		this.stlFgl = stlFgl;
	}

	public float getStlFhl() {
		return stlFhl;
	}

	public void setStlFhl(float stlFhl) {
		this.stlFhl = stlFhl;
	}

	public Integer getStlSyncDc() {
		return stlSyncDc;
	}

	public void setStlSyncDc(Integer stlSyncDc) {
		this.stlSyncDc = stlSyncDc;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

}