package com.qt.entity.statistics;

import org.apache.ibatis.type.Alias;

@Alias("DlUserBasePara")
public class DlUserBasePara {
	private Integer dlIndex;
	private Integer dlUserStationId;
	private Integer dlUserLineId;
	private String dlUserBh;
	private String dlUserXh;
	private Integer dlUserAcId;
	private Integer dlUserJm;
	private float dlUserBzz;
	private float dlUserSdz;
	private String dlUserZskId;
	private String dlUserXw;
	private String dlUserZblb;
	private String dlUserDw;
	
	private String dcsName;//站点名称
	
	private String dclName;//回路名称
	public DlUserBasePara(){
		
	}
	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
	public Integer getDlIndex() {
		return dlIndex;
	}

	public void setDlIndex(Integer dlIndex) {
		this.dlIndex = dlIndex;
	}

	public Integer getDlUserStationId() {
		return dlUserStationId;
	}

	public void setDlUserStationId(Integer dlUserStationId) {
		this.dlUserStationId = dlUserStationId;
	}

	public Integer getDlUserLineId() {
		return dlUserLineId;
	}

	public void setDlUserLineId(Integer dlUserLineId) {
		this.dlUserLineId = dlUserLineId;
	}

	public String getDlUserBh() {
		return dlUserBh;
	}

	public void setDlUserBh(String dlUserBh) {
		this.dlUserBh = dlUserBh;
	}

	public String getDlUserXh() {
		return dlUserXh;
	}

	public void setDlUserXh(String dlUserXh) {
		this.dlUserXh = dlUserXh;
	}

	public Integer getDlUserAcId() {
		return dlUserAcId;
	}

	public void setDlUserAcId(Integer dlUserAcId) {
		this.dlUserAcId = dlUserAcId;
	}

	public Integer getDlUserJm() {
		return dlUserJm;
	}

	public void setDlUserJm(Integer dlUserJm) {
		this.dlUserJm = dlUserJm;
	}


	public float getDlUserBzz() {
		return dlUserBzz;
	}

	public void setDlUserBzz(float dlUserBzz) {
		this.dlUserBzz = dlUserBzz;
	}

	public float getDlUserSdz() {
		return dlUserSdz;
	}

	public void setDlUserSdz(float dlUserSdz) {
		this.dlUserSdz = dlUserSdz;
	}

	public String getDlUserZskId() {
		return dlUserZskId;
	}

	public void setDlUserZskId(String dlUserZskId) {
		this.dlUserZskId = dlUserZskId;
	}
	public String getDlUserXw() {
		return dlUserXw;
	}
	public void setDlUserXw(String dlUserXw) {
		this.dlUserXw = dlUserXw;
	}
	public String getDlUserZblb() {
		return dlUserZblb;
	}
	public void setDlUserZblb(String dlUserZblb) {
		this.dlUserZblb = dlUserZblb;
	}
	public String getDlUserDw() {
		return dlUserDw;
	}
	public void setDlUserDw(String dlUserDw) {
		this.dlUserDw = dlUserDw;
	}
	
}
