package com.qt.entity.statistics;

import java.util.List;

import org.apache.ibatis.type.Alias;

/**
* <AUTHOR> :cuijian
* @version 创建时间：2021年8月27日 上午10:57:09
* 类说明:狼牙数据
*/
@Alias("StoWarnData")
public class StoWarnData {

	private String stoStationId;
	private String stoLineId;
	private String stoSyncTime;
	private float dataCon; //查询的指标值
	private String stationName;
	private String lineName;
	private Double avgNum; //波动指标
	private String assessment; //风险评估
	
	private float maxDeviation;//最大偏差率
	private long countDeviation; //偏差次数
	private String dataType;	//数据的状态 
	
	private List<StoWarnData> warnList;
	
	private List<StoWarnData> dbList; //对比的数据存放处
	
	
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	public List<StoWarnData> getDbList() {
		return dbList;
	}
	public void setDbList(List<StoWarnData> dbList) {
		this.dbList = dbList;
	}
	public float getMaxDeviation() {
		return maxDeviation;
	}
	public void setMaxDeviation(float maxDeviation) {
		this.maxDeviation = maxDeviation;
	}
	public long getCountDeviation() {
		return countDeviation;
	}
	public void setCountDeviation(long countDeviation) {
		this.countDeviation = countDeviation;
	}
	public List<StoWarnData> getWarnList() {
		return warnList;
	}
	public void setWarnList(List<StoWarnData> warnList) {
		this.warnList = warnList;
	}
	public String getStoStationId() {
		return stoStationId;
	}
	public void setStoStationId(String stoStationId) {
		this.stoStationId = stoStationId;
	}
	public String getStoLineId() {
		return stoLineId;
	}
	public void setStoLineId(String stoLineId) {
		this.stoLineId = stoLineId;
	}
	public String getStoSyncTime() {
		return stoSyncTime;
	}
	public void setStoSyncTime(String stoSyncTime) {
		this.stoSyncTime = stoSyncTime;
	}
	public float getDataCon() {
		return dataCon;
	}
	public void setDataCon(float dataCon) {
		this.dataCon = dataCon;
	}
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public String getLineName() {
		return lineName;
	}
	public void setLineName(String lineName) {
		this.lineName = lineName;
	}
	public Double getAvgNum() {
		return avgNum;
	}
	public void setAvgNum(Double avgNum) {
		this.avgNum = avgNum;
	}
	public String getAssessment() {
		return assessment;
	}
	public void setAssessment(String assessment) {
		this.assessment = assessment;
	}
	
	
	
}
