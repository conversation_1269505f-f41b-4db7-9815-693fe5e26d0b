package com.qt.entity.statistics;


import org.apache.ibatis.type.Alias;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2016-11-11 09:47:21
*/
@Alias("StoDataRecord")
public class StoDataRecord{

    // Fields
	private Integer sumdata;
    private Integer stoIndex;
    private Integer stoStationId;
    private Integer stoDeviceId; 
    private Integer stoLineId;
    private Integer stoSyncDb;
    private String stoSyncTime;
    private Float m10001;
    private Float m10002;
    private Float m10003;
    private Float m10004;
    private Float m10005;
    private Float m10006;
    private Float m10007;
    private Float m10008;
    private Float m10009;
    private Float m10010;
    private Float m10011;
    private Float m10012;
    private Float m10013;
    private Float m10014;
    private Float m10015;
    private Float m10016;
    private Float m10017;
    private Float m10018;
    private Float m10019;
    private Float m10020;
    private Float m10021;
    private Float m10022;
    private Float m10023;
    private Float m10024;
    private Float m10025;
    private Float m10026;
    private Float m10027;
    private Float m10028;
    private Float m10029;
    private Float m10030;
    private Float m10031;
    private Float m10032;
    private Float m10033;
    private Float m10034;
    private Float m10035;
    private Float m10036;
    private Float m10037;
    private Float m10038;
    private Float m10039;
    private Float m10040;
    private Float m10041;
    private Float m10042;
    private Float m10043;
    private Float m10044;
    private Float m10045;
    private Float m10046;
    private Float m10047;
    private Float m10048;
    private Float m10049;
    private Float m10050;
    private Float m10051;
    private Float m10052;
    private Float m10053;
    private Float m10054;
    private Float m10055;
    private Float m10056;
    private Float m10057;
    private Float m10058;
    private Float m10059;
    private Float m10060;
    private Float m10061;
    private Float m10062;
    private Float m10063;
    private Float m10064;
    private Float m10065;
    private Float m10066;
    private Float m10067;
    private Float m10068;
    private Float m10069;
    private Float m10070;
    private Float m10071;
    private Float m10072;
    private Float m10073;
    private Float m10074;
    private Float m10075;
    
    private Float m12084;
    private Float m12085;
    private Float m12086;
    private Float m12087;
    private Float m12088;
    private Float m12089;
    private Float m12090;
    private Float m12091;
    private Float m12092;
	
    private Float h10073;//发电量小时值
    private Float d10074;//发电量每月值
    private Float helec;//差值单位发电量
    private Float melec;//月单位发电量
    
    private Float totaldyElec;
    
	public Float getHelec() {
		return helec;
	}

	public void setHelec(Float helec) {
		this.helec = helec;
	}

	public Float getMelec() {
		return melec;
	}

	public void setMelec(Float melec) {
		this.melec = melec;
	}

	public Float getD10074() {
		return d10074;
	}

	public void setD10074(Float d10074) {
		this.d10074 = d10074;
	}

	public Float getH10073() {
		return h10073;
	}

	public void setH10073(Float h10073) {
		this.h10073 = h10073;
	}

	private String dclCapacity;
	private String dclInverter;
	private Float begelec;
	private Float endelec;
	private Float dvalue;
	private Float talYrElec;
    private String dcsName;
    private String dclName;
     
    
    // Constructors

    /** default constructor */
    public StoDataRecord() {
    }
    
	public String getDcsName() {
		return dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public Integer getStoIndex() {
		return stoIndex;
	}

	public Integer getStoStationId() {
		return stoStationId;
	}

	public Integer getStoDeviceId() {
		return stoDeviceId;
	}

	public Integer getStoLineId() {
		return stoLineId;
	}

	public Integer getStoSyncDb() {
		return stoSyncDb;
	}

	public String getStoSyncTime() {
		return stoSyncTime;
	}

	public Float getM10001() {
		return m10001;
	}

	public Float getM10002() {
		return m10002;
	}

	public Float getM10003() {
		return m10003;
	}

	public Float getM10004() {
		return m10004;
	}

	public Float getM10005() {
		return m10005;
	}

	public Float getM10006() {
		return m10006;
	}

	public Float getM10007() {
		return m10007;
	}

	public Float getM10008() {
		return m10008;
	}

	public Float getM10009() {
		return m10009;
	}

	public Float getM10010() {
		return m10010;
	}

	public Float getM10011() {
		return m10011;
	}

	public Float getM10012() {
		return m10012;
	}

	public Float getM10013() {
		return m10013;
	}

	public Float getM10014() {
		return m10014;
	}

	public Float getM10015() {
		return m10015;
	}

	public Float getM10016() {
		return m10016;
	}

	public Float getM10017() {
		return m10017;
	}

	public Float getM10018() {
		return m10018;
	}

	public Float getM10019() {
		return m10019;
	}

	public Float getM10020() {
		return m10020;
	}

	public Float getM10021() {
		return m10021;
	}

	public Float getM10022() {
		return m10022;
	}

	public Float getM10023() {
		return m10023;
	}

	public Float getM10024() {
		return m10024;
	}

	public Float getM10025() {
		return m10025;
	}

	public Float getM10026() {
		return m10026;
	}

	public Float getM10027() {
		return m10027;
	}

	public Float getM10028() {
		return m10028;
	}

	public Float getM10029() {
		return m10029;
	}

	public Float getM10030() {
		return m10030;
	}

	public Float getM10031() {
		return m10031;
	}

	public Float getM10032() {
		return m10032;
	}

	public Float getM10033() {
		return m10033;
	}

	public Float getM10034() {
		return m10034;
	}

	public Float getM10035() {
		return m10035;
	}

	public Float getM10036() {
		return m10036;
	}

	public Float getM10037() {
		return m10037;
	}

	public Float getM10038() {
		return m10038;
	}

	public Float getM10039() {
		return m10039;
	}

	public Float getM10040() {
		return m10040;
	}

	public Float getM10041() {
		return m10041;
	}

	public Float getM10042() {
		return m10042;
	}

	public Float getM10043() {
		return m10043;
	}

	public Float getM10044() {
		return m10044;
	}

	public Float getM10045() {
		return m10045;
	}

	public Float getM10046() {
		return m10046;
	}

	public Float getM10047() {
		return m10047;
	}

	public Float getM10048() {
		return m10048;
	}

	public Float getM10049() {
		return m10049;
	}

	public Float getM10050() {
		return m10050;
	}

	public Float getM10051() {
		return m10051;
	}

	public Float getM10052() {
		return m10052;
	}

	public Float getM10053() {
		return m10053;
	}

	public Float getM10054() {
		return m10054;
	}

	public Float getM10055() {
		return m10055;
	}

	public Float getM10056() {
		return m10056;
	}

	public Float getM10057() {
		return m10057;
	}

	public Float getM10058() {
		return m10058;
	}

	public Float getM10059() {
		return m10059;
	}

	public Float getM10060() {
		return m10060;
	}

	public Float getM10061() {
		return m10061;
	}

	public Float getM10062() {
		return m10062;
	}

	public Float getM10063() {
		return m10063;
	}

	public Float getM10064() {
		return m10064;
	}

	public Float getM10065() {
		return m10065;
	}

	public Float getM10066() {
		return m10066;
	}

	public Float getM10067() {
		return m10067;
	}

	public Float getM10068() {
		return m10068;
	}

	public Float getM10069() {
		return m10069;
	}

	public Float getM10070() {
		return m10070;
	}

	public Float getM10071() {
		return m10071;
	}

	public Float getM10072() {
		return m10072;
	}

	public Float getM10073() {
		return m10073;
	}

	public Float getM10074() {
		return m10074;
	}

	public Float getM10075() {
		return m10075;
	}

	public void setStoIndex(Integer stoIndex) {
		this.stoIndex = stoIndex;
	}

	public void setStoStationId(Integer stoStationId) {
		this.stoStationId = stoStationId;
	}

	public void setStoDeviceId(Integer stoDeviceId) {
		this.stoDeviceId = stoDeviceId;
	}

	public void setStoLineId(Integer stoLineId) {
		this.stoLineId = stoLineId;
	}

	public void setStoSyncDb(Integer stoSyncDb) {
		this.stoSyncDb = stoSyncDb;
	}

	public void setStoSyncTime(String stoSyncTime) {
		this.stoSyncTime = stoSyncTime;
	}

	public void setM10001(Float m10001) {
		this.m10001 = m10001;
	}

	public void setM10002(Float m10002) {
		this.m10002 = m10002;
	}

	public void setM10003(Float m10003) {
		this.m10003 = m10003;
	}

	public void setM10004(Float m10004) {
		this.m10004 = m10004;
	}

	public void setM10005(Float m10005) {
		this.m10005 = m10005;
	}

	public void setM10006(Float m10006) {
		this.m10006 = m10006;
	}

	public void setM10007(Float m10007) {
		this.m10007 = m10007;
	}

	public void setM10008(Float m10008) {
		this.m10008 = m10008;
	}

	public void setM10009(Float m10009) {
		this.m10009 = m10009;
	}

	public void setM10010(Float m10010) {
		this.m10010 = m10010;
	}

	public void setM10011(Float m10011) {
		this.m10011 = m10011;
	}

	public void setM10012(Float m10012) {
		this.m10012 = m10012;
	}

	public void setM10013(Float m10013) {
		this.m10013 = m10013;
	}

	public void setM10014(Float m10014) {
		this.m10014 = m10014;
	}

	public void setM10015(Float m10015) {
		this.m10015 = m10015;
	}

	public void setM10016(Float m10016) {
		this.m10016 = m10016;
	}

	public void setM10017(Float m10017) {
		this.m10017 = m10017;
	}

	public void setM10018(Float m10018) {
		this.m10018 = m10018;
	}

	public void setM10019(Float m10019) {
		this.m10019 = m10019;
	}

	public void setM10020(Float m10020) {
		this.m10020 = m10020;
	}

	public void setM10021(Float m10021) {
		this.m10021 = m10021;
	}

	public void setM10022(Float m10022) {
		this.m10022 = m10022;
	}

	public void setM10023(Float m10023) {
		this.m10023 = m10023;
	}

	public void setM10024(Float m10024) {
		this.m10024 = m10024;
	}

	public void setM10025(Float m10025) {
		this.m10025 = m10025;
	}

	public void setM10026(Float m10026) {
		this.m10026 = m10026;
	}

	public void setM10027(Float m10027) {
		this.m10027 = m10027;
	}

	public void setM10028(Float m10028) {
		this.m10028 = m10028;
	}

	public void setM10029(Float m10029) {
		this.m10029 = m10029;
	}

	public void setM10030(Float m10030) {
		this.m10030 = m10030;
	}

	public void setM10031(Float m10031) {
		this.m10031 = m10031;
	}

	public void setM10032(Float m10032) {
		this.m10032 = m10032;
	}

	public void setM10033(Float m10033) {
		this.m10033 = m10033;
	}

	public void setM10034(Float m10034) {
		this.m10034 = m10034;
	}

	public void setM10035(Float m10035) {
		this.m10035 = m10035;
	}

	public void setM10036(Float m10036) {
		this.m10036 = m10036;
	}

	public void setM10037(Float m10037) {
		this.m10037 = m10037;
	}

	public void setM10038(Float m10038) {
		this.m10038 = m10038;
	}

	public void setM10039(Float m10039) {
		this.m10039 = m10039;
	}

	public void setM10040(Float m10040) {
		this.m10040 = m10040;
	}

	public void setM10041(Float m10041) {
		this.m10041 = m10041;
	}

	public void setM10042(Float m10042) {
		this.m10042 = m10042;
	}

	public void setM10043(Float m10043) {
		this.m10043 = m10043;
	}

	public void setM10044(Float m10044) {
		this.m10044 = m10044;
	}

	public void setM10045(Float m10045) {
		this.m10045 = m10045;
	}

	public void setM10046(Float m10046) {
		this.m10046 = m10046;
	}

	public void setM10047(Float m10047) {
		this.m10047 = m10047;
	}

	public void setM10048(Float m10048) {
		this.m10048 = m10048;
	}

	public void setM10049(Float m10049) {
		this.m10049 = m10049;
	}

	public void setM10050(Float m10050) {
		this.m10050 = m10050;
	}

	public void setM10051(Float m10051) {
		this.m10051 = m10051;
	}

	public void setM10052(Float m10052) {
		this.m10052 = m10052;
	}

	public void setM10053(Float m10053) {
		this.m10053 = m10053;
	}

	public void setM10054(Float m10054) {
		this.m10054 = m10054;
	}

	public void setM10055(Float m10055) {
		this.m10055 = m10055;
	}

	public void setM10056(Float m10056) {
		this.m10056 = m10056;
	}

	public void setM10057(Float m10057) {
		this.m10057 = m10057;
	}

	public void setM10058(Float m10058) {
		this.m10058 = m10058;
	}

	public void setM10059(Float m10059) {
		this.m10059 = m10059;
	}

	public void setM10060(Float m10060) {
		this.m10060 = m10060;
	}

	public void setM10061(Float m10061) {
		this.m10061 = m10061;
	}

	public void setM10062(Float m10062) {
		this.m10062 = m10062;
	}

	public void setM10063(Float m10063) {
		this.m10063 = m10063;
	}

	public void setM10064(Float m10064) {
		this.m10064 = m10064;
	}

	public void setM10065(Float m10065) {
		this.m10065 = m10065;
	}

	public void setM10066(Float m10066) {
		this.m10066 = m10066;
	}

	public void setM10067(Float m10067) {
		this.m10067 = m10067;
	}

	public void setM10068(Float m10068) {
		this.m10068 = m10068;
	}

	public void setM10069(Float m10069) {
		this.m10069 = m10069;
	}

	public void setM10070(Float m10070) {
		this.m10070 = m10070;
	}

	public void setM10071(Float m10071) {
		this.m10071 = m10071;
	}

	public void setM10072(Float m10072) {
		this.m10072 = m10072;
	}

	public void setM10073(Float m10073) {
		this.m10073 = m10073;
	}

	public void setM10074(Float m10074) {
		this.m10074 = m10074;
	}

	public void setM10075(Float m10075) {
		this.m10075 = m10075;
	}

	public Integer getSumdata() {
		return sumdata;
	}

	public void setSumdata(Integer sumdata) {
		this.sumdata = sumdata;
	}

	public String getDclCapacity() {
		return dclCapacity;
	}

	public void setDclCapacity(String dclCapacity) {
		this.dclCapacity = dclCapacity;
	}

	public String getDclInverter() {
		return dclInverter;
	}

	public void setDclInverter(String dclInverter) {
		this.dclInverter = dclInverter;
	}

	public Float getBegelec() {
		return begelec;
	}

	public void setBegelec(Float begelec) {
		this.begelec = begelec;
	}

	public Float getEndelec() {
		return endelec;
	}

	public void setEndelec(Float endelec) {
		this.endelec = endelec;
	}

	public Float getDvalue() {
		return dvalue;
	}

	public void setDvalue(Float dvalue) {
		this.dvalue = dvalue;
	}

	public Float getTalYrElec() {
		return talYrElec;
	}

	public void setTalYrElec(Float talYrElec) {
		this.talYrElec = talYrElec;
	}

	public Float getM12084() {
		return m12084;
	}

	public void setM12084(Float m12084) {
		this.m12084 = m12084;
	}

	public Float getM12085() {
		return m12085;
	}

	public void setM12085(Float m12085) {
		this.m12085 = m12085;
	}

	public Float getM12086() {
		return m12086;
	}

	public void setM12086(Float m12086) {
		this.m12086 = m12086;
	}

	public Float getM12087() {
		return m12087;
	}

	public void setM12087(Float m12087) {
		this.m12087 = m12087;
	}

	public Float getM12088() {
		return m12088;
	}

	public void setM12088(Float m12088) {
		this.m12088 = m12088;
	}

	public Float getM12089() {
		return m12089;
	}

	public void setM12089(Float m12089) {
		this.m12089 = m12089;
	}

	public Float getM12090() {
		return m12090;
	}

	public void setM12090(Float m12090) {
		this.m12090 = m12090;
	}

	public Float getM12091() {
		return m12091;
	}

	public void setM12091(Float m12091) {
		this.m12091 = m12091;
	}

	public Float getM12092() {
		return m12092;
	}

	public void setM12092(Float m12092) {
		this.m12092 = m12092;
	}

	public Float getTotaldyElec() {
		return totaldyElec;
	}

	public void setTotaldyElec(Float totaldyElec) {
		this.totaldyElec = totaldyElec;
	}
}