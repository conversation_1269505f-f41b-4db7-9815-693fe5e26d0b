package com.qt.entity.statistics;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
 * �ô����ɱ���������Զ���� ʱ�䣺2016-08-15 23:36:50
 */
@Alias("StpYear")
public class StpYear {

	// Fields

	private Integer stpId;
	private String stpStationId;
	private String stpLineId;
	private float stpElecValue;
	private Date stpElecTime;
	private Integer stpSyncDc;
	private Date stpSyncTime;
	private String dcsName;
	private String dclName;
	// Constructors

	/** default constructor */
	public StpYear() {
	}

	public Integer getStpId() {
		return stpId;
	}

	public void setStpId(Integer stpId) {
		this.stpId = stpId;
	}

	public String getStpStationId() {
		return stpStationId;
	}

	public void setStpStationId(String stpStationId) {
		this.stpStationId = stpStationId;
	}

	public String getStpLineId() {
		return stpLineId;
	}

	public void setStpLineId(String stpLineId) {
		this.stpLineId = stpLineId;
	}

	public float getStpElecValue() {
		return stpElecValue;
	}

	public void setStpElecValue(float stpElecValue) {
		this.stpElecValue = stpElecValue;
	}

	public Date getStpElecTime() {
		return stpElecTime;
	}

	public void setStpElecTime(Date stpElecTime) {
		this.stpElecTime = stpElecTime;
	}

	public Integer getStpSyncDc() {
		return stpSyncDc;
	}

	public void setStpSyncDc(Integer stpSyncDc) {
		this.stpSyncDc = stpSyncDc;
	}

	public Date getStpSyncTime() {
		return stpSyncTime;
	}

	public void setStpSyncTime(Date stpSyncTime) {
		this.stpSyncTime = stpSyncTime;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

}