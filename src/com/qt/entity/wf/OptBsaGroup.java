package com.qt.entity.wf;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
  *运维指标码分组
  *ʱ�䣺2018-08-29 10:41:03
*/
@Alias("OptBsaGroup")
public class OptBsaGroup{

    // Fields

    private Integer optBsaId;
    private String optBsaGroupName;
    private String optBsaText;
    private String optCreateTime;
    
    // Constructors

    /** default constructor */
    public OptBsaGroup() {
    }

    // Property accessors

    public void setOptBsaId(Integer optBsaId) {
        this.optBsaId = optBsaId;
    }

    public Integer getOptBsaId() {
        return this.optBsaId;
    }

    public void setOptBsaGroupName(String optBsaGroupName) {
        this.optBsaGroupName = optBsaGroupName;
    }

    public String getOptBsaGroupName() {
        return this.optBsaGroupName;
    }

    public void setOptBsaText(String optBsaText) {
        this.optBsaText = optBsaText;
    }

    public String getOptBsaText() {
        return this.optBsaText;
    }

    public void setOptCreateTime(String optCreateTime) {
        this.optCreateTime = optCreateTime;
    }

    public String getOptCreateTime() {
        return this.optCreateTime;
    }

}