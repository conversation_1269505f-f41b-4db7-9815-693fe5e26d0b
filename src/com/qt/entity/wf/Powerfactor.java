package com.qt.entity.wf;

/**
 * <AUTHOR> 李强
 * @date 2017年3月8日18:23:20
 * @功能 存放功率因素与改善幅度的对应关系
 *
 */
public class Powerfactor {
		/**
		 * 功率因素
		 */
		private float power_factor;
		/**
		 * 存放改善幅度
		 */
		private float price;
		
		public float getPower_factor() {
			return power_factor;
		}
		public void setPower_factor(float power_factor) {
			this.power_factor = power_factor;
		}
		public float getPrice() {
			return price;
		}
		public void setPrice(float price) {
			this.price = price;
		}
}
