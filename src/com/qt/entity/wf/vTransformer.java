package com.qt.entity.wf;
/**
 * <AUTHOR> 李强
 * @date 2017年2月23日10:32:55
 * @功能 dcb_byq表（变压器表）关联数据实体层
 *
 */
public class vTransformer {
	
	private String dcb_id;        		//变压器ID
	private String dcb_dcs_id;			//站点ID
	private String dcb_index;  			//回路ID
	private String dcb_dcc_id;			//公司ID
	private Double useElec;  			//使用电量
	private Double supplyElec;    		//提供的电量
	private String dcb_name;			//变压器名字
	private String sto_sync_time;		//数据时间
	public String getDcb_id() {
		return dcb_id;
	}
	public void setDcb_id(String dcb_id) {
		this.dcb_id = dcb_id;
	}
	public String getDcb_dcs_id() {
		return dcb_dcs_id;
	}
	public void setDcb_dcs_id(String dcb_dcs_id) {
		this.dcb_dcs_id = dcb_dcs_id;
	}
	public String getDcb_index() {
		return dcb_index;
	}
	public void setDcb_index(String dcb_index) {
		this.dcb_index = dcb_index;
	}
	public String getDcb_dcc_id() {
		return dcb_dcc_id;
	}
	public void setDcb_dcc_id(String dcb_dcc_id) {
		this.dcb_dcc_id = dcb_dcc_id;
	}
	public Double getUseElec() {
		return useElec;
	}
	public void setUseElec(Double useElec) {
		this.useElec = useElec;
	}
	public Double getSupplyElec() {
		return supplyElec;
	}
	public void setSupplyElec(Double supplyElec) {
		this.supplyElec = supplyElec;
	}
	public String getDcb_name() {
		return dcb_name;
	}
	public void setDcb_name(String dcb_name) {
		this.dcb_name = dcb_name;
	}
	public String getSto_sync_time() {
		return sto_sync_time;
	}
	public void setSto_sync_time(String sto_sync_time) {
		this.sto_sync_time = sto_sync_time;
	}

}
