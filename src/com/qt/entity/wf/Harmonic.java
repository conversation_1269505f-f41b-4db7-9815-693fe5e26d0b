package com.qt.entity.wf;

/**
 * 
 * <AUTHOR> 李强
 * @date 2017年2月23日10:34:34
 * @功能 谐波专用数据实体
 *
 */
public class Harmonic {

	private float AI1;
	private float AI3;
	private float AI5;
	private float AI7;
	private float AI9;
	private float AI11;
	private float AI13;
	private float AI15;
	private float AI17;
	private float AI19;
	private float BI1;
	private float BI3;
	private float BI5;
	private float BI7;
	private float BI9;
	private float BI11;
	private float BI13;
	private float BI15;
	private float BI17;
	private float BI19;
	private float CI1;
	private float CI3;
	private float CI5;
	private float CI7;
	private float CI9;
	private float CI11;
	private float CI13;
	private float CI15;
	private float CI17;
	private float CI19;
	private float AV1;
	private float AV3;
	private float AV5;
	private float AV7;
	private float AV9;
	private float AV11;
	private float AV13;
	private float AV15;
	private float AV17;
	private float AV19;
	private float BV1;
	private float BV3;
	private float BV5;
	private float BV7;
	private float BV9;
	private float BV11;
	private float BV13;
	private float BV15;
	private float BV17;
	private float BV19;
	private float CV1;
	private float CV3;
	private float CV5;
	private float CV7;
	private float CV9;
	private float CV11;
	private float CV13;
	private float CV15;
	private float CV17;
	private float CV19;
	private String sto_sync_time;

	public float getAI1() {
		return AI1;
	}
	public void setAI1(float aI1) {
		AI1 = aI1;
	}
	public float getAI3() {
		return AI3;
	}
	public void setAI3(float aI3) {
		AI3 = aI3;
	}
	public float getAI5() {
		return AI5;
	}
	public void setAI5(float aI5) {
		AI5 = aI5;
	}
	public float getAI7() {
		return AI7;
	}
	public void setAI7(float aI7) {
		AI7 = aI7;
	}
	public float getAI9() {
		return AI9;
	}
	public void setAI9(float aI9) {
		AI9 = aI9;
	}
	public float getAI11() {
		return AI11;
	}
	public void setAI11(float aI11) {
		AI11 = aI11;
	}
	public float getAI13() {
		return AI13;
	}
	public void setAI13(float aI13) {
		AI13 = aI13;
	}
	public float getAI15() {
		return AI15;
	}
	public void setAI15(float aI15) {
		AI15 = aI15;
	}
	public float getAI17() {
		return AI17;
	}
	public void setAI17(float aI17) {
		AI17 = aI17;
	}
	public float getAI19() {
		return AI19;
	}
	public void setAI19(float aI19) {
		AI19 = aI19;
	}
	public float getBI1() {
		return BI1;
	}
	public void setBI1(float bI1) {
		BI1 = bI1;
	}
	public float getBI3() {
		return BI3;
	}
	public void setBI3(float bI3) {
		BI3 = bI3;
	}
	public float getBI5() {
		return BI5;
	}
	public void setBI5(float bI5) {
		BI5 = bI5;
	}
	public float getBI7() {
		return BI7;
	}
	public void setBI7(float bI7) {
		BI7 = bI7;
	}
	public float getBI9() {
		return BI9;
	}
	public void setBI9(float bI9) {
		BI9 = bI9;
	}
	public float getBI11() {
		return BI11;
	}
	public void setBI11(float bI11) {
		BI11 = bI11;
	}
	public float getBI13() {
		return BI13;
	}
	public void setBI13(float bI13) {
		BI13 = bI13;
	}
	public float getBI15() {
		return BI15;
	}
	public void setBI15(float bI15) {
		BI15 = bI15;
	}
	public float getBI17() {
		return BI17;
	}
	public void setBI17(float bI17) {
		BI17 = bI17;
	}
	public float getBI19() {
		return BI19;
	}
	public void setBI19(float bI19) {
		BI19 = bI19;
	}
	public float getCI1() {
		return CI1;
	}
	public void setCI1(float cI1) {
		CI1 = cI1;
	}
	public float getCI3() {
		return CI3;
	}
	public void setCI3(float cI3) {
		CI3 = cI3;
	}
	public float getCI5() {
		return CI5;
	}
	public void setCI5(float cI5) {
		CI5 = cI5;
	}
	public float getCI7() {
		return CI7;
	}
	public void setCI7(float cI7) {
		CI7 = cI7;
	}
	public float getCI9() {
		return CI9;
	}
	public void setCI9(float cI9) {
		CI9 = cI9;
	}
	public float getCI11() {
		return CI11;
	}
	public void setCI11(float cI11) {
		CI11 = cI11;
	}
	public float getCI13() {
		return CI13;
	}
	public void setCI13(float cI13) {
		CI13 = cI13;
	}
	public float getCI15() {
		return CI15;
	}
	public void setCI15(float cI15) {
		CI15 = cI15;
	}
	public float getCI17() {
		return CI17;
	}
	public void setCI17(float cI17) {
		CI17 = cI17;
	}
	public float getCI19() {
		return CI19;
	}
	public void setCI19(float cI19) {
		CI19 = cI19;
	}
	public float getAV1() {
		return AV1;
	}
	public void setAV1(float aV1) {
		AV1 = aV1;
	}
	public float getAV3() {
		return AV3;
	}
	public void setAV3(float aV3) {
		AV3 = aV3;
	}
	public float getAV5() {
		return AV5;
	}
	public void setAV5(float aV5) {
		AV5 = aV5;
	}
	public float getAV7() {
		return AV7;
	}
	public void setAV7(float aV7) {
		AV7 = aV7;
	}
	public float getAV9() {
		return AV9;
	}
	public void setAV9(float aV9) {
		AV9 = aV9;
	}
	public float getAV11() {
		return AV11;
	}
	public void setAV11(float aV11) {
		AV11 = aV11;
	}
	public float getAV13() {
		return AV13;
	}
	public void setAV13(float aV13) {
		AV13 = aV13;
	}
	public float getAV15() {
		return AV15;
	}
	public void setAV15(float aV15) {
		AV15 = aV15;
	}
	public float getAV17() {
		return AV17;
	}
	public void setAV17(float aV17) {
		AV17 = aV17;
	}
	public float getAV19() {
		return AV19;
	}
	public void setAV19(float aV19) {
		AV19 = aV19;
	}
	public float getBV1() {
		return BV1;
	}
	public void setBV1(float bV1) {
		BV1 = bV1;
	}
	public float getBV3() {
		return BV3;
	}
	public void setBV3(float bV3) {
		BV3 = bV3;
	}
	public float getBV5() {
		return BV5;
	}
	public void setBV5(float bV5) {
		BV5 = bV5;
	}
	public float getBV7() {
		return BV7;
	}
	public void setBV7(float bV7) {
		BV7 = bV7;
	}
	public float getBV9() {
		return BV9;
	}
	public void setBV9(float bV9) {
		BV9 = bV9;
	}
	public float getBV11() {
		return BV11;
	}
	public void setBV11(float bV11) {
		BV11 = bV11;
	}
	public float getBV13() {
		return BV13;
	}
	public void setBV13(float bV13) {
		BV13 = bV13;
	}
	public float getBV15() {
		return BV15;
	}
	public void setBV15(float bV15) {
		BV15 = bV15;
	}
	public float getBV17() {
		return BV17;
	}
	public void setBV17(float bV17) {
		BV17 = bV17;
	}
	public float getBV19() {
		return BV19;
	}
	public void setBV19(float bV19) {
		BV19 = bV19;
	}
	public float getCV1() {
		return CV1;
	}
	public void setCV1(float cV1) {
		CV1 = cV1;
	}
	public float getCV3() {
		return CV3;
	}
	public void setCV3(float cV3) {
		CV3 = cV3;
	}
	public float getCV5() {
		return CV5;
	}
	public void setCV5(float cV5) {
		CV5 = cV5;
	}
	public float getCV7() {
		return CV7;
	}
	public void setCV7(float cV7) {
		CV7 = cV7;
	}
	public float getCV9() {
		return CV9;
	}
	public void setCV9(float cV9) {
		CV9 = cV9;
	}
	public float getCV11() {
		return CV11;
	}
	public void setCV11(float cV11) {
		CV11 = cV11;
	}
	public float getCV13() {
		return CV13;
	}
	public void setCV13(float cV13) {
		CV13 = cV13;
	}
	public float getCV15() {
		return CV15;
	}
	public void setCV15(float cV15) {
		CV15 = cV15;
	}
	public float getCV17() {
		return CV17;
	}
	public void setCV17(float cV17) {
		CV17 = cV17;
	}
	public float getCV19() {
		return CV19;
	}
	public void setCV19(float cV19) {
		CV19 = cV19;
	}
	public String getSto_sync_time() {
		return sto_sync_time;
	}
	public void setSto_sync_time(String sto_sync_time) {
		this.sto_sync_time = sto_sync_time;
	}

}
