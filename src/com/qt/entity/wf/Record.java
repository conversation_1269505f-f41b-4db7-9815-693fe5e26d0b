package com.qt.entity.wf;

/**
 * <AUTHOR> 李强
 * @date 2017年3月21日12:23:24
 * @功能 存放记录的实体类
 *
 */
public class Record {

	private String id;					//主键 存放记录编号	
	private String opt_execute_time ;	//存放执行时间
	private String opt_create_name ;	//存放创建人
	private String opt_create_time ;	//存放创建时间
	private String opt_title;			//存放标题
	private String opt_content ;		//存放内容
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getOpt_execute_time() {
		return opt_execute_time;
	}
	public void setOpt_execute_time(String opt_execute_time) {
		this.opt_execute_time = opt_execute_time;
	}
	public String getOpt_create_name() {
		return opt_create_name;
	}
	public void setOpt_create_name(String opt_create_name) {
		this.opt_create_name = opt_create_name;
	}
	public String getOpt_create_time() {
		return opt_create_time;
	}
	public void setOpt_create_time(String opt_create_time) {
		this.opt_create_time = opt_create_time;
	}
	public String getOpt_title() {
		return opt_title;
	}
	public void setOpt_title(String opt_title) {
		this.opt_title = opt_title;
	}
	public String getOpt_content() {
		return opt_content;
	}
	public void setOpt_content(String opt_content) {
		this.opt_content = opt_content;
	}
}
