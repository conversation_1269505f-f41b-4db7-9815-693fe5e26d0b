package com.qt.entity.wf;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
  *运维用户分组
  *ʱ�䣺2018-08-29 10:41:03
*/
@Alias("OptOperatGroup")
public class OptOperatGroup{

    // Fields

    private Integer optId;
    private String optGroupName;
    private String optAlarmLevel;
    private Integer optBsaGroupId;
    private String optCreateTime;
    
    private String optBsaGroupName;// 指标码组名称
    // Constructors

    /** default constructor */
    public OptOperatGroup() {
    }

    // Property accessors

    public void setOptId(Integer optId) {
        this.optId = optId;
    }

    public Integer getOptId() {
        return this.optId;
    }

    public void setOptGroupName(String optGroupName) {
        this.optGroupName = optGroupName;
    }

    public String getOptGroupName() {
        return this.optGroupName;
    }

    public void setOptAlarmLevel(String optAlarmLevel) {
        this.optAlarmLevel = optAlarmLevel;
    }

    public String getOptAlarmLevel() {
        return this.optAlarmLevel;
    }

    public void setOptBsaGroupId(Integer optBsaGroupId) {
        this.optBsaGroupId = optBsaGroupId;
    }

    public Integer getOptBsaGroupId() {
        return this.optBsaGroupId;
    }

    public void setOptCreateTime(String optCreateTime) {
        this.optCreateTime = optCreateTime;
    }

    public String getOptCreateTime() {
        return this.optCreateTime;
    }

	public String getOptBsaGroupName() {
		return optBsaGroupName;
	}

	public void setOptBsaGroupName(String optBsaGroupName) {
		this.optBsaGroupName = optBsaGroupName;
	}

    
}