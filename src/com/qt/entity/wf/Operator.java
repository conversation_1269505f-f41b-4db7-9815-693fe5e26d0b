package com.qt.entity.wf;

/**
 * <AUTHOR> 李强
 * @date 2017年3月20日11:36:18
 * @功能 操作人员实体类    对应表opt_operat_employee
 *
 */
public class Operator {

	private int id;
	private String opt_id;//20180314因运维派单，新增人员编号
	private String opt_name;
	private String opt_sex;
	private String opt_position;
	private String opt_company;
	private String opt_phone;
	private int opt_group; //运维分组编号  
	private int opt_dcc_id; //公司编号
	private String opt_total_time;
	private String opt_total_frequency;
	private String opt_dcs_id;  //关联站点
	
	private String optGroupName;//分组名称   
	private String dccFirstName;//企业简称
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	
	public String getOpt_id() {
		return opt_id;
	}
	public void setOpt_id(String opt_id) {
		this.opt_id = opt_id;
	}
	public String getOpt_name() {
		return opt_name;
	}
	public void setOpt_name(String opt_name) {
		this.opt_name = opt_name;
	}
	public String getOpt_sex() {
		return opt_sex;
	}
	public void setOpt_sex(String opt_sex) {
		this.opt_sex = opt_sex;
	}
	public String getOpt_position() {
		return opt_position;
	}
	public void setOpt_position(String opt_position) {
		this.opt_position = opt_position;
	}

	public String getOpt_company() {
		return opt_company;
	}
	public void setOpt_company(String opt_company) {
		this.opt_company = opt_company;
	}
	public String getOpt_phone() {
		return opt_phone;
	}
	public void setOpt_phone(String opt_phone) {
		this.opt_phone = opt_phone;
	}
	
	public int getOpt_group() {
		return opt_group;
	}
	public void setOpt_group(int opt_group) {
		this.opt_group = opt_group;
	}
	public int getOpt_dcc_id() {
		return opt_dcc_id;
	}
	public void setOpt_dcc_id(int opt_dcc_id) {
		this.opt_dcc_id = opt_dcc_id;
	}
	public String getOpt_dcs_id() {
		return opt_dcs_id;
	}
	public void setOpt_dcs_id(String opt_dcs_id) {
		this.opt_dcs_id = opt_dcs_id;
	}
	public String getOpt_total_time() {
		return opt_total_time;
	}
	public void setOpt_total_time(String opt_total_time) {
		this.opt_total_time = opt_total_time;
	}
	public String getOpt_total_frequency() {
		return opt_total_frequency;
	}
	public void setOpt_total_frequency(String opt_total_frequency) {
		this.opt_total_frequency = opt_total_frequency;
	}
	public String getOptGroupName() {
		return optGroupName;
	}
	public void setOptGroupName(String optGroupName) {
		this.optGroupName = optGroupName;
	}
	public String getDccFirstName() {
		return dccFirstName;
	}
	public void setDccFirstName(String dccFirstName) {
		this.dccFirstName = dccFirstName;
	}
	
	
	
	
}
