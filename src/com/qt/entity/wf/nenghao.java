package com.qt.entity.wf;

import org.apache.ibatis.type.Alias;

/**
 * 节能分析，能效排名
 * <AUTHOR> 
 * @date 2017年2月15日
 *
 */
@Alias("/nenghao")
public class nenghao {

	private int staid; //sto_data_record表 sto_station_id 站点名称
	private int stolinid; //sto_data_record表 sto_stoline_id 回路名称
	private float nenghao; //sto_data_record表 10031  能耗
	private String linename; //dcl_line linename；回路名称
	private int dclid ;	//dcl_line dcl_id 回路id
	private int byqid; //dcb_byq 变压器的id
	private int byqlid;	//dcb_byq 变压器对应的特定回路id
	private int byqstaid;	//dcb_byq 变压器对应的站点id
	private String byqname;	//dcb_byq 变压器的名称
	private String timeduan; //sto_data_record sto_sync_time 时间点
	
	
	public String getTimeduan() {
		return timeduan;
	}
	public void setTimeduan(String timeduan) {
		this.timeduan = timeduan;
	}
	public int getByqid() {
		return byqid;
	}
	public void setByqid(int byqid) {
		this.byqid = byqid;
	}
	public int getByqlid() {
		return byqlid;
	}
	public void setByqlid(int byqlid) {
		this.byqlid = byqlid;
	}
	public int getByqstaid() {
		return byqstaid;
	}
	public void setByqstaid(int byqstaid) {
		this.byqstaid = byqstaid;
	}
	public String getByqname() {
		return byqname;
	}
	public void setByqname(String byqname) {
		this.byqname = byqname;
	}
	public int getStaid() {
		return staid;
	}
	public String getLinename() {
		return linename;
	}
	public void setLinename(String linename) {
		this.linename = linename;
	}
	public void setStaid(int staid) {
		this.staid = staid;
	}
	public int getStolinid() {
		return stolinid;
	}
	public void setStolinid(int stolinid) {
		this.stolinid = stolinid;
	}
	public float getNenghao() {
		return nenghao;
	}
	public void setNenghao(float nenghao) {
		this.nenghao = nenghao;
	}
	public int getDclid() {
		return dclid;
	}
	public void setDclid(int dclid) {
		this.dclid = dclid;
	}
}
