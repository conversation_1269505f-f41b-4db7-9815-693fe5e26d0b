package com.qt.entity.newFile;

/**
 * 
 * <AUTHOR>
 *
 */
public class DcvVideo{

    // Fields

    private Integer vid;
    private String stationId;
    private String videoId;
    private String videoName;
    private String videoType;
    private String installAddress;
    private String videoPort;
    private String liveAddressOne;
    private String liveAddressTwo;
    private String username;
    private String password;
    private String isEnable;
    
    private String stationName;
    
    // Constructors

    /** default constructor */
    public DcvVideo() {
    }

    // Property accessors
    
    public void setVid(Integer vid) {
        this.vid = vid;
    }

    public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public Integer getVid() {
        return this.vid;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getStationId() {
        return this.stationId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getVideoId() {
        return this.videoId;
    }

    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public String getVideoName() {
        return this.videoName;
    }

    public void setVideoType(String videoType) {
        this.videoType = videoType;
    }

    public String getVideoType() {
        return this.videoType;
    }

    public void setInstallAddress(String installAddress) {
        this.installAddress = installAddress;
    }

    public String getInstallAddress() {
        return this.installAddress;
    }

    public void setVideoPort(String videoPort) {
        this.videoPort = videoPort;
    }

    public String getVideoPort() {
        return this.videoPort;
    }

    public void setLiveAddressOne(String liveAddressOne) {
        this.liveAddressOne = liveAddressOne;
    }

    public String getLiveAddressOne() {
        return this.liveAddressOne;
    }

    public void setLiveAddressTwo(String liveAddressTwo) {
        this.liveAddressTwo = liveAddressTwo;
    }

    public String getLiveAddressTwo() {
        return this.liveAddressTwo;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsername() {
        return this.username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword() {
        return this.password;
    }

}