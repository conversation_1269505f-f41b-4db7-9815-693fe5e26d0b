package com.qt.entity.newFile;

/**
 * 
 * <AUTHOR>
 *
 */
public class DcsStationBase{

    // Fields

    private Integer baseId;
    private String stationId;
    private String stationName;
    private String stationAddress;
    private String stationContacts;
    private String lng;
    private String lat;
    private String stationPic;
    private String stationDetail;
    private String createTime;
    private Integer stationType;
    private Integer category;//站点用途类别：1-户用，2-工商业
    
    private Integer dcsProvinceId;
    private Integer dcsCityId;
    private Integer dcsDistrictId;
    
    // Constructors

    /** default constructor */
    public DcsStationBase() {
    }

    // Property accessors

    public void setBaseId(Integer baseId) {
        this.baseId = baseId;
    }

    public Integer getDcsProvinceId() {
		return dcsProvinceId;
	}

	public void setDcsProvinceId(Integer dcsProvinceId) {
		this.dcsProvinceId = dcsProvinceId;
	}

	public Integer getDcsCityId() {
		return dcsCityId;
	}

	public void setDcsCityId(Integer dcsCityId) {
		this.dcsCityId = dcsCityId;
	}

	public Integer getDcsDistrictId() {
		return dcsDistrictId;
	}

	public void setDcsDistrictId(Integer dcsDistrictId) {
		this.dcsDistrictId = dcsDistrictId;
	}

	public Integer getBaseId() {
        return this.baseId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getStationId() {
        return this.stationId;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationName() {
        return this.stationName;
    }

    public void setStationAddress(String stationAddress) {
        this.stationAddress = stationAddress;
    }

    public String getStationAddress() {
        return this.stationAddress;
    }

    public void setStationContacts(String stationContacts) {
        this.stationContacts = stationContacts;
    }

    public String getStationContacts() {
        return this.stationContacts;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLng() {
        return this.lng;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLat() {
        return this.lat;
    }

    public void setStationPic(String stationPic) {
        this.stationPic = stationPic;
    }

    public String getStationPic() {
        return this.stationPic;
    }

    public void setStationDetail(String stationDetail) {
        this.stationDetail = stationDetail;
    }

    public String getStationDetail() {
        return this.stationDetail;
    }

    public void setStationType(Integer stationType) {
        this.stationType = stationType;
    }

    public Integer getStationType() {
        return this.stationType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }
}