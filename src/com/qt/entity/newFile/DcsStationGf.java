package com.qt.entity.newFile;

/**
 * 
 * <AUTHOR>
 *
 */
public class DcsStationGf{

    // Fields

    private Integer id;
    private String stationId;
    private String installCapacity;
    private String buildWay;
    private String systemPic;
    private String threedPic;
    private String electrovalencePlan;
    private String electricalTopology;
    
    private String sunshineId;
    
    private Integer pid;
    private String names;
    private String vals;
    
    private String sunshineHour;
    private String useHour;
    
    // Constructors

    /** default constructor */
    public DcsStationGf() {
    }

    // Property accessors
    
    public void setId(Integer id) {
        this.id = id;
    }

    public String getUseHour() {
		return useHour;
	}

	public void setUseHour(String useHour) {
		this.useHour = useHour;
	}

	public String getSunshineHour() {
		return sunshineHour;
	}

	public void setSunshineHour(String sunshineHour) {
		this.sunshineHour = sunshineHour;
	}

	public String getSunshineId() {
		return sunshineId;
	}

	public void setSunshineId(String sunshineId) {
		this.sunshineId = sunshineId;
	}

	public Integer getPid() {
		return pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	public String getNames() {
		return names;
	}

	public void setNames(String names) {
		this.names = names;
	}

	public String getVals() {
		return vals;
	}

	public void setVals(String vals) {
		this.vals = vals;
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public Integer getId() {
        return this.id;
    }

    public void setInstallCapacity(String installCapacity) {
        this.installCapacity = installCapacity;
    }

    public String getInstallCapacity() {
        return this.installCapacity;
    }

    public void setBuildWay(String buildWay) {
        this.buildWay = buildWay;
    }

    public String getBuildWay() {
        return this.buildWay;
    }

    public void setSystemPic(String systemPic) {
        this.systemPic = systemPic;
    }

    public String getSystemPic() {
        return this.systemPic;
    }

    public void setThreedPic(String threedPic) {
        this.threedPic = threedPic;
    }

    public String getThreedPic() {
        return this.threedPic;
    }

    public void setElectrovalencePlan(String electrovalencePlan) {
        this.electrovalencePlan = electrovalencePlan;
    }

    public String getElectrovalencePlan() {
        return this.electrovalencePlan;
    }

    public void setElectricalTopology(String electricalTopology) {
        this.electricalTopology = electricalTopology;
    }

    public String getElectricalTopology() {
        return this.electricalTopology;
    }

}