package com.qt.entity.newFile;

/**
 * 
 * <AUTHOR>
 *
 */
public class DceEquipment{

    // Fields

    private String equId;
    private String equName;
    private String nums;
    private String bsaId;
    private String gatewayId;
    
    private String stationId;
    private String stationName;
    private String gatewayName;
    private String bsaName;
    
    private String equType;
    private String equTypeName;
    
    // Constructors

    /** default constructor */
    public DceEquipment() {
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }
// Property accessors
    
    public void setEquId(String equId) {
        this.equId = equId;
    }

    public String getEquTypeName() {
		return equTypeName;
	}

	public void setEquTypeName(String equTypeName) {
		this.equTypeName = equTypeName;
	}

	public String getEquType() {
		return equType;
	}

	public void setEquType(String equType) {
		this.equType = equType;
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public String getBsaName() {
		return bsaName;
	}

	public void setBsaName(String bsaName) {
		this.bsaName = bsaName;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public String getEquId() {
        return this.equId;
    }

    public void setEquName(String equName) {
        this.equName = equName;
    }

    public String getEquName() {
        return this.equName;
    }

    public void setNums(String nums) {
        this.nums = nums;
    }

    public String getNums() {
        return this.nums;
    }

    public void setBsaId(String bsaId) {
        this.bsaId = bsaId;
    }

    public String getBsaId() {
        return this.bsaId;
    }

    public void setGatewayId(String gatewayId) {
        this.gatewayId = gatewayId;
    }

    public String getGatewayId() {
        return this.gatewayId;
    }

}