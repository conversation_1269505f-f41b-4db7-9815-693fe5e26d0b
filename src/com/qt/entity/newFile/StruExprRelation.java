package com.qt.entity.newFile;

/**
 * 配置表达式公式
  *2021-07-23 18:10:12
*/
public class StruExprRelation {

    // Fields

    private Integer relationIndex;
    private String realtionExprId;
    private String relationId;
    private Float relationCoefficient;
    private String relationName;
    
    // Constructors

    /** default constructor */
    public StruExprRelation() {
    }

    // Property accessors


    public String getRelationName() {
        return relationName;
    }

    public void setRelationName(String relationName) {
        this.relationName = relationName;
    }

    public void setRelationIndex(Integer relationIndex) {
        this.relationIndex = relationIndex;
    }

    public Integer getRelationIndex() {
        return this.relationIndex;
    }

    public void setRealtionExprId(String realtionExprId) {
        this.realtionExprId = realtionExprId;
    }

    public String getRealtionExprId() {
        return this.realtionExprId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getRelationId() {
        return this.relationId;
    }

    public void setRelationCoefficient(Float relationCoefficient) {
        this.relationCoefficient = relationCoefficient;
    }

    public Float getRelationCoefficient() {
        return this.relationCoefficient;
    }

}