package com.qt.entity.newFile;

/**
 * 
 * <AUTHOR>
 * 2021-09-14 14:17:11
 *
 */
public class DcsStationPd{

    // Fields

    private Integer id;
    private String voltType;
    private String supplyWay;
    private String billMode;
    private String registerDate;
    private String systemPic;
    private String threedPic;
    private String electrovalencePlan;
    private String electricalTopology;
    
    // Constructors

    /** default constructor */
    public DcsStationPd() {
    }

    // Property accessors

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return this.id;
    }

    public void setVoltType(String voltType) {
        this.voltType = voltType;
    }

    public String getVoltType() {
        return this.voltType;
    }

    public void setSupplyWay(String supplyWay) {
        this.supplyWay = supplyWay;
    }

    public String getSupplyWay() {
        return this.supplyWay;
    }

    public void setBillMode(String billMode) {
        this.billMode = billMode;
    }

    public String getBillMode() {
        return this.billMode;
    }

    public void setRegisterDate(String registerDate) {
        this.registerDate = registerDate;
    }

    public String getRegisterDate() {
        return this.registerDate;
    }

    public void setSystemPic(String systemPic) {
        this.systemPic = systemPic;
    }

    public String getSystemPic() {
        return this.systemPic;
    }

    public void setThreedPic(String threedPic) {
        this.threedPic = threedPic;
    }

    public String getThreedPic() {
        return this.threedPic;
    }

    public void setElectrovalencePlan(String electrovalencePlan) {
        this.electrovalencePlan = electrovalencePlan;
    }

    public String getElectrovalencePlan() {
        return this.electrovalencePlan;
    }

    public void setElectricalTopology(String electricalTopology) {
        this.electricalTopology = electricalTopology;
    }

    public String getElectricalTopology() {
        return this.electricalTopology;
    }

}