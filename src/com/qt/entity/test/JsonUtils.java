package com.qt.entity.test;

import java.io.FileReader;  
import java.io.FileWriter;  
import java.io.IOException;  
import java.io.StringWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.codehaus.jackson.JsonFactory;  
import org.codehaus.jackson.JsonGenerationException;  
import org.codehaus.jackson.JsonGenerator;  
import org.codehaus.jackson.JsonNode;  
import org.codehaus.jackson.JsonParseException;  
import org.codehaus.jackson.map.JsonMappingException;  
import org.codehaus.jackson.map.ObjectMapper;  
import org.codehaus.jackson.map.SerializationConfig;  
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.support.GenericXmlApplicationContext;

import com.qt.service.statistics.EvrAlarmService;
import com.qt.servlet.GjInsertUtil;
import com.qt.servlet.SocketList;

  
/** 
 * Json工具类，实现JSON与Java Bean的互相转换  
 * User: shijing 
 * <span style="font-family: Arial, Helvetica, sans-serif;">2015年4月3日上午10:42:19</span> 
 */  
public class JsonUtils implements ApplicationContextAware  {  
  
	private static ApplicationContext appCtx;
	
	public static ApplicationContext getApplicationContext() {
        return appCtx;
    }

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appCtx = applicationContext;
    }
	
	public static Object getBean(String beanName) {
        return appCtx.getBean(beanName);
    }
	
	

	private static ObjectMapper objectMapper = new ObjectMapper();  
    private static JsonFactory jsonFactory = new JsonFactory();  
  
    static {  
        objectMapper.configure(SerializationConfig.Feature.WRITE_NULL_MAP_VALUES, false);  
       // objectMapper.setSerializationInclusion(JsonSerialize.Inclusion.NON_NULL);  
    }  
  
    /** 
     * 泛型返回，json字符串转对象 
     * 2015年4月3日上午10:42:19 
     * auther:shijing 
     * @param jsonAsString 
     * @param pojoClass 
     * @return 
     * @throws JsonMappingException 
     * @throws JsonParseException 
     * @throws IOException 
     */  
    public static <T> T fromJson(String jsonAsString, Class<T> pojoClass) throws JsonMappingException,  
            JsonParseException, IOException {  
        return objectMapper.readValue(jsonAsString, pojoClass);  
    }  
  
    public static <T> T fromJson(FileReader fr, Class<T> pojoClass) throws JsonParseException, IOException {  
        return objectMapper.readValue(fr, pojoClass);  
    }  
  
    /** 
     * Object对象转json 
     * 2015年4月3日上午10:41:53 
     * auther:shijing 
     * @param pojo 
     * @return 
     * @throws JsonMappingException 
     * @throws JsonGenerationException 
     * @throws IOException 
     */  
    public static String toJson(Object pojo) throws JsonMappingException, JsonGenerationException, IOException {  
        return toJson(pojo, false);  
    }  
  
    public static String toJson(Object pojo, boolean prettyPrint) throws JsonMappingException, JsonGenerationException,  
            IOException {  
        StringWriter sw = new StringWriter();  
        JsonGenerator jg = jsonFactory.createJsonGenerator(sw);  
        if (prettyPrint) {  
            jg.useDefaultPrettyPrinter();  
        }  
        objectMapper.writeValue(jg, pojo);  
        return sw.toString();  
    }  
  
    public static void toJson(Object pojo, FileWriter fw, boolean prettyPrint) throws JsonMappingException,  
            JsonGenerationException, IOException {  
        JsonGenerator jg = jsonFactory.createJsonGenerator(fw);  
        if (prettyPrint) {  
            jg.useDefaultPrettyPrinter();  
        }  
        objectMapper.writeValue(jg, pojo);  
    }  
  
    /** 
     * json字符串转Map 
     * 2015年4月3日上午10:41:25 
     * auther:shijing 
     * @param jsonStr 
     * @return 
     * @throws IOException 
     * @throws ParseException 
     */  
    public static Map<String, Object> parseMap(String jsonStr) throws IOException, ParseException {  
        Map<String, Object> map = objectMapper.readValue(jsonStr, Map.class);  
        System.out.println(map);
//        Map<String, Object> params = new HashMap<String, Object>();
        String evhAlarmBianhao = map.get("alarmid").toString();
        String evrAlarmBianhao = map.get("alarmid").toString();
        System.out.println("报警的编号:"+map.get("alarmid"));
        System.out.println("表明传送的是报警数据:"+map.get("alarmtype"));
       
        //数据解析插入
        String data = map.get("data").toString();
        System.out.println("报警数据字符串待解析:"+data);
        
        
        String evhOcTime = "";
        String evhRcTime = "";
        String evhDesc = "";
        String evrOcTime = "";
        String evrDesc = "";
        String evrLevel = "";
        String evrNowValue = "";
        String evrAlarmValue = "";
        String evrReturnValue = "";
        String evhNowValue = "";
        String evhAlarmValue = "";
        String evhReturnValue = "";
      
        String [] stringArr = data.split(",");  
        int i = 0;
        for (String string : stringArr) {
        	i++;
        	if (i==1) {
        		evhOcTime = string;
        		evrOcTime = string;
        		evhRcTime = string;
        		System.out.println("告警时间:"+string);
			}
        	if (i==2) {
        		System.out.println("变量名:"+string);
			}
        	if (i==3) {
        		System.out.println("告警类型:"+string);
			}
        	if (i==4) {
        		evhDesc = string;
        		evrDesc = string;
        		System.out.println("描述:"+string);
			}
        	if (i==5) {
        		evrLevel = string;
        		System.out.println("级别:"+string);
			}
        	if (i==6) {
        		evrNowValue = string;
        		evhNowValue = string;
        		System.out.println("当前值:"+string);
			}
        	if (i==7) {
        		evrAlarmValue = string;
        		evhAlarmValue = string;
        		System.out.println("告警值:"+string);
			}
        	if (i==8) {
        		evrReturnValue = string;
        		evhReturnValue = string;
        		System.out.println("恢复值:"+string);
			}
			
		}
        String evhStationId = map.get("istation").toString();
        String evrStationId = map.get("istation").toString();
        
        System.out.println("站号（项目编号）:"+map.get("istation"));
        
        String evhLineId = map.get("lineid").toString();
        String evrLineId = map.get("lineid").toString();

        System.out.println("回路编号:"+map.get("lineid"));
        System.out.println("表明传送的是报警数据"+map.get("type"));
        
        String evhRcType = "1";
        		
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String evhRcId = sdf.format(date);
        String evrTime = sdf.format(date);
        
        String evhBsaCode = map.get("bscode").toString();
        String evrBsaCode = map.get("bscode").toString();
        
        System.out.println("采集编号"+map.get("bscode"));
        
        SocketList socketList = new SocketList();
        socketList.add(evrStationId,evrLineId,evrBsaCode,evrDesc,evrOcTime,evrLevel,evrAlarmBianhao,evrTime,evrNowValue,evrAlarmValue,evrReturnValue);
        
//        GjInsertUtil gjInsertUtil = new GjInsertUtil();
//        gjInsertUtil.save(evrStationId,evrLineId,evrBsaCode,evrDesc,evrOcTime,evrLevel,evrAlarmBianhao,evrTime,evrNowValue,evrAlarmValue,evrReturnValue);
        return map;  
    }  
    
    public static JsonNode parse(String jsonStr) throws IOException {  
        JsonNode node = null;  
        node = objectMapper.readTree(jsonStr);  
        System.out.println(node.getTextValue());
        return node;  
    }  
  
    public static ObjectMapper getObjectMapper() {  
        return objectMapper;  
    }  

    public static void main(String[] args) throws IOException, ParseException {
    	  String jsonStr = "{\"alarmid\":\"0000100100300036\",\"alarmtype\":1,\"data\":\"2016-10-08 14:10:12,var36,越限告警,10017 越上限 恢复,0,1725.60,,111.6\",\"istation\":1,\"lineid\":1003,\"type\":\"alarmdata\"}";  
    		 
    	
    	  JsonUtils.parseMap(jsonStr);
	}
  
}  