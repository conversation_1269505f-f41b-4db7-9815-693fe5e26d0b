package com.qt.entity.customModel;

import org.apache.ibatis.type.Alias;

@Alias("StoUserVarRealData")
public class StoUserVarRealData {
	private Integer userIndex;
	
	private Integer userStationId;
	
	private Integer userLineId;
	
	private Integer userSyncDb;
	
	private String userSyncTime;
	
	private Float S_ZYA;
	
	private Float S_ZYB;
	
	private Float S_ZYC;
	
	private Float S_ZYZ;

	private Float S_ZWA;
	
	private Float S_ZWB;
	
	private Float S_ZWC;
	
	private Float S_ZWZ;

	private Float S_FYA;
	
	private Float S_FYB;
	
	private Float S_FYC;
	
	private Float S_FYZ;
	
	private Float S_TA;
	
	private Float S_TB;
	
	private Float S_TC;
	
	private Float S_SA;
	
	private Float S_SB;
	
	private Float S_SC;
	
	private Float S_SZ;
	
	private Float S_GA;
	
	private Float S_GB;
	
	private Float S_GC;
	
	private Float S_GZ;
	
	private Float S_IA;
	
	private Float S_IB;
	
	private Float S_IC;

	private Float S_VA;
	
	private Float S_VB;
	
	private Float S_VC;
	
	private Float S_XIA1;

	private Float S_XIB1;
	
	private Float S_XIC1;

	private Float S_XIA3;

	private Float S_XIB3;
	
	private Float S_XIC3;

	private Float S_XIA5;

	private Float S_XIB5;
	
	private Float S_XIC5;

	private Float S_XIA7;

	private Float S_XIB7;
	
	private Float S_XIC7;

	private Float S_XIA9;

	private Float S_XIB9;
	
	private Float S_XIC9;

	private Float S_XIA11;

	private Float S_XIB11;
	
	private Float S_XIC11;
	
	private Float S_XIA13;

	private Float S_XIB13;
	
	private Float S_XIC13;
	
	private Float S_XIA15;

	private Float S_XIB15;
	
	private Float S_XIC15;
	
	private Float S_XIA17;

	private Float S_XIB17;
	
	private Float S_XIC17;

	private Float S_XIA19;

	private Float S_XIB19;
	
	private Float S_XIC19;

	private Float S_XIA21;

	private Float S_XIB21;
	
	private Float S_XIC21;
	
	private Float S_XIA23;

	private Float S_XIB23;
	
	private Float S_XIC23;
	
	private Float S_XIA25;

	private Float S_XIB25;
	
	private Float S_XIC25;
	
	private Float S_XIA27;

	private Float S_XIB27;
	
	private Float S_XIC27;

	private Float S_XIA29;

	private Float S_XIB29;
	
	private Float S_XIC29;

	private Float S_XIA31;

	private Float S_XIB31;
	
	private Float S_XIC31;
	
	private String dcsName;
	
	private String dclName;
	
	public StoUserVarRealData(Integer userIndex, Integer userStationId, Integer userLineId, Integer userSyncDb,
			String userSyncTime, Float s_ZYA, Float s_ZYB, Float s_ZYC, Float s_ZYZ, Float s_ZWA, Float s_ZWB,
			Float s_ZWC, Float s_ZWZ, Float s_FYA, Float s_FYB, Float s_FYC, Float s_FYZ, Float s_TA, Float s_TB,
			Float s_TC, Float s_SA, Float s_SB, Float s_SC, Float s_SZ, Float s_GA, Float s_GB, Float s_GC, Float s_GZ,
			Float s_IA, Float s_IB, Float s_IC, Float s_VA, Float s_VB, Float s_VC, Float s_XIA1, Float s_XIB1,
			Float s_XIC1, Float s_XIA3, Float s_XIB3, Float s_XIC3, Float s_XIA5, Float s_XIB5, Float s_XIC5,
			Float s_XIA7, Float s_XIB7, Float s_XIC7, Float s_XIA9, Float s_XIB9, Float s_XIC9, Float s_XIA11,
			Float s_XIB11, Float s_XIC11, Float s_XIA13, Float s_XIB13, Float s_XIC13, Float s_XIA15, Float s_XIB15,
			Float s_XIC15, Float s_XIA17, Float s_XIB17, Float s_XIC17, Float s_XIA19, Float s_XIB19, Float s_XIC19,
			Float s_XIA21, Float s_XIB21, Float s_XIC21, Float s_XIA23, Float s_XIB23, Float s_XIC23, Float s_XIA25,
			Float s_XIB25, Float s_XIC25, Float s_XIA27, Float s_XIB27, Float s_XIC27, Float s_XIA29, Float s_XIB29,
			Float s_XIC29, Float s_XIA31, Float s_XIB31, Float s_XIC31,String dcsName,String dclName) {
		super();
		this.userIndex = userIndex;
		this.userStationId = userStationId;
		this.userLineId = userLineId;
		this.userSyncDb = userSyncDb;
		this.userSyncTime = userSyncTime;
		this.dcsName = dcsName;
		this.dclName = dclName;
		S_ZYA = s_ZYA;
		S_ZYB = s_ZYB;
		S_ZYC = s_ZYC;
		S_ZYZ = s_ZYZ;
		S_ZWA = s_ZWA;
		S_ZWB = s_ZWB;
		S_ZWC = s_ZWC;
		S_ZWZ = s_ZWZ;
		S_FYA = s_FYA;
		S_FYB = s_FYB;
		S_FYC = s_FYC;
		S_FYZ = s_FYZ;
		S_TA = s_TA;
		S_TB = s_TB;
		S_TC = s_TC;
		S_SA = s_SA;
		S_SB = s_SB;
		S_SC = s_SC;
		S_SZ = s_SZ;
		S_GA = s_GA;
		S_GB = s_GB;
		S_GC = s_GC;
		S_GZ = s_GZ;
		S_IA = s_IA;
		S_IB = s_IB;
		S_IC = s_IC;
		S_VA = s_VA;
		S_VB = s_VB;
		S_VC = s_VC;
		S_XIA1 = s_XIA1;
		S_XIB1 = s_XIB1;
		S_XIC1 = s_XIC1;
		S_XIA3 = s_XIA3;
		S_XIB3 = s_XIB3;
		S_XIC3 = s_XIC3;
		S_XIA5 = s_XIA5;
		S_XIB5 = s_XIB5;
		S_XIC5 = s_XIC5;
		S_XIA7 = s_XIA7;
		S_XIB7 = s_XIB7;
		S_XIC7 = s_XIC7;
		S_XIA9 = s_XIA9;
		S_XIB9 = s_XIB9;
		S_XIC9 = s_XIC9;
		S_XIA11 = s_XIA11;
		S_XIB11 = s_XIB11;
		S_XIC11 = s_XIC11;
		S_XIA13 = s_XIA13;
		S_XIB13 = s_XIB13;
		S_XIC13 = s_XIC13;
		S_XIA15 = s_XIA15;
		S_XIB15 = s_XIB15;
		S_XIC15 = s_XIC15;
		S_XIA17 = s_XIA17;
		S_XIB17 = s_XIB17;
		S_XIC17 = s_XIC17;
		S_XIA19 = s_XIA19;
		S_XIB19 = s_XIB19;
		S_XIC19 = s_XIC19;
		S_XIA21 = s_XIA21;
		S_XIB21 = s_XIB21;
		S_XIC21 = s_XIC21;
		S_XIA23 = s_XIA23;
		S_XIB23 = s_XIB23;
		S_XIC23 = s_XIC23;
		S_XIA25 = s_XIA25;
		S_XIB25 = s_XIB25;
		S_XIC25 = s_XIC25;
		S_XIA27 = s_XIA27;
		S_XIB27 = s_XIB27;
		S_XIC27 = s_XIC27;
		S_XIA29 = s_XIA29;
		S_XIB29 = s_XIB29;
		S_XIC29 = s_XIC29;
		S_XIA31 = s_XIA31;
		S_XIB31 = s_XIB31;
		S_XIC31 = s_XIC31;
	}
	
	public StoUserVarRealData(){
		super();
	}
	
	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public Integer getUserIndex() {
		return userIndex;
	}

	public void setUserIndex(Integer userIndex) {
		this.userIndex = userIndex;
	}

	public Integer getUserStationId() {
		return userStationId;
	}

	public void setUserStationId(Integer userStationId) {
		this.userStationId = userStationId;
	}

	public Integer getUserLineId() {
		return userLineId;
	}

	public void setUserLineId(Integer userLineId) {
		this.userLineId = userLineId;
	}

	public Integer getUserSyncDb() {
		return userSyncDb;
	}

	public void setUserSyncDb(Integer userSyncDb) {
		this.userSyncDb = userSyncDb;
	}

	public String getUserSyncTime() {
		return userSyncTime;
	}

	public void setUserSyncTime(String userSyncTime) {
		this.userSyncTime = userSyncTime;
	}

	public Float getS_ZYA() {
		return S_ZYA;
	}

	public void setS_ZYA(Float s_ZYA) {
		S_ZYA = s_ZYA;
	}

	public Float getS_ZYB() {
		return S_ZYB;
	}

	public void setS_ZYB(Float s_ZYB) {
		S_ZYB = s_ZYB;
	}

	public Float getS_ZYC() {
		return S_ZYC;
	}

	public void setS_ZYC(Float s_ZYC) {
		S_ZYC = s_ZYC;
	}

	public Float getS_ZYZ() {
		return S_ZYZ;
	}

	public void setS_ZYZ(Float s_ZYZ) {
		S_ZYZ = s_ZYZ;
	}

	public Float getS_ZWA() {
		return S_ZWA;
	}

	public void setS_ZWA(Float s_ZWA) {
		S_ZWA = s_ZWA;
	}

	public Float getS_ZWB() {
		return S_ZWB;
	}

	public void setS_ZWB(Float s_ZWB) {
		S_ZWB = s_ZWB;
	}

	public Float getS_ZWC() {
		return S_ZWC;
	}

	public void setS_ZWC(Float s_ZWC) {
		S_ZWC = s_ZWC;
	}

	public Float getS_ZWZ() {
		return S_ZWZ;
	}

	public void setS_ZWZ(Float s_ZWZ) {
		S_ZWZ = s_ZWZ;
	}

	public Float getS_FYA() {
		return S_FYA;
	}

	public void setS_FYA(Float s_FYA) {
		S_FYA = s_FYA;
	}

	public Float getS_FYB() {
		return S_FYB;
	}

	public void setS_FYB(Float s_FYB) {
		S_FYB = s_FYB;
	}

	public Float getS_FYC() {
		return S_FYC;
	}

	public void setS_FYC(Float s_FYC) {
		S_FYC = s_FYC;
	}

	public Float getS_FYZ() {
		return S_FYZ;
	}

	public void setS_FYZ(Float s_FYZ) {
		S_FYZ = s_FYZ;
	}

	public Float getS_TA() {
		return S_TA;
	}

	public void setS_TA(Float s_TA) {
		S_TA = s_TA;
	}

	public Float getS_TB() {
		return S_TB;
	}

	public void setS_TB(Float s_TB) {
		S_TB = s_TB;
	}

	public Float getS_TC() {
		return S_TC;
	}

	public void setS_TC(Float s_TC) {
		S_TC = s_TC;
	}

	public Float getS_SA() {
		return S_SA;
	}

	public void setS_SA(Float s_SA) {
		S_SA = s_SA;
	}

	public Float getS_SB() {
		return S_SB;
	}

	public void setS_SB(Float s_SB) {
		S_SB = s_SB;
	}

	public Float getS_SC() {
		return S_SC;
	}

	public void setS_SC(Float s_SC) {
		S_SC = s_SC;
	}

	public Float getS_SZ() {
		return S_SZ;
	}

	public void setS_SZ(Float s_SZ) {
		S_SZ = s_SZ;
	}

	public Float getS_GA() {
		return S_GA;
	}

	public void setS_GA(Float s_GA) {
		S_GA = s_GA;
	}

	public Float getS_GB() {
		return S_GB;
	}

	public void setS_GB(Float s_GB) {
		S_GB = s_GB;
	}

	public Float getS_GC() {
		return S_GC;
	}

	public void setS_GC(Float s_GC) {
		S_GC = s_GC;
	}

	public Float getS_GZ() {
		return S_GZ;
	}

	public void setS_GZ(Float s_GZ) {
		S_GZ = s_GZ;
	}

	public Float getS_IA() {
		return S_IA;
	}

	public void setS_IA(Float s_IA) {
		S_IA = s_IA;
	}

	public Float getS_IB() {
		return S_IB;
	}

	public void setS_IB(Float s_IB) {
		S_IB = s_IB;
	}

	public Float getS_IC() {
		return S_IC;
	}

	public void setS_IC(Float s_IC) {
		S_IC = s_IC;
	}

	public Float getS_VA() {
		return S_VA;
	}

	public void setS_VA(Float s_VA) {
		S_VA = s_VA;
	}

	public Float getS_VB() {
		return S_VB;
	}

	public void setS_VB(Float s_VB) {
		S_VB = s_VB;
	}

	public Float getS_VC() {
		return S_VC;
	}

	public void setS_VC(Float s_VC) {
		S_VC = s_VC;
	}

	public Float getS_XIA1() {
		return S_XIA1;
	}

	public void setS_XIA1(Float s_XIA1) {
		S_XIA1 = s_XIA1;
	}

	public Float getS_XIB1() {
		return S_XIB1;
	}

	public void setS_XIB1(Float s_XIB1) {
		S_XIB1 = s_XIB1;
	}

	public Float getS_XIC1() {
		return S_XIC1;
	}

	public void setS_XIC1(Float s_XIC1) {
		S_XIC1 = s_XIC1;
	}

	public Float getS_XIA3() {
		return S_XIA3;
	}

	public void setS_XIA3(Float s_XIA3) {
		S_XIA3 = s_XIA3;
	}

	public Float getS_XIB3() {
		return S_XIB3;
	}

	public void setS_XIB3(Float s_XIB3) {
		S_XIB3 = s_XIB3;
	}

	public Float getS_XIC3() {
		return S_XIC3;
	}

	public void setS_XIC3(Float s_XIC3) {
		S_XIC3 = s_XIC3;
	}

	public Float getS_XIA5() {
		return S_XIA5;
	}

	public void setS_XIA5(Float s_XIA5) {
		S_XIA5 = s_XIA5;
	}

	public Float getS_XIB5() {
		return S_XIB5;
	}

	public void setS_XIB5(Float s_XIB5) {
		S_XIB5 = s_XIB5;
	}

	public Float getS_XIC5() {
		return S_XIC5;
	}

	public void setS_XIC5(Float s_XIC5) {
		S_XIC5 = s_XIC5;
	}

	public Float getS_XIA7() {
		return S_XIA7;
	}

	public void setS_XIA7(Float s_XIA7) {
		S_XIA7 = s_XIA7;
	}

	public Float getS_XIB7() {
		return S_XIB7;
	}

	public void setS_XIB7(Float s_XIB7) {
		S_XIB7 = s_XIB7;
	}

	public Float getS_XIC7() {
		return S_XIC7;
	}

	public void setS_XIC7(Float s_XIC7) {
		S_XIC7 = s_XIC7;
	}

	public Float getS_XIA9() {
		return S_XIA9;
	}

	public void setS_XIA9(Float s_XIA9) {
		S_XIA9 = s_XIA9;
	}

	public Float getS_XIB9() {
		return S_XIB9;
	}

	public void setS_XIB9(Float s_XIB9) {
		S_XIB9 = s_XIB9;
	}

	public Float getS_XIC9() {
		return S_XIC9;
	}

	public void setS_XIC9(Float s_XIC9) {
		S_XIC9 = s_XIC9;
	}

	public Float getS_XIA11() {
		return S_XIA11;
	}

	public void setS_XIA11(Float s_XIA11) {
		S_XIA11 = s_XIA11;
	}

	public Float getS_XIB11() {
		return S_XIB11;
	}

	public void setS_XIB11(Float s_XIB11) {
		S_XIB11 = s_XIB11;
	}

	public Float getS_XIC11() {
		return S_XIC11;
	}

	public void setS_XIC11(Float s_XIC11) {
		S_XIC11 = s_XIC11;
	}

	public Float getS_XIA13() {
		return S_XIA13;
	}

	public void setS_XIA13(Float s_XIA13) {
		S_XIA13 = s_XIA13;
	}

	public Float getS_XIB13() {
		return S_XIB13;
	}

	public void setS_XIB13(Float s_XIB13) {
		S_XIB13 = s_XIB13;
	}

	public Float getS_XIC13() {
		return S_XIC13;
	}

	public void setS_XIC13(Float s_XIC13) {
		S_XIC13 = s_XIC13;
	}

	public Float getS_XIA15() {
		return S_XIA15;
	}

	public void setS_XIA15(Float s_XIA15) {
		S_XIA15 = s_XIA15;
	}

	public Float getS_XIB15() {
		return S_XIB15;
	}

	public void setS_XIB15(Float s_XIB15) {
		S_XIB15 = s_XIB15;
	}

	public Float getS_XIC15() {
		return S_XIC15;
	}

	public void setS_XIC15(Float s_XIC15) {
		S_XIC15 = s_XIC15;
	}

	public Float getS_XIA17() {
		return S_XIA17;
	}

	public void setS_XIA17(Float s_XIA17) {
		S_XIA17 = s_XIA17;
	}

	public Float getS_XIB17() {
		return S_XIB17;
	}

	public void setS_XIB17(Float s_XIB17) {
		S_XIB17 = s_XIB17;
	}

	public Float getS_XIC17() {
		return S_XIC17;
	}

	public void setS_XIC17(Float s_XIC17) {
		S_XIC17 = s_XIC17;
	}

	public Float getS_XIA19() {
		return S_XIA19;
	}

	public void setS_XIA19(Float s_XIA19) {
		S_XIA19 = s_XIA19;
	}

	public Float getS_XIB19() {
		return S_XIB19;
	}

	public void setS_XIB19(Float s_XIB19) {
		S_XIB19 = s_XIB19;
	}

	public Float getS_XIC19() {
		return S_XIC19;
	}

	public void setS_XIC19(Float s_XIC19) {
		S_XIC19 = s_XIC19;
	}

	public Float getS_XIA21() {
		return S_XIA21;
	}

	public void setS_XIA21(Float s_XIA21) {
		S_XIA21 = s_XIA21;
	}

	public Float getS_XIB21() {
		return S_XIB21;
	}

	public void setS_XIB21(Float s_XIB21) {
		S_XIB21 = s_XIB21;
	}

	public Float getS_XIC21() {
		return S_XIC21;
	}

	public void setS_XIC21(Float s_XIC21) {
		S_XIC21 = s_XIC21;
	}

	public Float getS_XIA23() {
		return S_XIA23;
	}

	public void setS_XIA23(Float s_XIA23) {
		S_XIA23 = s_XIA23;
	}

	public Float getS_XIB23() {
		return S_XIB23;
	}

	public void setS_XIB23(Float s_XIB23) {
		S_XIB23 = s_XIB23;
	}

	public Float getS_XIC23() {
		return S_XIC23;
	}

	public void setS_XIC23(Float s_XIC23) {
		S_XIC23 = s_XIC23;
	}

	public Float getS_XIA25() {
		return S_XIA25;
	}

	public void setS_XIA25(Float s_XIA25) {
		S_XIA25 = s_XIA25;
	}

	public Float getS_XIB25() {
		return S_XIB25;
	}

	public void setS_XIB25(Float s_XIB25) {
		S_XIB25 = s_XIB25;
	}

	public Float getS_XIC25() {
		return S_XIC25;
	}

	public void setS_XIC25(Float s_XIC25) {
		S_XIC25 = s_XIC25;
	}

	public Float getS_XIA27() {
		return S_XIA27;
	}

	public void setS_XIA27(Float s_XIA27) {
		S_XIA27 = s_XIA27;
	}

	public Float getS_XIB27() {
		return S_XIB27;
	}

	public void setS_XIB27(Float s_XIB27) {
		S_XIB27 = s_XIB27;
	}

	public Float getS_XIC27() {
		return S_XIC27;
	}

	public void setS_XIC27(Float s_XIC27) {
		S_XIC27 = s_XIC27;
	}

	public Float getS_XIA29() {
		return S_XIA29;
	}

	public void setS_XIA29(Float s_XIA29) {
		S_XIA29 = s_XIA29;
	}

	public Float getS_XIB29() {
		return S_XIB29;
	}

	public void setS_XIB29(Float s_XIB29) {
		S_XIB29 = s_XIB29;
	}

	public Float getS_XIC29() {
		return S_XIC29;
	}

	public void setS_XIC29(Float s_XIC29) {
		S_XIC29 = s_XIC29;
	}

	public Float getS_XIA31() {
		return S_XIA31;
	}

	public void setS_XIA31(Float s_XIA31) {
		S_XIA31 = s_XIA31;
	}

	public Float getS_XIB31() {
		return S_XIB31;
	}

	public void setS_XIB31(Float s_XIB31) {
		S_XIB31 = s_XIB31;
	}

	public Float getS_XIC31() {
		return S_XIC31;
	}

	public void setS_XIC31(Float s_XIC31) {
		S_XIC31 = s_XIC31;
	}
	
	
}
