package com.qt.entity.APP;

import java.util.Date;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2020-06-20 12:01:21
*/
public class AppVersionUpgrade{

    // Fields

    private Integer id;
    private String versionCode;
    private String versionMin;
    private String apkUrl;
    private String upgradeDesc;
    private String isForce;
    private Date createTime;
    private Date updateTime;
    
    // Constructors

    /** default constructor */
    public AppVersionUpgrade() {
    }

    // Property accessors

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return this.id;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getVersionCode() {
        return this.versionCode;
    }

    public void setVersionMin(String versionMin) {
        this.versionMin = versionMin;
    }

    public String getVersionMin() {
        return this.versionMin;
    }

    public void setApkUrl(String apkUrl) {
        this.apkUrl = apkUrl;
    }

    public String getApkUrl() {
        return this.apkUrl;
    }

    public void setUpgradeDesc(String upgradeDesc) {
        this.upgradeDesc = upgradeDesc;
    }

    public String getUpgradeDesc() {
        return this.upgradeDesc;
    }

    public void setIsForce(String isForce) {
        this.isForce = isForce;
    }

    public String getIsForce() {
        return this.isForce;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

}