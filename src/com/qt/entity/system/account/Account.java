package com.qt.entity.system.account;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.type.Alias;

import com.qt.entity.base.BaseEntity;
import com.qt.entity.system.log.LoginLog;
import com.qt.entity.system.org.Position;
/**
 * 用户帐号表
 */
@Alias("BaseAccount")
public class Account extends BaseEntity{
	
	private static final long serialVersionUID = 1L;

	private String accountId;

	private String loginName;

	private String password;
	
	private String salt;

	private String name;
	
	private String picUrl;
	
	private String skin;
	
	private String roleId;
	
	private String roleName;

	private String email;

	private String description;
	
	private Integer isValid;

	private Date createTime;

	private Date updateTime;
	
    private LoginLog loginLog=new LoginLog();
	
	private String keyWord;
	
	private Integer createCapacity; //创建用户允许创建用户个数
	
	private String createUserId; //创建用户id
	
	private String createUserName; //创建用户名称组合，下划线分割
	
	private	Integer type;	//类型：1超级管理员，0：普通用户
	
	private Integer alarmState;	//告警配置是否开启，1 开启，0关闭
	
	private Integer regState;	//是否是注册用户：0：非注册，1：注册用户
	
	private Date invalidTime;	//过期时间
	
	private String corNamval;	//公司名称
	
	private String corPhoval;	//公司电话
	
	private String phone;		//手机号
	
	private String accountStationId;
	
	private String invalidTimer;
	
	private String limitStationId; //关联站点id（注册平台传递所用）
	
	
	
	
	
	public String getLimitStationId() {
		return limitStationId;
	}

	public void setLimitStationId(String limitStationId) {
		this.limitStationId = limitStationId;
	}

	public String getInvalidTimer() {
		return invalidTimer;
	}

	public void setInvalidTimer(String invalidTimer) {
		this.invalidTimer = invalidTimer;
	}

	public Integer getAlarmState() {
		return alarmState;
	}

	public void setAlarmState(Integer alarmState) {
		this.alarmState = alarmState;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	private List<Position> poss=new ArrayList<Position>();

	public String getAccountId() {
		return accountId;
	}
	
	public Integer getCreateCapacity() {
		return createCapacity;
	}

	public void setCreateCapacity(Integer createCapacity) {
		this.createCapacity = createCapacity;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public String getSkin() {
		return skin;
	}

	public void setSkin(String skin) {
		this.skin = skin;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public String getSalt() {
		return salt;
	}

	public void setSalt(String salt) {
		this.salt = salt;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getKeyWord() {
		return keyWord;
	}

	public void setKeyWord(String keyWord) {
		this.keyWord = keyWord;
	}

	public LoginLog getLoginLog() {
		return loginLog;
	}

	public void setLoginLog(LoginLog loginLog) {
		this.loginLog = loginLog;
	}

	public List<Position> getPoss() {
		return poss;
	}

	public void setPoss(List<Position> poss) {
		this.poss = poss;
	}

	public Integer getRegState() {
		return regState;
	}

	public void setRegState(Integer regState) {
		this.regState = regState;
	}

	public Date getInvalidTime() {
		return invalidTime;
	}

	public void setInvalidTime(Date invalidTime) {
		this.invalidTime = invalidTime;
	}

	public String getCorNamval() {
		return corNamval;
	}

	public void setCorNamval(String corNamval) {
		this.corNamval = corNamval;
	}

	public String getCorPhoval() {
		return corPhoval;
	}

	public void setCorPhoval(String corPhoval) {
		this.corPhoval = corPhoval;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public String getAccountStationId() {
		return accountStationId;
	}

	public void setAccountStationId(String accountStationId) {
		this.accountStationId = accountStationId;
	}
	
}