package com.qt.entity.system.account;


import java.util.Date;

import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 运行指标设置
 * <AUTHOR>
 *
 */
@Alias("OpeIndexSetting")
public class OpeIndexSetting {
	
	private Integer id;
	private String lineTypeId;
	private String settingName;
	private String unit;
	private Date createTime;
	private String bsaCodeIds;
	private String bsaCodeNames;
	
	private Integer counts;
	
	private String systemName;	//系统名称
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getLineTypeId() {
		return lineTypeId;
	}
	public void setLineTypeId(String lineTypeId) {
		this.lineTypeId = lineTypeId;
	}
	public Integer getCounts() {
		return counts;
	}
	public void setCounts(Integer counts) {
		this.counts = counts;
	}
	
	public String getSettingName() {
		return settingName;
	}
	
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss") //入参
	public void setSettingName(String settingName) {
		this.settingName = settingName;
	}
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd hh:mm:ss") //出参
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getBsaCodeIds() {
		return bsaCodeIds;
	}
	public void setBsaCodeIds(String bsaCodeIds) {
		this.bsaCodeIds = bsaCodeIds;
	}
	public String getBsaCodeNames() {
		return bsaCodeNames;
	}
	public void setBsaCodeNames(String bsaCodeNames) {
		this.bsaCodeNames = bsaCodeNames;
	}
	public String getSystemName() {
		return systemName;
	}
	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}
	
	
}
