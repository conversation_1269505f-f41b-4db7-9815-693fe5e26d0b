package com.qt.entity.system.log;

import org.apache.ibatis.type.Alias;

@Alias("SystemInfo")
public class SystemInfo{
	
	private String id;
	
	private String company;
	
	private String qtName;
	
	private String beiyong;
	
	private String aboutUs;
	
	private String onlineHelp;
	
	private String connect;
	
	private String phoneNum;
	
	private String commissioningDate;
	
	private String authorizationDate;
	
	private String filePath;

	private String IP;
	
	private String mqttName;
	
	private String mqttPassword;
	private String logoPic;
	
	public String getIP() {
		return IP;
	}

	public void setIP(String iP) {
		IP = iP;
	}

	public String getMqttName() {
		return mqttName;
	}

	public void setMqttName(String mqttName) {
		this.mqttName = mqttName;
	}

	public String getMqttPassword() {
		return mqttPassword;
	}

	public void setMqttPassword(String mqttPassword) {
		this.mqttPassword = mqttPassword;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getCommissioningDate() {
		return commissioningDate;
	}

	public void setCommissioningDate(String commissioningDate) {
		this.commissioningDate = commissioningDate;
	}

	public String getAboutUs() {
		return aboutUs;
	}

	public void setAboutUs(String aboutUs) {
		this.aboutUs = aboutUs;
	}

	public String getOnlineHelp() {
		return onlineHelp;
	}

	public void setOnlineHelp(String onlineHelp) {
		this.onlineHelp = onlineHelp;
	}

	public String getConnect() {
		return connect;
	}

	public void setConnect(String connect) {
		this.connect = connect;
	}

	public String getPhoneNum() {
		return phoneNum;
	}

	public void setPhoneNum(String phoneNum) {
		this.phoneNum = phoneNum;
	}

	public String getId() {
		return id;
	}

	public String getCompany() {
		return company;
	}

	public String getQtName() {
		return qtName;
	}

	public String getBeiyong() {
		return beiyong;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public void setQtName(String qtName) {
		this.qtName = qtName;
	}

	public void setBeiyong(String beiyong) {
		this.beiyong = beiyong;
	}

	public String getAuthorizationDate() {
		return authorizationDate;
	}

	public void setAuthorizationDate(String authorizationDate) {
		this.authorizationDate = authorizationDate;
	}

	public String getLogoPic() {
		return logoPic;
	}

	public void setLogoPic(String logoPic) {
		this.logoPic = logoPic;
	}
}
