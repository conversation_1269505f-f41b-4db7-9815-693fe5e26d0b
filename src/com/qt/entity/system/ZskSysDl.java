package com.qt.entity.system;

import org.apache.ibatis.type.Alias;

@Alias("ZskSysDl")
public class ZskSysDl {
	
	private Integer dlSysIndex;//序号
	
	private String dlSysLx;//知识库类型
	
	private String dlSysZlx;//知识子类型
	
	private String dlSysSyxh;//适应型号
	
	private String dlSysZskId;//知识库编号
	
	private Integer dlSysGsFlag;//监测值及公式类型说明，0：监测值，1：复合公式
	
	private String dlSysGs;//监测值或者公式
	
	private Float dlSysMinValue;//阈值下限
	
	private Float dlSysMaxValue;//阈值上限
	
	private String dlSysBj;//报警类型，报警类型编号
	
	private Integer dlSysQyFlag;//是否启用标识，0：不启用，1：启用
	
	private String dlSysAqpj;//安全评价
	
	private String dlSysAqfx;//安全分析
	
	private String dlSysJjpj;//经济评价
	
	private String dlSysJjfx;//经济分析
	
	private String dlSysZgyj;//整改意见

	public Integer getDlSysIndex() {
		return dlSysIndex;
	}

	public void setDlSysIndex(Integer dlSysIndex) {
		this.dlSysIndex = dlSysIndex;
	}

	public String getDlSysLx() {
		return dlSysLx;
	}

	public void setDlSysLx(String dlSysLx) {
		this.dlSysLx = dlSysLx;
	}

	public String getDlSysZlx() {
		return dlSysZlx;
	}

	public void setDlSysZlx(String dlSysZlx) {
		this.dlSysZlx = dlSysZlx;
	}

	public String getDlSysSyxh() {
		return dlSysSyxh;
	}

	public void setDlSysSyxh(String dlSysSyxh) {
		this.dlSysSyxh = dlSysSyxh;
	}

	public String getDlSysZskId() {
		return dlSysZskId;
	}

	public void setDlSysZskId(String dlSysZskId) {
		this.dlSysZskId = dlSysZskId;
	}

	public Integer getDlSysGsFlag() {
		return dlSysGsFlag;
	}

	public void setDlSysGsFlag(Integer dlSysGsFlag) {
		this.dlSysGsFlag = dlSysGsFlag;
	}

	public String getDlSysGs() {
		return dlSysGs;
	}

	public void setDlSysGs(String dlSysGs) {
		this.dlSysGs = dlSysGs;
	}

	public Float getDlSysMinValue() {
		return dlSysMinValue;
	}

	public void setDlSysMinValue(Float dlSysMinValue) {
		this.dlSysMinValue = dlSysMinValue;
	}

	public Float getDlSysMaxValue() {
		return dlSysMaxValue;
	}

	public void setDlSysMaxValue(Float dlSysMaxValue) {
		this.dlSysMaxValue = dlSysMaxValue;
	}

	public String getDlSysBj() {
		return dlSysBj;
	}

	public void setDlSysBj(String dlSysBj) {
		this.dlSysBj = dlSysBj;
	}

	public Integer getDlSysQyFlag() {
		return dlSysQyFlag;
	}

	public void setDlSysQyFlag(Integer dlSysQyFlag) {
		this.dlSysQyFlag = dlSysQyFlag;
	}

	public String getDlSysAqpj() {
		return dlSysAqpj;
	}

	public void setDlSysAqpj(String dlSysAqpj) {
		this.dlSysAqpj = dlSysAqpj;
	}

	public String getDlSysAqfx() {
		return dlSysAqfx;
	}

	public void setDlSysAqfx(String dlSysAqfx) {
		this.dlSysAqfx = dlSysAqfx;
	}

	public String getDlSysJjpj() {
		return dlSysJjpj;
	}

	public void setDlSysJjpj(String dlSysJjpj) {
		this.dlSysJjpj = dlSysJjpj;
	}

	public String getDlSysJjfx() {
		return dlSysJjfx;
	}

	public void setDlSysJjfx(String dlSysJjfx) {
		this.dlSysJjfx = dlSysJjfx;
	}

	public String getDlSysZgyj() {
		return dlSysZgyj;
	}

	public void setDlSysZgyj(String dlSysZgyj) {
		this.dlSysZgyj = dlSysZgyj;
	}
	

}
