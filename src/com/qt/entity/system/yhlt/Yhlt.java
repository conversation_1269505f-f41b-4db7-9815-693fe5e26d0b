package com.qt.entity.system.yhlt;

import org.apache.ibatis.type.Alias;

@Alias("/Yhlt")
public class Yhlt{
	private Integer yhltId;
	private String title;
	private String content;
	private String publisher;
	private String addtime;
	private String uptime;
	private String sequence;
	
	
	public Yhlt() {
		super();
	}


	public Yhlt(Integer yhltId, String title, String content, String publisher, String addtime, String uptime,
			String sequence) {
		super();
		this.yhltId = yhltId;
		this.title = title;
		this.content = content;
		this.publisher = publisher;
		this.addtime = addtime;
		this.uptime = uptime;
		this.sequence = sequence;
	}


	public Integer getYhltId() {
		return yhltId;
	}


	public void setYhltId(Integer yhltId) {
		this.yhltId = yhltId;
	}


	public String getTitle() {
		return title;
	}


	public void setTitle(String title) {
		this.title = title;
	}


	public String getContent() {
		return content;
	}


	public void setContent(String content) {
		this.content = content;
	}


	public String getPublisher() {
		return publisher;
	}


	public void setPublisher(String publisher) {
		this.publisher = publisher;
	}


	public String getAddtime() {
		return addtime;
	}


	public void setAddtime(String addtime) {
		this.addtime = addtime;
	}


	public String getUptime() {
		return uptime;
	}


	public void setUptime(String uptime) {
		this.uptime = uptime;
	}


	public String getSequence() {
		return sequence;
	}


	public void setSequence(String sequence) {
		this.sequence = sequence;
	}


	
	

}
