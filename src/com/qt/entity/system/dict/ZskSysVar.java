package com.qt.entity.system.dict;

import org.apache.ibatis.type.Alias;

import com.qt.entity.base.BaseEntity;

@Alias("ZskSysVar")
public class ZskSysVar extends BaseEntity {
	
	private static final long serialVersionUID = 1L;
	
	private Integer zskVarIndex;//序号
	
	private String zskVarMastType;//一级类别
	
	private String zskVarSubType;//二级类别
	
	private String zskVarName;//变量名
	
	private String zskVarDesc;//描述
	
	private String zskvarNote;//备注
	
	private String zskVarSyncTime;//添加时间

	public Integer getZskVarIndex() {
		return zskVarIndex;
	}

	public void setZskVarIndex(Integer zskVarIndex) {
		this.zskVarIndex = zskVarIndex;
	}

	public String getZskVarMastType() {
		return zskVarMastType;
	}

	public void setZskVarMastType(String zskVarMastType) {
		this.zskVarMastType = zskVarMastType;
	}

	public String getZskVarSubType() {
		return zskVarSubType;
	}

	public void setZskVarSubType(String zskVarSubType) {
		this.zskVarSubType = zskVarSubType;
	}

	public String getZskVarName() {
		return zskVarName;
	}

	public void setZskVarName(String zskVarName) {
		this.zskVarName = zskVarName;
	}

	public String getZskVarDesc() {
		return zskVarDesc;
	}

	public void setZskVarDesc(String zskVarDesc) {
		this.zskVarDesc = zskVarDesc;
	}

	public String getZskvarNote() {
		return zskvarNote;
	}

	public void setZskvarNote(String zskvarNote) {
		this.zskvarNote = zskvarNote;
	}

	public String getZskVarSyncTime() {
		return zskVarSyncTime;
	}

	public void setZskVarSyncTime(String zskVarSyncTime) {
		this.zskVarSyncTime = zskVarSyncTime;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	

}
