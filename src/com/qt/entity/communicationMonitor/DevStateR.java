package com.qt.entity.communicationMonitor;

/**
  *
  *2021-09-27 14:55:15
*/
public class DevStateR{

    // Fields

    private Integer devIndex;
    private String devStationId;
    private String devId;
    private Integer devType;
    private Integer devState;
    private String devOnlineTime;
    private String devOfflineTime;
    private String devPeerConn;
    private String devSerialNo;
    
    private String stationName;
    private String gatewayName;
    private String address;
    private String id;
    private String equCount;
    private String timeCount;
    
    // Constructors

    /** default constructor */
    public DevStateR() {
    }

    // Property accessors
    
    public void setDevIndex(Integer devIndex) {
        this.devIndex = devIndex;
    }

    public String getTimeCount() {
		return timeCount;
	}

	public void setTimeCount(String timeCount) {
		this.timeCount = timeCount;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getEquCount() {
		return equCount;
	}

	public void setEquCount(String equCount) {
		this.equCount = equCount;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Integer getDevIndex() {
        return this.devIndex;
    }

    public void setDevStationId(String devStationId) {
        this.devStationId = devStationId;
    }

    public String getDevStationId() {
        return this.devStationId;
    }

    public void setDevId(String devId) {
        this.devId = devId;
    }

    public String getDevId() {
        return this.devId;
    }

    public void setDevType(Integer devType) {
        this.devType = devType;
    }

    public Integer getDevType() {
        return this.devType;
    }

    public void setDevState(Integer devState) {
        this.devState = devState;
    }

    public Integer getDevState() {
        return this.devState;
    }

    public void setDevOnlineTime(String devOnlineTime) {
        this.devOnlineTime = devOnlineTime;
    }

    public String getDevOnlineTime() {
        return this.devOnlineTime;
    }

    public void setDevOfflineTime(String devOfflineTime) {
        this.devOfflineTime = devOfflineTime;
    }

    public String getDevOfflineTime() {
        return this.devOfflineTime;
    }

    public void setDevPeerConn(String devPeerConn) {
        this.devPeerConn = devPeerConn;
    }

    public String getDevPeerConn() {
        return this.devPeerConn;
    }

    public void setDevSerialNo(String devSerialNo) {
        this.devSerialNo = devSerialNo;
    }

    public String getDevSerialNo() {
        return this.devSerialNo;
    }

}