package com.qt.entity.communicationMonitor;

/**
  *
  *2021-09-27 14:56:12
*/
public class DelStateH{

    // Fields

    private Integer delIndex;
    private String delStationId;
    private String delLineId;
    private Integer delType;
    private Integer delState;
    private String delTime;
    
    private String equName;
    
    // Constructors

    /** default constructor */
    public DelStateH() {
    }

    // Property accessors

    public void setDelIndex(Integer delIndex) {
        this.delIndex = delIndex;
    }

    public String getEquName() {
		return equName;
	}

	public void setEquName(String equName) {
		this.equName = equName;
	}

	public Integer getDelIndex() {
        return this.delIndex;
    }

    public void setDelStationId(String delStationId) {
        this.delStationId = delStationId;
    }

    public String getDelStationId() {
        return this.delStationId;
    }

    public void setDelLineId(String delLineId) {
        this.delLineId = delLineId;
    }

    public String getDelLineId() {
        return this.delLineId;
    }

    public void setDelType(Integer delType) {
        this.delType = delType;
    }

    public Integer getDelType() {
        return this.delType;
    }

    public void setDelState(Integer delState) {
        this.delState = delState;
    }

    public Integer getDelState() {
        return this.delState;
    }

    public void setDelTime(String delTime) {
        this.delTime = delTime;
    }

    public String getDelTime() {
        return this.delTime;
    }

}