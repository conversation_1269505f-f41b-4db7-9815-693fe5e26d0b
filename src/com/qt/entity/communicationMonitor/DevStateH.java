package com.qt.entity.communicationMonitor;

/**
  *
  *2021-09-27 14:55:37
*/
public class DevStateH{

    // Fields

    private Integer devIndex;
    private String devStationId;
    private String devId;
    private Integer devType;
    private Integer devState;
    private String devTime;
    
    private String gatewayName;
    
    // Constructors

    /** default constructor */
    public DevStateH() {
    }

    // Property accessors

    public void setDevIndex(Integer devIndex) {
        this.devIndex = devIndex;
    }

    public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public Integer getDevIndex() {
        return this.devIndex;
    }

    public void setDevStationId(String devStationId) {
        this.devStationId = devStationId;
    }

    public String getDevStationId() {
        return this.devStationId;
    }

    public void setDevId(String devId) {
        this.devId = devId;
    }

    public String getDevId() {
        return this.devId;
    }

    public void setDevType(Integer devType) {
        this.devType = devType;
    }

    public Integer getDevType() {
        return this.devType;
    }

    public void setDevState(Integer devState) {
        this.devState = devState;
    }

    public Integer getDevState() {
        return this.devState;
    }

    public void setDevTime(String devTime) {
        this.devTime = devTime;
    }

    public String getDevTime() {
        return this.devTime;
    }

}