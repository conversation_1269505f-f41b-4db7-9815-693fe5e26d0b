package com.qt.entity.communicationMonitor;
/**
* <AUTHOR> :cuijian
* @version 创建时间：2021年9月27日 下午3:05:31
* 类说明:
*/
public class DelAndDevCount {

	private String stationName;
	private Integer wOnLine; 	//网关在线
	private Integer wOffLine;	//网关离线
	private Integer sOnLine;	//设备在线
	private Integer sOffLine;	//设备离线
	private String stationId;
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public Integer getwOnLine() {
		return wOnLine;
	}
	public void setwOnLine(Integer wOnLine) {
		this.wOnLine = wOnLine;
	}
	public Integer getwOffLine() {
		return wOffLine;
	}
	public void setwOffLine(Integer wOffLine) {
		this.wOffLine = wOffLine;
	}
	public Integer getsOnLine() {
		return sOnLine;
	}
	public void setsOnLine(Integer sOnLine) {
		this.sOnLine = sOnLine;
	}
	public Integer getsOffLine() {
		return sOffLine;
	}
	public void setsOffLine(Integer sOffLine) {
		this.sOffLine = sOffLine;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
	
}
