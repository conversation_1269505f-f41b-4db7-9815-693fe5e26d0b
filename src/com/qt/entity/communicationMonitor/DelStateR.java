package com.qt.entity.communicationMonitor;

/**
  *
  *2021-09-27 14:56:00
*/
public class DelStateR{

    // Fields

    private Integer delIndex;
    private String delStationId;
    private String delLineId;
    private Integer delType;
    private Integer delState;
    private String delOnlineTime;
    private String delOfflineTime;
    
    private String stationName;
    private String equName;
    private String timeCount;
    
    // Constructors

    /** default constructor */
    public DelStateR() {
    }

    // Property accessors
    
    public void setDelIndex(Integer delIndex) {
        this.delIndex = delIndex;
    }

    public String getTimeCount() {
		return timeCount;
	}

	public void setTimeCount(String timeCount) {
		this.timeCount = timeCount;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getEquName() {
		return equName;
	}

	public void setEquName(String equName) {
		this.equName = equName;
	}

	public Integer getDelIndex() {
        return this.delIndex;
    }

    public void setDelStationId(String delStationId) {
        this.delStationId = delStationId;
    }

    public String getDelStationId() {
        return this.delStationId;
    }

    public void setDelLineId(String delLineId) {
        this.delLineId = delLineId;
    }

    public String getDelLineId() {
        return this.delLineId;
    }

    public void setDelType(Integer delType) {
        this.delType = delType;
    }

    public Integer getDelType() {
        return this.delType;
    }

    public void setDelState(Integer delState) {
        this.delState = delState;
    }

    public Integer getDelState() {
        return this.delState;
    }

    public void setDelOnlineTime(String delOnlineTime) {
        this.delOnlineTime = delOnlineTime;
    }

    public String getDelOnlineTime() {
        return this.delOnlineTime;
    }

    public void setDelOfflineTime(String delOfflineTime) {
        this.delOfflineTime = delOfflineTime;
    }

    public String getDelOfflineTime() {
        return this.delOfflineTime;
    }

}