package com.qt.entity.video;

import org.apache.ibatis.type.Alias;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2017年12月4日上午10:05:14
 * @instruction 
 */
@Alias("YsyToken")
public class YsyToken {
	private Integer tokenIndex;
	private String accessToken;
	/*private String expireTime;
	private String updateTime;*/
	private Date expireTime;
	private Date updateTime;
	public Integer getIndex() {
		return tokenIndex;
	}
	public void setIndex(Integer tokenIndex) {
		this.tokenIndex = tokenIndex;
	}
	public String getAccessToken() {
		return accessToken;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	/*public String getExpireTime() {
		return expireTime;
	}
	public void setExpireTime(String expireTime) {
		this.expireTime = expireTime;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}*/
	public Date getExpireTime() {
		return expireTime;
	}
	public void setExpireTime(Date expireTime) {
		this.expireTime = expireTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	
	
	
	
	

}
