package com.qt.entity.devAccount;

import java.util.Date;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2021-06-29 11:54:46
*/
public class DevDefectRecord{

    // Fields

    private Integer dedIndex;
    private String dedId;
    private String dedDesc;
    private String dedDevId;
    private String dedCreateUser;
    private Date dedCreateTime;
    private Integer dedState;
    
    private String dedDevName;
    private String dcsName;
    private String detNodePath;
    private String userName;
    // Constructors

    /** default constructor */
    public DevDefectRecord() {
    }

    // Property accessors

    public void setDedIndex(Integer dedIndex) {
        this.dedIndex = dedIndex;
    }

    public Integer getDedIndex() {
        return this.dedIndex;
    }

    public void setDedId(String dedId) {
        this.dedId = dedId;
    }

    public String getDedId() {
        return this.dedId;
    }

    public void setDedDesc(String dedDesc) {
        this.dedDesc = dedDesc;
    }

    public String getDedDesc() {
        return this.dedDesc;
    }

    public void setDedDevId(String dedDevId) {
        this.dedDevId = dedDevId;
    }

    public String getDedDevId() {
        return this.dedDevId;
    }

    public void setDedCreateUser(String dedCreateUser) {
        this.dedCreateUser = dedCreateUser;
    }

    public String getDedCreateUser() {
        return this.dedCreateUser;
    }

    public void setDedCreateTime(Date dedCreateTime) {
        this.dedCreateTime = dedCreateTime;
    }

    public Date getDedCreateTime() {
        return this.dedCreateTime;
    }

    public void setDedState(Integer dedState) {
        this.dedState = dedState;
    }

    public Integer getDedState() {
        return this.dedState;
    }

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDetNodePath() {
		return detNodePath;
	}

	public void setDetNodePath(String detNodePath) {
		this.detNodePath = detNodePath;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getDedDevName() {
		return dedDevName;
	}

	public void setDedDevName(String dedDevName) {
		this.dedDevName = dedDevName;
	}

}