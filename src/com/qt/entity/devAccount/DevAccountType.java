package com.qt.entity.devAccount;

import java.util.Date;
import java.util.List;
import com.qt.entity.devAccount.DevAccountInfo;;


/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2021-06-22 16:15:57
*/
public class DevAccountType{

    // Fields

    private Integer detIndex;
    private String detNodeId;
    private String detNodeName;
    private String detNodeParentId;
    private String opeIds;
    
    private String detNodePath;//设备分类名称（含父子级）
    
    private List<DevAccountType> children;

    private String id;	//树id
    private String name;	//树节点名字
    private String pId;	//上级菜单编号
    private String value;	//树id，特供下拉xm-select共用使用
    
    private List<DevAccountInfo> infos;
    
    // Constructors

    /** default constructor */
    public DevAccountType() {
    }

    // Property accessors
    
    public void setDetIndex(Integer detIndex) {
        this.detIndex = detIndex;
    }

    public String getOpeIds() {
		return opeIds;
	}

	public void setOpeIds(String opeIds) {
		this.opeIds = opeIds;
	}

	public Integer getDetIndex() {
        return this.detIndex;
    }

    public void setDetNodeId(String detNodeId) {
        this.detNodeId = detNodeId;
    }

    public String getDetNodeId() {
        return this.detNodeId;
    }

    public void setDetNodeName(String detNodeName) {
        this.detNodeName = detNodeName;
    }

    public String getDetNodeName() {
        return this.detNodeName;
    }

    public void setDetNodeParentId(String detNodeParentId) {
        this.detNodeParentId = detNodeParentId;
    }

    public String getDetNodeParentId() {
        return this.detNodeParentId;
    }

	public List<DevAccountType> getChildren() {
		return children;
	}

	public void setChildren(List<DevAccountType> children) {
		this.children = children;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getpId() {
		return pId;
	}

	public void setpId(String pId) {
		this.pId = pId;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public List<DevAccountInfo> getInfos() {
		return infos;
	}

	public void setInfos(List<DevAccountInfo> infos) {
		this.infos = infos;
	}

	public String getDetNodePath() {
		return detNodePath;
	}

	public void setDetNodePath(String detNodePath) {
		this.detNodePath = detNodePath;
	}
	

}