package com.qt.entity.devAccount;

import java.util.Date;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2021-06-22 16:16:39
*/
public class DevAccountInfo{

    // Fields

    private Integer defIndex;
    private String defNodeId;
    private String defNodeAttr;
    private String defNodeVal;
    private Integer defIsReq;
    private Integer defType;
    
    private String deoAttrValue;
    private String deaNodeId;
    private String deoIndex;
    
    @Override
    public String toString() {
        return "DevAccountInfo{" +
                "defIndex=" + defIndex +
                ", defNodeId='" + defNodeId + '\'' +
                ", defNodeAttr='" + defNodeAttr + '\'' +
                ", defNodeVal='" + defNodeVal + '\'' +
                ", defIsReq='" + defIsReq + '\'' +
                ", defType='" + defType + '\'' +
                '}';
    }
    
    // Constructors

    /** default constructor */
    public DevAccountInfo() {
    }
    
    public DevAccountInfo(Integer defIndex,String defNodeId,String defNodeAttr,String defNodeVal,Integer defIsReq,Integer defType) {
    	this.defIndex=defIndex;
    	this.defNodeId = defNodeId;
    	this.defNodeAttr=defNodeAttr;
    	this.defNodeVal=defNodeVal;
    	this.defIsReq=defIsReq;
    	this.defType=defType;
    }

    // Property accessors

    public void setDefIndex(Integer defIndex) {
        this.defIndex = defIndex;
    }

    public Integer getDefIndex() {
        return this.defIndex;
    }

    public void setDefNodeId(String defNodeId) {
        this.defNodeId = defNodeId;
    }

    public String getDefNodeId() {
        return this.defNodeId;
    }

    public void setDefNodeAttr(String defNodeAttr) {
        this.defNodeAttr = defNodeAttr;
    }

    public String getDefNodeAttr() {
        return this.defNodeAttr;
    }

    public void setDefNodeVal(String defNodeVal) {
        this.defNodeVal = defNodeVal;
    }

    public String getDefNodeVal() {
        return this.defNodeVal;
    }

    public void setDefIsReq(Integer defIsReq) {
        this.defIsReq = defIsReq;
    }

    public Integer getDefIsReq() {
        return this.defIsReq;
    }

    public void setDefType(Integer defType) {
        this.defType = defType;
    }

    public Integer getDefType() {
        return this.defType;
    }

	public String getDeoAttrValue() {
		return deoAttrValue;
	}

	public void setDeoAttrValue(String deoAttrValue) {
		this.deoAttrValue = deoAttrValue;
	}

	public String getDeaNodeId() {
		return deaNodeId;
	}

	public void setDeaNodeId(String deaNodeId) {
		this.deaNodeId = deaNodeId;
	}

	public String getDeoIndex() {
		return deoIndex;
	}

	public void setDeoIndex(String deoIndex) {
		this.deoIndex = deoIndex;
	}
    

}