package com.qt.entity.devAccount;

import java.util.Date;
import java.util.List;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2021-06-25 13:29:48
*/
public class DevArchives{

    // Fields

    private Integer deaIndex;
    private String deaNodeId;
    private String deaNodeName;
    private String deaNodeParentId;
    private String detNodeId;
    private String detNodePath;
    private String deaNodeRelationDev;
    private String deaNodeRelationDcs;
    private Integer deaIsvalid;
    private String deaPicUrl;
    private String deaQrcodeId;
    private String deaQrcodeUrl;
    private Integer deaIsleaf;
    private Date deaCreatetime;
    private Date deaUpdatetime;
    
    private Integer isTypeChange;
    private String dcsName;
    private String dclName;
    
    private List<DevArchives> children;

    private String id;	//树id
    private String name;	//树节点名字
    private String pId;	//上级菜单编号
    private String value;	//树id，特供下拉xm-select共用使用
    
    private List<DevAttrInfo> infos;
    
    private List<DevAccountInfo> devAccountInfos;
    
    // Constructors

    /** default constructor */
    public DevArchives() {
    }

    // Property accessors

    public void setDeaIndex(Integer deaIndex) {
        this.deaIndex = deaIndex;
    }

    public Integer getDeaIndex() {
        return this.deaIndex;
    }

    public void setDeaNodeId(String deaNodeId) {
        this.deaNodeId = deaNodeId;
    }

    public String getDeaNodeId() {
        return this.deaNodeId;
    }

    public void setDeaNodeName(String deaNodeName) {
        this.deaNodeName = deaNodeName;
    }

    public String getDeaNodeName() {
        return this.deaNodeName;
    }

    public void setDeaNodeParentId(String deaNodeParentId) {
        this.deaNodeParentId = deaNodeParentId;
    }

    public String getDeaNodeParentId() {
        return this.deaNodeParentId;
    }

    public void setDeaNodeRelationDev(String deaNodeRelationDev) {
        this.deaNodeRelationDev = deaNodeRelationDev;
    }

    public String getDeaNodeRelationDev() {
        return this.deaNodeRelationDev;
    }

    public void setDeaNodeRelationDcs(String deaNodeRelationDcs) {
        this.deaNodeRelationDcs = deaNodeRelationDcs;
    }

    public String getDeaNodeRelationDcs() {
        return this.deaNodeRelationDcs;
    }

    public void setDeaIsvalid(Integer deaIsvalid) {
        this.deaIsvalid = deaIsvalid;
    }

    public Integer getDeaIsvalid() {
        return this.deaIsvalid;
    }

    public void setDeaPicUrl(String deaPicUrl) {
        this.deaPicUrl = deaPicUrl;
    }

    public String getDeaPicUrl() {
        return this.deaPicUrl;
    }

    public void setDeaQrcodeId(String deaQrcodeId) {
        this.deaQrcodeId = deaQrcodeId;
    }

    public String getDeaQrcodeId() {
        return this.deaQrcodeId;
    }

    public void setDeaQrcodeUrl(String deaQrcodeUrl) {
        this.deaQrcodeUrl = deaQrcodeUrl;
    }

    public String getDeaQrcodeUrl() {
        return this.deaQrcodeUrl;
    }

    public void setDeaIsleaf(Integer deaIsleaf) {
        this.deaIsleaf = deaIsleaf;
    }

    public Integer getDeaIsleaf() {
        return this.deaIsleaf;
    }

    public void setDeaCreatetime(Date deaCreatetime) {
        this.deaCreatetime = deaCreatetime;
    }

    public Date getDeaCreatetime() {
        return this.deaCreatetime;
    }

    public void setDeaUpdatetime(Date deaUpdatetime) {
        this.deaUpdatetime = deaUpdatetime;
    }

    public Date getDeaUpdatetime() {
        return this.deaUpdatetime;
    }

	public String getDetNodeId() {
		return detNodeId;
	}

	public void setDetNodeId(String detNodeId) {
		this.detNodeId = detNodeId;
	}

	public List<DevArchives> getChildren() {
		return children;
	}

	public void setChildren(List<DevArchives> children) {
		this.children = children;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getpId() {
		return pId;
	}

	public void setpId(String pId) {
		this.pId = pId;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public List<DevAttrInfo> getInfos() {
		return infos;
	}

	public void setInfos(List<DevAttrInfo> infos) {
		this.infos = infos;
	}

	public List<DevAccountInfo> getDevAccountInfos() {
		return devAccountInfos;
	}

	public void setDevAccountInfos(List<DevAccountInfo> devAccountInfos) {
		this.devAccountInfos = devAccountInfos;
	}

	public Integer getIsTypeChange() {
		return isTypeChange;
	}

	public void setIsTypeChange(Integer isTypeChange) {
		this.isTypeChange = isTypeChange;
	}

	public String getDetNodePath() {
		return detNodePath;
	}

	public void setDetNodePath(String detNodePath) {
		this.detNodePath = detNodePath;
	}

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}
    

}