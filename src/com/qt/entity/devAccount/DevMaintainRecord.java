package com.qt.entity.devAccount;

import java.util.Date;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2021-06-29 11:54:28
*/
public class DevMaintainRecord{

    // Fields

    private Integer demIndex;
    private String demId;
    private String demDesc;
    private String demDevId;
    private String demCreateUser;
    private Date demCreateTime;
    private Integer demState;
    
    private String demDevName;
    private String dcsName;
    private String detNodePath;
    private String userName;
    
    // Constructors

    /** default constructor */
    public DevMaintainRecord() {
    }

    // Property accessors

    public void setDemIndex(Integer demIndex) {
        this.demIndex = demIndex;
    }

    public Integer getDemIndex() {
        return this.demIndex;
    }

    public void setDemId(String demId) {
        this.demId = demId;
    }

    public String getDemId() {
        return this.demId;
    }

    public void setDemDesc(String demDesc) {
        this.demDesc = demDesc;
    }

    public String getDemDesc() {
        return this.demDesc;
    }

    public void setDemDevId(String demDevId) {
        this.demDevId = demDevId;
    }

    public String getDemDevId() {
        return this.demDevId;
    }

    public void setDemCreateUser(String demCreateUser) {
        this.demCreateUser = demCreateUser;
    }

    public String getDemCreateUser() {
        return this.demCreateUser;
    }

    public void setDemCreateTime(Date demCreateTime) {
        this.demCreateTime = demCreateTime;
    }

    public Date getDemCreateTime() {
        return this.demCreateTime;
    }

    public void setDemState(Integer demState) {
        this.demState = demState;
    }

    public Integer getDemState() {
        return this.demState;
    }

	public String getDcsName() {
		return dcsName;
	}

	public void setDcsName(String dcsName) {
		this.dcsName = dcsName;
	}

	public String getDetNodePath() {
		return detNodePath;
	}

	public void setDetNodePath(String detNodePath) {
		this.detNodePath = detNodePath;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getDemDevName() {
		return demDevName;
	}

	public void setDemDevName(String demDevName) {
		this.demDevName = demDevName;
	}

}