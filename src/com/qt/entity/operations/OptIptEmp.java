package com.qt.entity.operations;

import org.apache.ibatis.type.Alias;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2016-12-14 16:28:51
*/
@Alias("/OptIptEmp")
public class OptIptEmp{

    // Fields

    private Integer id;
    private String empUnitId;
    private String name;
    private String empId;
    private String position;
    private String tel;
    private String dccFullName;
    
    // Constructors

    /** default constructor */
    public OptIptEmp() {
    }

    // Property accessors
    
    public void setId(Integer id) {
        this.id = id;
    }

    public String getDccFullName() {
		return dccFullName;
	}

	public void setDccFullName(String dccFullName) {
		this.dccFullName = dccFullName;
	}

	public Integer getId() {
        return this.id;
    }

    public void setEmpUnitId(String empUnitId) {
        this.empUnitId = empUnitId;
    }

    public String getEmpUnitId() {
        return this.empUnitId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getEmpId() {
        return this.empId;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPosition() {
        return this.position;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getTel() {
        return this.tel;
    }

}