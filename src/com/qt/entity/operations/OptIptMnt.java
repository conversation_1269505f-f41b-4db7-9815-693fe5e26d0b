package com.qt.entity.operations;

import java.util.Date;

import org.apache.ibatis.type.Alias;

/**
  *�ô����ɱ���������Զ����
  *ʱ�䣺2016-12-13 14:27:13
*/
@Alias("OptIptMnt")
public class OptIptMnt{

    // Fields

    private Integer id;
    private Integer eqmId;
    private Integer eqmStationId;
    private Date eqmPlanTime;
    private String eqmPlanTask;
    private Date eqmMntTime;
    private String eqmMntDo;
    private Integer eqmStatus;
    
    // Constructors

    /** default constructor */
    public OptIptMnt() {
    }

    // Property accessors

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return this.id;
    }

    public void setEqmId(Integer eqmId) {
        this.eqmId = eqmId;
    }

    public Integer getEqmId() {
        return this.eqmId;
    }

    public void setEqmStationId(Integer eqmStationId) {
        this.eqmStationId = eqmStationId;
    }

    public Integer getEqmStationId() {
        return this.eqmStationId;
    }

    public void setEqmPlanTime(Date eqmPlanTime) {
        this.eqmPlanTime = eqmPlanTime;
    }

    public Date getEqmPlanTime() {
        return this.eqmPlanTime;
    }

    public void setEqmPlanTask(String eqmPlanTask) {
        this.eqmPlanTask = eqmPlanTask;
    }

    public String getEqmPlanTask() {
        return this.eqmPlanTask;
    }

    public void setEqmMntTime(Date eqmMntTime) {
        this.eqmMntTime = eqmMntTime;
    }

    public Date getEqmMntTime() {
        return this.eqmMntTime;
    }

    public void setEqmMntDo(String eqmMntDo) {
        this.eqmMntDo = eqmMntDo;
    }

    public String getEqmMntDo() {
        return this.eqmMntDo;
    }

    public void setEqmStatus(Integer eqmStatus) {
        this.eqmStatus = eqmStatus;
    }

    public Integer getEqmStatus() {
        return this.eqmStatus;
    }

}