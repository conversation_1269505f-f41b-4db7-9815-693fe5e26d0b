package com.qt.cache;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.qt.cache.base.QtCache;
import com.qt.cache.bean.SomeThing;

@Component(value = "someThingCache")
public class SomeThingCache extends QtCache<SomeThing> {
	private static final long serialVersionUID = 1L;
	private static final Logger logger = LoggerFactory.getLogger(SomeThingCache.class);

	@Override
	public void refresh() throws Exception {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		String function="xxxx";
		SomeThing someThing=new SomeThing("good");
		//SERVICE GET RESULTSET FROM DB OR OTHERWISE
		boolean getSomeThingFromDB=21>2;
		
		if (getSomeThingFromDB) {
			getCachedMap().put("xxx",someThing);
		} else {
			throw new Exception(String.format("加载XXX缓存出错[%s:%s]",function , paramMap));
		}
		
	}

	@Override
	public String getName() {
		return "XXX缓存";
	}
	

}
