package com.qt.cache.base;

import java.io.File;
import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;


@Service("remoteManager")
public class RemoteManagerImpl implements IRemoteManager {

	private static final Logger logger = LoggerFactory.getLogger(RemoteManagerImpl.class);

	@Autowired
	CacheManager cacheManager;

	@Override
	public void registerCache(CacheInfo cache) {
		cacheManager.addCache(cache);
	}

	@Override
	public void registerTerminal(String address, String host, String binpath, long start_time, String os_name, boolean is_unix) {
		//TODO
	}

	@Override
	public File getConfigResource(String path) {
		try {
			return new ClassPathResource(path).getFile();
		} catch (IOException e) {
			logger.error("读取文件[" + path + "]发生错误", e);
		}
		return null;
	}

}
