package com.qt.cache.base;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.core.Ordered;

import com.qt.common.utils.JsonUtil;
import com.qt.common.utils.base.MD5;


public abstract class QtCache<T> implements Serializable, Ordered {
	private static final long serialVersionUID = 1L;
	private Map<String, T> cachedMap = new LinkedHashMap<String, T>();

	public int getOrder() {
		return 0;
	}

	@Override
	public int hashCode() {
		String md5 = MD5.md5(JsonUtil.toJson(this));
		return md5.hashCode();
	}

	public void setCachedMap(Map<String, T> cachedMap) {
		this.cachedMap = cachedMap;
	}

	public Map<String, T> getCachedMap() {
		return cachedMap;
	}

	/**
	 * 刷新缓存，从中间件/配置文件重新获取
	 */
	public abstract void refresh() throws Exception;

	public abstract String getName();

}
