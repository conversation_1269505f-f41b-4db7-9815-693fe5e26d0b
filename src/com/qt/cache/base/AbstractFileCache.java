package com.qt.cache.base;

import java.io.File;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;

public abstract class AbstractFileCache<T> extends QtCache<T> {

	public static final String CONF_DIR = "qt_conf";

	@Autowired
	IRemoteManager remote;

	public Resource getClasspathResource(String path) {
		if (!path.startsWith(CONF_DIR)) {
			path = CONF_DIR + "/" + path;
		}
		File file = remote.getConfigResource(path);
		// 兼容处理，方便开发
		return file == null ? new ClassPathResource(path) : new FileSystemResource(file);
	}

}
