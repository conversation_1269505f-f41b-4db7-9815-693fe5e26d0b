package com.qt.cache.base;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import com.qt.common.utils.SpringUtil;

/**
 * 功能说明: 缓存管理器<br>
 */
@Component("cacheManager")
public class CacheManager implements Ordered {

	private static final Logger logger = LoggerFactory.getLogger(CacheManager.class);

	Map<String, CacheInfo> cacheMap = new LinkedHashMap<String, CacheInfo>();

	@Autowired
	ApplicationContext appcontext;

	@Autowired
	private IRemoteManager remote;

	@Value("${monitor.cacheSeconds}")
	long cacheSeconds;

	/**
	 * 更新所有缓存<br/>
	 * 根据order顺序刷新，先刷新被引用项
	 * @throws Exception
	 */
	@PostConstruct
	private void refreshAll() throws Exception {
		/*
		 * 根据order顺序刷新，先刷新被引用项
		 */
		List<Map.Entry<String, QtCache>> list = SpringUtil.getOrderedBeans(appcontext, QtCache.class);

		try {
			for (Map.Entry<String, QtCache> entry : list) {
				refreshOne(entry.getKey());
			}
		} catch (Exception e) {
			logger.error("更新缓存发生错误", e);
			throw e;
		}
	}

	/**
	 * 指定刷新
	 * @param cacheName
	 * @throws Exception
	 */
	public void refreshOne(String cacheName) throws Exception {
		QtCache<?> cache = appcontext.getBean(cacheName, QtCache.class);
		if (cache != null) {
			cache.refresh();
			String type = cache.getClass().isAssignableFrom(AbstractFileCache.class) ? "FILE" : "DB";
			remote.registerCache(new CacheInfo(cacheName, cache.getName(), type, new Date().getTime()));
			logger.info("刷新缓存[" + cache.getName() + "]成功！");
		}
	}

	/**
	 * 监控配置缓存更新，包括数据库内配置与文件配置
	 */
	@PostConstruct
	private void monitor() {

		new Thread() {

			public void run() {
				while (true) {
					try {
						sleep(cacheSeconds * 1000);

						refreshAll();

					} catch (InterruptedException e) {
						logger.error("监听线程休眠发生错误", e);
					} catch (Exception e) {
						logger.error("更新缓存发生错误，跳过本次更新", e);
					}
				}
			}
		}.start();
	}

	/**
	 * 保证比普通service先加载
	 */
	@Override
	public int getOrder() {
		return -1;
	}

	public void addCache(CacheInfo cache) {
		cacheMap.put(cache.getName(), cache);
	}

	public Map<String, CacheInfo> getCacheMap() {
		return cacheMap;
	}

}
