package com.qt.cache.base;

import java.io.Serializable;

public class CacheInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	String id;
	String name;
	String type;
	long refresh_time; 

	public CacheInfo(String id, String name, String type, long refresh_time) {
		super();
		this.id = id;
		this.name = name;
		this.type = type;
		this.refresh_time = refresh_time;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public long getRefresh_time() {
		return refresh_time;
	}

	public void setRefresh_time(long refresh_time) {
		this.refresh_time = refresh_time;
	}

}
