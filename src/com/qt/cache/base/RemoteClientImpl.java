package com.qt.cache.base;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.web.context.WebApplicationContext;

@Service("remoteClient")
public class RemoteClientImpl implements IRemoteClient, Ordered {

	private static final Logger logger = LoggerFactory.getLogger(RemoteClientImpl.class);

	long start_time = new Date().getTime();

	@Autowired
	WebApplicationContext appcontext;

	@Value("${monitor.cacheSeconds}")
	long re_space;

	@Override
	public void updateCache(String cacheName) {

		QtCache localCache = appcontext.getBean(cacheName, QtCache.class);
		try {
			localCache.refresh();
			logger.info("更新缓存[" + cacheName + "]成功");
		} catch (Exception e) {
			logger.info("更新缓存[" + cacheName + "]发生错误", e);
		}

	}

	@Override
	public Map<String, String> checkServerStatus() {
		Map<String, String> map = new HashMap<String, String>();
		return map;
	}

	@Override
	public int getOrder() {
		return 0;
	}

}
