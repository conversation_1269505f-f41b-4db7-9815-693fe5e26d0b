package com.qt.common.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.tools.zip.ZipOutputStream;

public class FileOptUtil {
	boolean flag;
	File file;
	List<String> fileList=new ArrayList<String>();
	List<String> fileNameList=new ArrayList<String>();
	/** 
	  * 删除单个文件 
	  * @param   sPath    被删除文件的文件名 
	  * @return 单个文件删除成功返回true，否则返回false 
	  */  
	 public boolean deleteFile(String sPath) {  
	     flag = false;  
	     file = new File(sPath);  
	     // 路径为文件且不为空则进行删除  
	     if (file.isFile() && file.exists()) {  
	         file.delete();  
	         flag = true;  
	     }  
	     return flag;  
	 }  
	 /** 
	  * 删除目录（文件夹）以及目录下的文件 
	  * @param   sPath 被删除目录的文件路径 
	  * @return  目录删除成功返回true，否则返回false 
	  */  
	 public boolean deleteDirectory(String sPath) {  
	     //如果sPath不以文件分隔符结尾，自动添加文件分隔符  
	     if (!sPath.endsWith(File.separator)) {  
	         sPath = sPath + File.separator;  
	     }  
	     File dirFile = new File(sPath);  
	     //如果dir对应的文件不存在，或者不是一个目录，则退出  
	     if (!dirFile.exists() || !dirFile.isDirectory()) {  
	         return false;  
	     }  
	     flag = true;  
	     //删除文件夹下的所有文件(包括子目录)  
	     File[] files = dirFile.listFiles();  
	     for (int i = 0; i < files.length; i++) {  
	         //删除子文件  
	         if (files[i].isFile()) {  
	             flag = deleteFile(files[i].getAbsolutePath());  
	             if (!flag) break;  
	         } //删除子目录  
	         else {  
	             flag = deleteDirectory(files[i].getAbsolutePath());  
	             if (!flag) break;  
	         }  
	     }  
	     if (!flag) return false;  
	     //删除当前目录  
	     if (dirFile.delete()) {  
	         return true;  
	     } else {  
	         return false;  
	     }  
	 }  
	 /** 
	  *  根据路径删除指定的目录或文件，无论存在与否 
	  *@param sPath  要删除的目录或文件 
	  *@return 删除成功返回 true，否则返回 false。 
	  */  
	 public boolean DeleteFolder(String sPath) {  
	     flag = false;  
	     file = new File(sPath);  
	     // 判断目录或文件是否存在  
	     if (!file.exists()) {  // 不存在返回 false  
	         return flag;  
	     } else {  
	         // 判断是否为文件  
	         if (file.isFile()) {  // 为文件时调用删除文件方法  
	             return deleteFile(sPath);  
	         } else {  // 为目录时调用删除目录方法  
	             return deleteDirectory(sPath);  
	         }  
	     }  
	 } 
	   /* 
	    * inputFileName 输入一个文件夹 
	    * zipFileName 输出一个压缩文件夹 
	    */  
	    public void zip(String inputFileName,String zipFileName) throws Exception {  
	        System.out.println(zipFileName);  
	        zip(zipFileName, new File(inputFileName));  
	    }  
	  
	    private void zip(String zipFileName, File inputFile) throws Exception {  
	        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFileName)); 
	        out.setEncoding("gbk");//设置编码格式
	        zip(out, inputFile, "");  
	        System.out.println("zip done");  
	        out.close();  
	    }  
	  
	    private void zip(ZipOutputStream out, File f, String base) throws Exception {  
	        if (f.isDirectory()) {  
	           File[] fl = f.listFiles();  
	           out.putNextEntry(new org.apache.tools.zip.ZipEntry(base + "/"));  
	           base = base.length() == 0 ? "" : base + "/";  
	           for (int i = 0; i < fl.length; i++) {  
	           zip(out, fl[i], base + fl[i].getName());  
	         }  
	        }else {  
	           out.putNextEntry(new org.apache.tools.zip.ZipEntry(base));  
	           FileInputStream in = new FileInputStream(f);  
	           int b;  
	         // System.out.println(base);  
	           while ( (b = in.read()) != -1) {  
	            out.write(b);  
	         }  
	         in.close();  
	       }  
	    }  
	    
	    /** 
		  *  获取文件夹下所有文件路径
		  *@param sPath  要查询的目录
		  *@return 目录存在返回 true以及文件路径列表，否则返回 false以及null。 
		  */
	    public Map<String, Object> getFilePath(String path) {
	    	Map<String, Object> map = new HashMap<String, Object>();
	        File f = new File(path);
	        if (!f.exists()) {
	          map.put("msg", false);
	          map.put("result", fileList);
	          return map;
	        }
	        File fa[] = f.listFiles();
	        for (int i = 0; i < fa.length; i++) {
	          File fs = fa[i];
	          //如果是文件夹,递归获取里面的文件/文件夹
	          if (fs.isDirectory()) {
	        	  getFilePath(fs.getAbsolutePath());
	          } else {
	        	fileList.add(fs.getAbsolutePath());
	        	map.put("msg", true);
		        map.put("result", fileList);
		        
	          }
	        }
	        return map;
	      }
	    
	    /** 
		  *  获取文件夹下所有文件路径，截取参数String path后面的路径
		  *@param sPath  要查询的目录
		  *@return 目录存在返回 true以及文件路径列表，否则返回 false以及null。 
		  */
	    public Map<String, Object> getLastFilePath(String path) {
	    	Map<String, Object> map = new HashMap<String, Object>();
	        File f = new File(path);
	        if (!f.exists()) {
	          map.put("msg", false);
	          map.put("result", fileNameList);
	          return map;
	        }
	        File fa[] = f.listFiles();
	        for (int i = 0; i < fa.length; i++) {
	          File fs = fa[i];
	          String absolutePath=fs.getAbsolutePath();
	          String lastPath=absolutePath.substring(path.length(), absolutePath.length());
	          //如果是文件夹,递归获取里面的文件/文件夹
	          if (fs.isDirectory()) {
	        	  getFilePath(absolutePath);
	          } else {
	        	  fileNameList.add(lastPath);
	        	map.put("msg", true);
		        map.put("result", fileNameList);
		        
	          }
	        }
	        return map;
	      }

}
