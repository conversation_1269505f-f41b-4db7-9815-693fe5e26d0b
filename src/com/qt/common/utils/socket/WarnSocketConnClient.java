package com.qt.common.utils.socket;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.net.SocketTimeoutException;
import java.util.Map;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.qt.common.utils.JsonUtil;

public class WarnSocketConnClient {
	private static Logger logger=LoggerFactory.getLogger(WarnSocketConnClient.class);
	public static final String warnServerIp="**************";
	public static final int warnServerPort=6804;
	
	
	/**
	 * 报文发送方法
	 * @return
	 */
	public static String sendXmlToRsk(){
		StringBuffer sb = new StringBuffer();
		Socket socket = null;
		SocketAddress socketAddress=null;
		BufferedReader br = null;
		OutputStream out = null;
		try {
			socketAddress=new InetSocketAddress(warnServerIp, warnServerPort); 
			socket = new Socket();
			socket.connect(socketAddress,20000);
			socket.setSoTimeout(20000);  
			// 发送命令
			out = socket.getOutputStream();
			Map<String,Object> reqMap=new TreeMap<String,Object>();
			reqMap.put("type", "identify");
			reqMap.put("istation", "-1");
			String xmlContent=JsonUtil.toJson(reqMap);
			logger.info("WARN请求报文:" + xmlContent);
			out.write(xmlContent.getBytes("UTF-8"));
			// 接收服务器的反馈
			br = new BufferedReader(new InputStreamReader(socket.getInputStream(), "UTF-8"));
			String lineTxt = null;
			while ((lineTxt = br.readLine()) != null) {
				logger.info("WARN返回报文:" + lineTxt);
				sb.append(lineTxt);
			}
		} catch (SocketTimeoutException e) {
			logger.error("########socket客户端请求时间超时异常########"+ e);
			logger.warn("超时时的默认处理报文:" + sb.toString());
		} catch (IOException e) {
			logger.error("########socket客户端请求异常########"+ e);
			logger.warn("超时时的默认处理报文:" + sb.toString());
		} finally {  
            try {  
                br.close();  
                out.close();
                socket.close();  
            } catch (Exception e) {  
            	logger.error("########socket客户端请求操作异常########"+e);
            }  
        }  
		return sb.toString();
	}
	
	public static void main(String[] args) {
		WarnSocketConnClient.sendXmlToRsk();
	}
	
}
