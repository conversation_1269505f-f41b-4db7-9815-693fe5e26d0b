package com.qt.common.utils.tree.entity;

import java.util.List;

public class TreeNode {
	private String id;
	private String name;
	private String open;
	private String icon;
	private String iconSkin;
	private String isParent;
	private List<TreeNode> children;
	
	private String lon;
	private String ltu;
    private String icon2;
    private String picUrl;
    private String parentId;
	
	public String getParentId() {
		return parentId;
	}


	public void setParentId(String parentId) {
		this.parentId = parentId;
	}
    
	
	public TreeNode() {
		super();
	}
	

	public TreeNode(String id, String name, String open, String icon,
			String iconSkin, String isParent, List<TreeNode> children,
			String lon, String ltu) {
		super();
		this.id = id;
		this.name = name;
		this.open = open;
		this.icon = icon;
		this.iconSkin = iconSkin;
		this.isParent = isParent;
		this.children = children;
		this.lon = lon;
		this.ltu = ltu;
	}



	public TreeNode(String id, String name, String open, String lon, String ltu, String icon2,String icon,
			String iconSkin, String isParent, List<TreeNode> children
			) {
		super();
		this.id = id;
		this.name = name;
		this.open = open;
		this.icon = icon;
		this.iconSkin = iconSkin;
		this.isParent = isParent;
		this.children = children;
		this.lon = lon;
		this.ltu = ltu;
		this.icon2 = icon2;
	}


	public TreeNode(String id, String name, String open, String iconSkin) {
		super();
		this.id = id;
		this.name = name;
		this.open = open;
		this.iconSkin = iconSkin;
	}

	public TreeNode(String id, String name, String open, String iconSkin , String parentId) {
		super();
		this.id = id;
		this.name = name;
		this.open = open;
		this.iconSkin = iconSkin;
		this.parentId = parentId;
	}

	public TreeNode(String id, String name, String open) {
		super();
		this.id = id;
		this.name = name;
		this.open = open;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getOpen() {
		return open;
	}

	public void setOpen(String open) {
		this.open = open;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getIconSkin() {
		return iconSkin;
	}

	public void setIconSkin(String iconSkin) {
		this.iconSkin = iconSkin;
	}

	public String getIsParent() {
		return isParent;
	}

	public void setIsParent(String isParent) {
		this.isParent = isParent;
	}

	public List<TreeNode> getChildren() {
		return children;
	}

	public void setChildren(List<TreeNode> children) {
		this.children = children;
	}
	

	public String getLon() {
		return lon;
	}

	public void setLon(String lon) {
		this.lon = lon;
	}

	public String getLtu() {
		return ltu;
	}

	public void setLtu(String ltu) {
		this.ltu = ltu;
	}
	

	public String getIcon2() {
		return icon2;
	}


	public void setIcon2(String icon2) {
		this.icon2 = icon2;
	}
	
    
    


	public String getPicUrl() {
		return picUrl;
	}


	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}


	@Override
	public String toString() {
		return "TreeNode [id=" + id + ", name=" + name + ", open=" + open + ", icon=" + icon + ", iconSkin=" + iconSkin + ", isParent=" + isParent + ", children=" + children + "]";
	}


	public TreeNode(String id, String name, String open, String lon, String ltu, String picUrl) {
		super();
		this.id = id;
		this.name = name;
		this.open = open;
		this.lon = lon;
		this.ltu = ltu;
		this.picUrl = picUrl;
	}


	
	
	

}
