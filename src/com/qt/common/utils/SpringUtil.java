package com.qt.common.utils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.OrderComparator;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.multiaction.InternalPathMethodNameResolver;
import org.springframework.web.servlet.mvc.multiaction.MethodNameResolver;

public class SpringUtil {

	private static ApplicationContext context = null;

	public static HttpServletRequest getRequest() {
		try {
			ServletRequestAttributes attr = (ServletRequestAttributes)RequestContextHolder.currentRequestAttributes();
			if (attr != null) {
				return attr.getRequest();
			}
		} catch (Exception e) {

		}
		return null;
	}

	public static Object getRequestAttribute(String attr) {
		HttpServletRequest request = getRequest();
		if (request != null) {
			return request.getAttribute(attr);
		}
		return null;
	}

	public static String getRequestParameter(String param) {
		HttpServletRequest request = getRequest();
		if (request != null) {
			return request.getParameter(param);
		}
		return null;
	}

	public static Method getHandleMethod(HttpServletRequest request, Object handler) throws Exception {
		MethodNameResolver methodNameResolver = new InternalPathMethodNameResolver();
		String methodName = methodNameResolver.getHandlerMethodName(request);
		Map<String, String> pathVariables = (Map<String, String>)request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
		for (Method method : handler.getClass().getMethods()) {
			RequestMapping rm = method.getAnnotation(RequestMapping.class);
			if (rm != null) {
				for (String val : rm.value()) {
					while (pathVariables != null && val.indexOf("{") < val.indexOf("}")) {
						String temp = val.substring(val.indexOf("{") + 1, val.indexOf("}"));
						val = val.replace("{" + temp + "}", pathVariables.get(temp));
					}
					if ("".equals(val) || methodName.equals(val)) {
						return method;
					}
				}
			}
		}
		return null;
	}

	public static String getMessage(String code, Object[] args) {
		MessageSource ms = getBean(MessageSource.class);
		return ms.getMessage(code, args, LocaleContextHolder.getLocale());
	}

	/**
	 * 后台获取当前访问session
	 * @return session
	 */
	public static HttpSession getCurrSession() {
		HttpSession contextSess = null;
		try {
			ServletRequestAttributes attr = (ServletRequestAttributes)RequestContextHolder.currentRequestAttributes();
			contextSess = attr == null ? null : attr.getRequest().getSession(true);
		} catch (Exception e) {

		}
		return contextSess;
	}

	public static <T> Map<String, T> getBeans(Class<T> clazz) {
		if (context == null) {
			context = ContextLoader.getCurrentWebApplicationContext();
		}
		return context.getBeansOfType(clazz);
	}

	public static Object getBean(String beanId) {
		if (context == null) {
			context = ContextLoader.getCurrentWebApplicationContext();
		}
		return context.getBean(beanId);
	}

	public static <T> T getBean(Class<T> clazz) {
		if (context == null) {
			context = ContextLoader.getCurrentWebApplicationContext();
		}
		return context.getBean(clazz);
	}

	public static <T> T getBean(String beanId, Class<T> clazz) {
		if (context == null) {
			context = ContextLoader.getCurrentWebApplicationContext();
		}
		return context.getBean(beanId, clazz);
	}

	public static Resource getResource(String location) {
		if (context == null) {
			context = ContextLoader.getCurrentWebApplicationContext();
		}
		return context.getResource(location);
	}

	public static <T> List<Map.Entry<String, T>> getOrderedBeans(ApplicationContext context, Class<T> clazz) {
		if (context == null) {
			context = ContextLoader.getCurrentWebApplicationContext();
		}
		Set<Map.Entry<String, T>> caches = context.getBeansOfType(clazz).entrySet();
		List<Map.Entry<String, T>> list = new ArrayList<Map.Entry<String, T>>(caches);
		/*
		 * 根据order顺序排列
		 */
		Collections.sort(list, new Comparator<Map.Entry<String, T>>() {

			public int compare(Map.Entry<String, T> e1, Map.Entry<String, T> e2) {
				return new OrderComparator().compare(e1.getValue(), e2.getValue());
			}
		});
		return list;
	}
}
