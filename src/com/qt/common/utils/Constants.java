package com.qt.common.utils;

/**
 * 全局常量
 * 
 * <AUTHOR>
 */
public class Constants {

	/**
	 * 默认初始化密码
	 * 
	 * <AUTHOR>
	 */
	public static final String INIT_PWD = "123456";

	/**
	 * 默认文件存储位置
	 * <AUTHOR>
	 */
	public static final String FILE_DEFAULT_URL="/WEB-INF/upload";
	
	/**
	 * 分页
	 * 
	 * <AUTHOR>
	 * 
	 */
	public static class Page {
		/**
		 * 记录开始索引
		 */
		public static String OFFSET = "offset";
		/**
		 * 页面显示记录数量
		 */
		public static String LIMIT = "limit";

		/**
		 * 每页显示记录数量，默认15条
		 */
		public static Integer PAGE_SIZE = 15;

	}

	/**
	 * 操作类型
	 * 
	 * <AUTHOR>
	 * 
	 */
	public static class Action {
		/**
		 * 添加操作
		 */
		public static String SAVE = "添加";

		/**
		 * 删除操作
		 */
		public static String DELETE = "删除";

		/**
		 * 删除版本失败信息,版本正在使用中不能被删除
		 */
		public static String VERSION_DONT_DELETE = "版本正在使用中,删除";
		
		/**
		 * 修改操作
		 */
		public static String UPDATE = "修改";

		/**
		 * 绑定操作
		 */
		public static String BIND = "绑定";

		/**
		 * 启用操作
		 * 
		 */

		public static String ENABLED = "启用";
		/**
		 * 禁用操作
		 * 
		 */
		public static String DISABLED = "禁用";

		/**
		 * 导入操作
		 * 
		 */
		public static String IMPORT = "导入";

		/**
		 * 导出操作
		 * 
		 */
		public static String EXPORT = "导出";

	}

	/**
	 * ajax异步响应
	 * 
	 * <AUTHOR>
	 * 
	 */
	public static class Response {
		/**
		 * 显示类型为右下角弹窗
		 */
		public static String SHOWTYPE_SLIDE = "slide";

		/**
		 * 右下角弹窗显示时间 默认为2000
		 */
		public static Integer TIMEOUT = 2000;

		/**
		 * 右下角弹窗显示的标题
		 */
		public static String TITLE = "提示";
	}

	/**
	 * 查询条件
	 * 
	 * <AUTHOR>
	 * 
	 */
	public static class Condition {

		/**
		 * 时间范围起始
		 */
		public static String START_TIME = "startTime";

		/**
		 * 时间范围终止
		 */
		public static String END_TIME = "endTime";

		/**
		 * 根据指定列排序
		 */
		public static String ORDERBY_CLAUSE = "ORDERBY_CLAUSE";
	}

	public static class InspectionPlan {
		/**
		 * 线路巡查1
		 */
		public static String LINE_PLAN_ID = "1";

		public static String LINE_PLAN_COLOR = "#00008B";

		public static String LINE_PLAN_NAME= "线路巡查";

		/**
		 * 设备点检2
		 */
		public static String EQM_PLAN_ID = "2";

		public static String EQM_PLAN_COLOR = "#87CEFA";

		public static String EQM_PLAN_NAME = "设备点检";

		/**
		 * 告警巡查3
		 */
		public static String ALERT_PLAN_ID = "3";

		public static String ALERT_PLAN_COLOR = "#FFB6C1";
		
		public static String ALERT_PLAN_NAME = "告警巡查";

	}

	/**
	 * 热费结算默认时间
	 */
	public static class BalanceDate {

		/**
		 * 每日00:00（日结）
		 */
		public static String BALANCE_DAILY = "1";

		/**
		 * 每周00:00（周结）
		 */
		public static String BALANCE_WEEKLY = "2";

		/**
		 * 每月00:00（月结）
		 */
		public static String BALANCE_MONTHLY = "3";
	}

	/**
	 * 
	 * 日志类型
	 * 
	 */
	public static class OperateLogType {
		/**
		 * 系统操作记录
		 */
		public static String OPERATE_ACTION = "1";

		/**
		 * 系统异常记录
		 */
		public static String OPERATE_EXCEPTION = "2";
		
		/**
		 * 	数据告警记录
		 */
		public static String OPERATE_ALERT = "3";

	}
}
