package com.qt.common.utils;

import java.util.HashMap;
import java.util.Map;

public class AjaxUtils {

	/**
	 * 
	 * @param flag
	 *            操作是否成功
	 * @param title
	 *            标题
	 * @param msg
	 *            信息内容
	 * @param timeout
	 *            多少时间关闭 Defaults to 4 seconds.
	 * @param showType
	 *            显示类型 Available values are: null,slide,fade,show. Defaults to
	 *            slide.
	 * @return
	 */
	public static Map<String, Object> reponseToJson(Boolean flag, String title, String msg, Integer timeout,
			String showType) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("flag", flag);
		map.put("title", title);
		map.put("msg", msg);
		map.put("timeout", timeout);
		map.put("showType", showType);
		return map;
	}

	/**
	 * 
	 * @param method
	 *            Constants.ADD Constants.DELETE Constants.UPDATE
	 * @param flag
	 *            为true表示成功 false代表失败
	 * @return 右下角提示操作成功或者失败的消息
	 */
	public static Map<String, Object> reponseToJson(String method, Boolean flag) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("title", Constants.Response.TITLE);
		if (flag == true) {
			map.put("msg", method + "成功");
		} else {
			map.put("msg", method + "失败");
		}
		map.put("timeout", Constants.Response.TIMEOUT);
		map.put("showType", Constants.Response.SHOWTYPE_SLIDE);
		return map;
	}


}
