package com.qt.common.utils.APPUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.druid.util.StringUtils;

//import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTSigner;
import com.auth0.jwt.JWTVerifier;
//import com.auth0.jwt.algorithms.Algorithm;
//import com.auth0.jwt.interfaces.Claim;
//import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.internal.com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @date 2018年11月27日下午5:33:54
 * @instruction  APP登录Token的生成和解析
 */
public class JwtToken {
	/** token 过期时间: 60天 */
	public static final int calendarField = Calendar.DATE;
	public static final int calendarInterval = 60;
	/** token秘钥 */
	private static final String SECRET = "SITCAPP";

    private static final String EXP = "exp";

    private static final String PAYLOAD = "payload";

    //加密，返回加密后的token；传入一个对象和有效期
    // public static <T> String sign(T object, long maxAge) {
    public static String sign(String user) {
        try {
        	Date iatDate = new Date();
    		// expire time
    		Calendar nowTime = Calendar.getInstance();
    		nowTime.add(Calendar.DATE, 10);
    		Date expiresDate = nowTime.getTime();
        	long maxAge=expiresDate.getTime();
            final JWTSigner signer = new JWTSigner(SECRET);
            final Map<String, Object> claims = new HashMap<String, Object>();
           /* ObjectMapper mapper = new ObjectMapper();
            String jsonString = mapper.writeValueAsString(object);*/
            claims.put(PAYLOAD, user);
            claims.put(EXP, System.currentTimeMillis() + maxAge);
            return signer.sign(claims);
        } catch(Exception e) {
            return null;
        }
    }

	
	//解密，返回加密放入的内容；传入一个加密后的token字符串和解密后的类型
    //public static<T> T unsign(String jwt, Class<T> classT) {
    public static String unsign(String jwt) {
        final JWTVerifier verifier = new JWTVerifier(SECRET);
        try {
            final Map<String,Object> claims= verifier.verify(jwt);
            if (claims.containsKey(EXP) && claims.containsKey(PAYLOAD)) {
                long exp = (Long)claims.get(EXP);
                long currentTimeMillis = System.currentTimeMillis();
                if (exp > currentTimeMillis) {
                    String json = (String)claims.get(PAYLOAD);
                    //ObjectMapper objectMapper = new ObjectMapper();
                    //return objectMapper.readValue(json, classT);
                    return json;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
	
	public static void main(String[] args) throws Exception {
		/*String tokenSTR = createToken("hhe");
		Map<String, Claim> clainm=verifyToken(tokenSTR);*/
		String tokenSTR = sign("2");
		System.out.println(tokenSTR);
		String user =unsign(tokenSTR);
		System.out.println(user);
		
	}
}

