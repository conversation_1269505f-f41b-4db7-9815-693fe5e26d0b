package com.qt.common.utils;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;

public class ReadPropertyUtil {
	
	//读取配置文件
	public static Map<String,String> getProperty(String propertiesName) { 
		//public static void main(String[] args){ 
		Map<String,String> resultMap=new HashMap<String,String>();
		Properties prop = new Properties();     
		try{
			//读取属性文件a.properties
			/*InputStream in = ReadPropertyUtil.class.getResourceAsStream(propertyName);  
			prop.load(in);*/
		   prop.load(new InputStreamReader(ReadPropertyUtil.class.getResourceAsStream(propertiesName), "UTF-8"));///加载属性列表
		   Iterator<String> it=prop.stringPropertyNames().iterator();
		   while(it.hasNext()){
			   String key=it.next();
			  // System.out.println(key+":"+prop.getProperty(key));
			   resultMap.put(key, prop.getProperty(key));
		   	}
		   
		}catch(Exception e){
		           System.out.println(e);

		} 
		
		return resultMap;
	}
	
	//修改配置文件
	public static void updateProperty(String propertiesName,String propertyName,String value) { 
	    //加载配置文件
		Properties pro = new Properties();
		InputStreamReader in = null;
		try {
			//in = new BufferedInputStream (new FileInputStream(propertiesName));
			in=new InputStreamReader(ReadPropertyUtil.class.getResourceAsStream(propertiesName), "UTF-8");
			pro.load(in);
			in.close();
			//重新写入配置文件
			
			pro.setProperty(propertyName, value);
			//pro.put(propertyName, value);
			FileOutputStream file = new FileOutputStream(propertiesName);
			//System.out.println("得到属性key:"+pro.getProperty(propertyName));
			DateUtils util=new DateUtils();
			pro.store(file, propertyName+"更新时间"+util.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss") ); //这句话表示重新写入配置文件注释
			System.out.println("得到:"+pro);
			file.close();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
	
	public static void main(String[] args) {
		System.out.println(ReadPropertyUtil.class.getResourceAsStream("/ys7API.properties"));
		//updateProperty("/ys7API.properties","speed","0");
		//Map<String,String> resultMap=getProperty("/ys7API.properties");
		//System.out.println(resultMap.get("speed"));
	}
	
}
