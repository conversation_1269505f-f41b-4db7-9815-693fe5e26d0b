package com.qt.common.wf;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR> 李强 
 * @date 2017年2月17日10:03:37
 * @功能 用于存放常用的工具方法
 *
 */

public class Function {

	/**
	 * <AUTHOR> 李强
	 * @date 2017年2月17日10:08:04
	 * @功能 获取图标所用的时间 根据传入参数生成不同的时间（返回时间格式：YY-mm-DD:HH:MM:SS）
	 * @param  yesterday：昨天  lastweek：上周 lastmonth：上月 lastthreemonth：上季度 lastyear：去年
	 * @return （ key：starttime ， value：开始时间），（key：endtime ， value：结束时间）
	 */
	public Map<String , String> get_chartDate(String mode)
	{
		Map<String , String> params = new HashMap<String, String>();
		
		Calendar c = Calendar.getInstance();  
	    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
	    Date d =new Date();  //存放返回的开始时间
	    Date end =new Date();	//存放返回的结束时间
		if(mode!=null)
		{
			if(mode.equals("yesterday"))
			{	
				params.put("starttime",df.format(new Date(d.getTime() - (long)1 * 24 * 60 * 60 * 1000))+" 00:00:00");
				params.put("endtime",df.format(new Date(d.getTime() - (long)1 * 24 * 60 * 60 * 1000))+" 23:45:00");
			}
			else if(mode.equals("lastweek"))
			{
				Calendar cal = Calendar.getInstance();
				int n = cal.get(Calendar.DAY_OF_WEEK) - 1;
		        if (n == 0) {
		            n = 7;
		        }
			    cal.add(Calendar.DATE, -(7 + (n - 1)));
			    d = cal.getTime();
				cal.add(Calendar.DATE, 6);
				 end = cal.getTime();
				params.put("starttime",df.format(d)+" 00:00:00");
				params.put("endtime",df.format(end)+" 23:45:00");
			}
			else if(mode.equals("lastmonth"))
			{
				Calendar cal = Calendar.getInstance();
				cal.add(Calendar.MONTH, -1);
				cal.set(Calendar.DAY_OF_MONTH,1);
				d = cal.getTime();
				Calendar cale = Calendar.getInstance();
				cale.set(Calendar.DAY_OF_MONTH,0);
				end =cale.getTime();
				params.put("starttime",df.format(d)+" 00:00:00");
				params.put("endtime",df.format(end)+" 23:45:00");
				//return "上月:"+df.format(d)+" "+df.format(end);
				
			}
			else if(mode.equals("lastthreemonth"))
			{
				Calendar cal = Calendar.getInstance();
				int year = cal.get(Calendar.YEAR);
				int months = cal.get(Calendar.MONTH)+1;
				int monthe = months+2;
				if(months>=1&&months<=3)
				{
					params.put("starttime",year+"-01-01 00:00:00");
					params.put("endtime",year+"-03-31 23:45:00");
					//return year+"-01-01 00:00:00" + year+"-03-31 23:45:00";
				}else if(months>=4&&months<=6)
				{
					params.put("starttime",year+"-04-01 00:00:00");
					params.put("endtime",year+"-06-30 23:45:00");
					//return year+"-04-01 00:00:00" + year+"-06-30 23:45:00";
				}
				else if(months>=7&&months<=9)
				{
					params.put("starttime",year+"-07-01 00:00:00");
					params.put("endtime",year+"-09-30 23:45:00");
					//return year+"-07-01 00:00:00" + year+"-09-30 23:45:00";
					
				}else if (months>=10&&months<=12) 
				{
					params.put("starttime",year+"-10-01 00:00:00");
					params.put("endtime",year+"-12-31 23:45:00");
					//return year+"-10-01 00:00:00" + year+"-12-31 23:45:00";	
				}
			}
			else if(mode.equals("lastyear"))
			{	
				Calendar cal = Calendar.getInstance();
				int year = cal.get(Calendar.YEAR) - 1;
				params.put("starttime",year+"-01-01 00:00:00");
				params.put("endtime",year+"-12-31 23:45:00");
				//return year+"-01-01 00:00:00" + year+"-12-31 23:45:00";
			}
		}	
		return params;
	}
	
	/**
	 * <AUTHOR> 李强
	 * @date 2017年2月17日10:53:17
	 * @功能 获取图标所用的时间 根据传入参数生成不同的时间（返回时间格式 YY-mm-DD）
	 * @param  yesterday：昨天  lastweek：上周 lastmonth：上月 lastthreemonth：上季度 lastyear：去年
	 * @return （ key：starttime ， value：开始时间），（key：endtime ， value：结束时间）
	 */
	public Map<String , String> get_chartDate_LQ(String mode)
	{
		Map<String , String> params = new HashMap<String, String>();
		
		Calendar c = Calendar.getInstance();  
	    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
	    Date d =new Date();  //存放返回的开始时间
	    Date end =new Date();	//存放返回的结束时间
		if(mode!=null)
		{
			if(mode.equals("yesterday"))
			{	c.setTime(d);
				params.put("starttime",df.format(d.getTime()));
				c.add(c.DATE,1);
				params.put("endtime",df.format(c.getTime()));
			}
			else if(mode.equals("lastweek"))
			{
				Calendar cal = Calendar.getInstance();
				int n = cal.get(Calendar.DAY_OF_WEEK) - 1;
		        if (n == 0) {
		            n = 7;
		        }
			    cal.add(Calendar.DATE, -(7 + (n - 1)));
			    d = cal.getTime();
				cal.add(Calendar.DATE, 7);
				 end = cal.getTime();
				params.put("starttime",df.format(d));
				params.put("endtime",df.format(end));
			}
			else if(mode.equals("lastmonth"))
			{
				Calendar cal = Calendar.getInstance();
				cal.set(Calendar.DAY_OF_MONTH,1);
				end =cal.getTime();
				cal.add(Calendar.MONTH, -1);
				cal.set(Calendar.DAY_OF_MONTH,1);
				d = cal.getTime();
				params.put("starttime",df.format(d));
				params.put("endtime",df.format(end));
				//return "上月:"+df.format(d)+" "+df.format(end);
				
			}
			else if(mode.equals("lastthreemonth"))
			{
				Calendar cal = Calendar.getInstance();
				int year = cal.get(Calendar.YEAR);
				int months = cal.get(Calendar.MONTH)+1;
				int monthe = months+2;
				if(months>=1&&months<=3)
				{
					params.put("starttime",year+"-01-01");
					params.put("endtime",year+"-04-01");
					//return year+"-01-01 00:00:00" + year+"-03-31 23:45:00";
				}else if(months>=4&&months<=6)
				{
					params.put("starttime",year+"-04-01");
					params.put("endtime",year+"-07-01");
					//return year+"-04-01 00:00:00" + year+"-06-30 23:45:00";
				}
				else if(months>=7&&months<=9)
				{
					params.put("starttime",year+"-07-01");
					params.put("endtime",year+"-10-01");
					//return year+"-07-01 00:00:00" + year+"-09-30 23:45:00";
					
				}else if (months>=10&&months<=12) 
				{
					year++;
					params.put("starttime",year+"-10-01");
					params.put("endtime",year+"-01-01");
					//return year+"-10-01 00:00:00" + year+"-12-31 23:45:00";	
				}
			}
			else if(mode.equals("lastyear"))
			{	
				Calendar cal = Calendar.getInstance();
				int year = cal.get(Calendar.YEAR) - 1;
				params.put("starttime",year+"-01-01");
				year++;
				params.put("endtime",year+"-01-01");
				//return year+"-01-01 00:00:00" + year+"-12-31 23:45:00";
			}
		}	
		return params;
	}
	
}
