package com.qt.service.customModel;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.customModel.StoUserVarRealData;
import com.qt.repository.customModel.StoUserVarRealDataDao;

@Service
public class StoUserVarRealDataService {
	
	private Logger logger = LoggerFactory.getLogger(StoUserVarRealDataService.class);
    
    @Resource
	private StoUserVarRealDataDao stoUserVarRealDataDao;
    
    //根据条件搜索记录
	public List<StoUserVarRealData> listEXL(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return stoUserVarRealDataDao.selectList(params);
	}

}
