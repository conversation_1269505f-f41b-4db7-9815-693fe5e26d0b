package com.qt.service.experience;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.experience.SysNews;
import com.qt.repository.experience.SysNewsDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-06 09:49:20
 */
@Service
public class SysNewsService{

	private Logger logger = LoggerFactory.getLogger(SysNewsService.class);
     
    @Resource
	private SysNewsDao sysNewsDao;
	
	//根据条件搜索记录
	public Map<String , Object> list(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", sysNewsDao.selectList(queryMap));
		resultMap.put("total", sysNewsDao.selectCount(queryMap));
		return resultMap;
	}
	
	//根据条件搜索记录
	public List<SysNews> listEXL(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return sysNewsDao.selectList(params);
	}
	
	//根据条件数据库对应数据
	public int count(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return sysNewsDao.selectCountCX(params);
	}
       
	//新增记录
	public void add(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			SysNews temp=MapUtil.toObject(SysNews.class, params);
			sysNewsDao.insert(temp);
		} catch (Exception e) {
			logger.error("上传失败",e);
		}
	}
	
	//更新记录
	public void update(SysNews sysNews){
		sysNewsDao.update(sysNews);
	}
	
	//删除记录
	public int del(String[] idArray){
		for(String pk:idArray){
			sysNewsDao.delete(pk);
		}
		return idArray.length;
	}
	
	//删除记录-删除单条
	public void delete(String pk){
		sysNewsDao.delete(pk);
	}
	
}
