package com.qt.service.backstage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.qt.repository.system.backstage.BackStageDao;

 

 
@Service
public class BackStageService{

	@Resource
	BackStageDao backStageDao;
 
	public Map<String,Object> getSystemoverview(Map<String,Object> params){
		   Map<String,Object> result=new HashMap<String, Object>();
		   result.put("rows",backStageDao.getSystemoverview(params));
		   result.put("total",backStageDao.getSystemoverviewCount(params));
		return result;
	}
	
	public List<Map<String,Object>> selectCountDccInfo(Map<String,Object> params){
		return backStageDao.selectCountDccInfo(params);
	}
	
	
}
