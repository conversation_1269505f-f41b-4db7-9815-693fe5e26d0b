package com.qt.service.saj;

import com.qt.repository.saj.gfjkzxListDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class gfjkzxListService {
    @Resource
    private gfjkzxListDao gfjkzxListDao;


    public List<Map<String,Object>> ceshi(Map<String, Object> params) {
        return gfjkzxListDao.ceshi(params);
    }

    public List<Map<String,Object>> PowerGenerationStatisticsList(Map<String, Object> params) {
        return gfjkzxListDao.PowerGenerationStatisticsList(params);
    }

    public List<Map<String,Object>> PowerGenerationStatisticsListMonth(Map<String, Object> params) {
        return gfjkzxListDao.PowerGenerationStatisticsListMonth(params);
    }

    public List<Map<String,Object>> PowerGenerationStatisticsListYear(Map<String, Object> params) {
        return gfjkzxListDao.PowerGenerationStatisticsListYear(params);
    }
}
