package com.qt.service.statistics.wf;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.wf.OptBsaGroup;
import com.qt.entity.wf.OptOperatGroup;
import com.qt.repository.statistics.wf.OptBsaGroupDao;

@Service
public class OptBsaGroupSevice {

	
	private Logger logger = LoggerFactory.getLogger(OptBsaGroupSevice.class);

	/**
	 * @function  运维用户 指标码组
	 */
	
	@Resource
	private OptBsaGroupDao optBsaGroupDao;

	public Map<String, Object> GetbsaList(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		
		resultMap.put("rows", optBsaGroupDao.getOptBsaGroupList(queryMap));
		resultMap.put("total", optBsaGroupDao.GetCountOptBsa(queryMap));
		return resultMap;   
	}
	
	
	public OptBsaGroup selectByPk(Integer BsaGroupId) {
		
		
		
		return optBsaGroupDao.selectByPk(BsaGroupId);
	}

	public boolean DeleteOptGroup(Integer BsaGroupId) {
		 try {
		   		HashMap<String , Object> params = new HashMap<String , Object>(); 
				params.put("BsaGroupId", BsaGroupId);
				optBsaGroupDao.deleteByPk(params);
				return true;
		   } catch (Exception e) {
				// TODO: handle exception
				return false;  
			}
	}

	public boolean InsertOptGroup(OptBsaGroup optBsaGroup) {
		try {
			optBsaGroupDao.insert(optBsaGroup); 
   			return true;  
		} catch (Exception e) {
			// TODO: handle exception
			return false;  
		}
	}

	public boolean UpdateOptBsaGroup(OptBsaGroup optBsaGroup) {
		try {
			optBsaGroupDao.update(optBsaGroup); 
   			return true;  
		} catch (Exception e) {
			// TODO: handle exception
			return false;  
		}
	}
	
}
