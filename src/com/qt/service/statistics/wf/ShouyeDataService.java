package com.qt.service.statistics.wf;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.wf.ShouyeData;
import com.qt.entity.wf.device_rank;
import com.qt.repository.statistics.wf.ShouyeDataDao;

@Service
public class ShouyeDataService {
	
private Logger logger = LoggerFactory.getLogger(device_rankService.class);
	
	/**
	 * <AUTHOR> 
	 * @function  首页数据提取
	 */
	@Resource
	public ShouyeDataDao shouyeDataDao;
	
	public Map<String , List<ShouyeData>> Get_syydl(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<ShouyeData>> resultMap = new HashMap<String, List<ShouyeData>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", shouyeDataDao.get_syydl(queryMap));	
		return resultMap;
	}
	
	public Map<String , List<ShouyeData>> Get_fgsjtime(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<ShouyeData>> resultMap = new HashMap<String, List<ShouyeData>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", shouyeDataDao.get_fgsjtime(queryMap));	
		return resultMap;
	}
	
	public Map<String , List<ShouyeData>> Get_fgsjydl(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<ShouyeData>> resultMap = new HashMap<String, List<ShouyeData>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", shouyeDataDao.get_fgsjydl(queryMap));	
		return resultMap;
	}
}
