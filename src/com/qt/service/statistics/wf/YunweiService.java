package com.qt.service.statistics.wf;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.baselib.BscType;
import com.qt.entity.wf.Operator;
import com.qt.entity.wf.Record;
import com.qt.repository.statistics.wf.PowerqualityDao;
import com.qt.repository.statistics.wf.YunweiDao;

@Service
public class YunweiService {
	
		private Logger logger = LoggerFactory.getLogger(YunweiService.class);
	
		@Resource
		private YunweiDao yunweiDao;
	 
	   public Map<String , Object> GetOptList(Map<String, Object> params)
	   {
			Map<String , Object> queryMap = new HashMap<String, Object>();
			Map<String , Object> resultMap = new HashMap<String, Object>();
			queryMap.putAll(params);
			
			List<Operator> list = yunweiDao.GetOptList(queryMap);
			List<Map<String , Object>> listarr = new ArrayList<Map<String , Object>>();
			
			for (Operator arr : list) {
				Map<String , Object> map = new HashMap<String , Object>();
				
				map.put("id", arr.getId());
				map.put("opt_id", arr.getOpt_id());
				map.put("opt_name", arr.getOpt_name());
				map.put("opt_sex", arr.getOpt_sex());
				map.put("opt_position", arr.getOpt_position());
				map.put("opt_company", arr.getOpt_company());
				map.put("opt_phone", arr.getOpt_phone());
				map.put("opt_dcc_id", arr.getOpt_dcc_id());
				map.put("opt_group", arr.getOpt_group());
				map.put("opt_total_time", arr.getOpt_total_time());
				map.put("opt_total_frequency", arr.getOpt_total_frequency());	
				map.put("optGroupName", arr.getOptGroupName());
				map.put("dccFirstName", arr.getDccFirstName());
				listarr.add(map);
			}
			resultMap.put("rows", listarr);
			resultMap.put("total", yunweiDao.GetCountOptList(queryMap));
			return resultMap;   
	   }
	   
	   /**
	    * 根据报警的指标码、报警级别。 查询运维人员
	    * @param params
	    * @return
	    */
	   public List<Operator> getOperForAlarmMSG(Map<String, Object> params){
			List<Operator>  resList = yunweiDao.getOperForAlarmMSG(params);			
			return resList;
	   }
	   
	   
	   
	   
	   
	   /**
	    * <AUTHOR>
	    * @date 2018年3月16日上午11:21:35
	    * @功能 获取人员list
	    */   
	   public List<Operator> GetOptLists(Map<String, Object> params)
	   {
			List<Operator>  resList = yunweiDao.GetOptLists(params);			
			return resList;
	   }
	   
	   public boolean InsertOperator( Operator operator){	
		   try {
		   			yunweiDao.InsertOperator(operator); 
		   			return true;  
				} catch (Exception e) {
					// TODO: handle exception
					return false;  
				}
		   
	   }
	   public boolean UpdateOperator( Operator operator)
	   {		   
		   try {
			   yunweiDao.UpdateOperator(operator);
	   			return true;  
			} catch (Exception e) {
				// TODO: handle exception
				return false;  
			}
	   }
	   
	   public boolean updataOptStationCheck( Operator operator)
	   {		   
		   try {
			   yunweiDao.updataOptStationCheck(operator);
	   			return true;  
			} catch (Exception e) {
				// TODO: handle exception
				return false;  
			}
	   }
	   
	   
	   public boolean DeleteOperator(String[] idArray)
	   {	
		   try {
			   HashMap<String , Object> params = new HashMap<String , Object>(); 
				for(String arr:idArray){
					params.put("oId", arr);
					yunweiDao.DeleteOperator(params);
				}
				return true;
		   } catch (Exception e) {
				// TODO: handle exception
				return false;  
			}
			
	   }
	   
	   /**
	    * <AUTHOR> 李强
	    * @date 2017年3月21日 14:34:17
	    * @功能 获取设备巡检列表
	    * @param params
	    * @return
	    */   
	   public List<Record> GetRecordList(Map<String, Object> params)
	   {
			Map<String , Object> queryMap = new HashMap<String, Object>();
			queryMap.putAll(params);
			List<Record>  resList = yunweiDao.GetRecordList(queryMap);			
			return resList;
	   }
	   public int GetCountRecordList(Map<String, Object> params)
	   {		   
			Map<String , Object> queryMap = new HashMap<String, Object>();
			queryMap.putAll(params);
		   return yunweiDao.GetCountRecordList(queryMap);  
	   }
	   public String InsertRecord(Record record)
	   {		   
		   yunweiDao.InsertRecord(record);
		   return record.getId();  
	   }
	   public int UpdateRecord(Record record)
	   {		   
		   return yunweiDao.UpdateRecord(record);  
	   }
	   public int DeleteRecord(Map<String, Object> params)
	   {		
			Map<String , Object> queryMap = new HashMap<String, Object>();
			queryMap.putAll(params);
		   return yunweiDao.DeleteRecord(queryMap);  
	   }
	   
	   public List<Record> GetYRecordList(Map<String, Object> params)
	   {
			Map<String , Object> queryMap = new HashMap<String, Object>();
			queryMap.putAll(params);
			List<Record>  resList = yunweiDao.GetYRecordList(queryMap);			
			return resList;
	   }
	   public int GetCountYRecordList(Map<String, Object> params)
	   {
			Map<String , Object> queryMap = new HashMap<String, Object>();
			queryMap.putAll(params);	
			return  yunweiDao.GetCountYRecordList(queryMap)	;
	   }
	   public String InsertYRecord(Record record)
	   {		   
		    yunweiDao.InsertYRecord(record);  
		    return record.getId();
	   }
	   public int UpdateYRecord(Record record)
	   {		   
		   return yunweiDao.UpdateYRecord(record);  
	   }
	   public int DeleteYRecord(Map<String, Object> params)
	   {		
			Map<String , Object> queryMap = new HashMap<String, Object>();
			queryMap.putAll(params);
		   return yunweiDao.DeleteYRecord(queryMap);  
	   }
	   
	   /**
		* <AUTHOR> 
		* @Description 排除查询对应opt_operat_employee表中除已绑定运维人员除外的人员列表
		* @date 2018年3月14日下午5:58:01  
		* @param 某站点已绑定的运维人员列表  params
		*/
	   public List<Operator> getToUsersNoChecked(Map<String,Object> params)
	   {		
		   return yunweiDao.getToUsersNoChecked(params);  
	   }
	   
	   /**
		* <AUTHOR> 
		* @Description 查询对应opt_operat_employee表中已绑定运维人员的列表
		* @date 2018年3月16日上午9:44:31  
		* @param 某站点已绑定的运维人员列表  params
		*/
	   public List<Operator> getToUsersChecked(Map<String,Object> params)
	   {		
		   return yunweiDao.getToUsersChecked(params);  
	   }
	   
}
