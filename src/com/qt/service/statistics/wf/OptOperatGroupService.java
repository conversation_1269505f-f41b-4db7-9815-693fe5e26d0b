package com.qt.service.statistics.wf;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DclLine;
import com.qt.entity.wf.OptOperatGroup;
import com.qt.repository.statistics.wf.OptOperatGroupDao;

@Service
public class OptOperatGroupService {
	
	private Logger logger = LoggerFactory.getLogger(OptOperatGroupService.class);

	/**
	 * @function  运维用户 分组
	 */
	
	@Resource
	private OptOperatGroupDao optOperatGroupDao;

	public Map<String, Object> GetOptList(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		
		resultMap.put("rows", optOperatGroupDao.getOperatGroupList(queryMap));
		resultMap.put("total", optOperatGroupDao.GetCountOptList(queryMap));
		return resultMap;   
		
	}

	public boolean DeleteOptGroup(String[] idArray) {
		try {
			   HashMap<String , Object> params = new HashMap<String , Object>(); 
				for(String arr:idArray){
					params.put("optId", arr);
					optOperatGroupDao.deleteByPk(params);
				}
				return true;
		   } catch (Exception e) {
				// TODO: handle exception
				return false;  
			}
	}

	public boolean InsertOptGroup(OptOperatGroup optOperatGroup) {
		try {
			optOperatGroupDao.insert(optOperatGroup); 
   			return true;  
		} catch (Exception e) {
			// TODO: handle exception
			return false;  
		}
	}

	public boolean UpdateOptGroup(OptOperatGroup optOperatGroup) {
		try {
			optOperatGroupDao.update(optOperatGroup); 
   			return true;  
		} catch (Exception e) {
			// TODO: handle exception
			return false;  
		}
	}

	
	
}
