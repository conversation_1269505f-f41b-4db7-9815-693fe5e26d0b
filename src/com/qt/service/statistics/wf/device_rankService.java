package com.qt.service.statistics.wf;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.wf.device_rank;
import com.qt.repository.statistics.wf.device_rankDao;

@Service
public class device_rankService {
	private Logger logger = LoggerFactory.getLogger(device_rankService.class);
	
	/**
	 * <AUTHOR> 
	 * @function  能效排名数据提取
	 */
	@Resource
	public device_rankDao Device_rankDao;
	//设备初始用电量 00:00:00
	public Map<String , List<device_rank>> Get_devicerank(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.get_devicerank(queryMap));	
		return resultMap;
	}
	//设备结束用电量 23:45:00
	public Map<String , List<device_rank>> Get_devicerankEnd(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.get_devicerankEnd(queryMap));	
		return resultMap;
	}
	//回路名称
	public Map<String , List<device_rank>> Get_linename(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.get_linename(queryMap));	
		return resultMap;
	}
	//根据站点取变压器数据
	public Map<String , List<device_rank>> Get_byqname(Map<String, Object> params)
	{
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.get_byqname(queryMap));	
		return resultMap;
	}
	//根据站点和变压器id取变压器数据
	public Map<String , List<device_rank>> Get_byqname_bybyqid(Map<String, Object> params)
	{
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.get_byqname_bybyqid(queryMap));	
		return resultMap;
	}
	//获取该站点下回路1,相减数据2
	//1
	public Map<String , List<device_rank>> Get_huilu(Map<String, Object> params)
	{
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.get_huilu(queryMap));	
		return resultMap;
	}
	//2
	public Map<String , List<device_rank>> Shuju(Map<String, Object> params)
	{
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.shuju(queryMap));	
		return resultMap;
	}
	//根据站点和变压器id取回路数据
	public Map<String , List<device_rank>> get_linesname_bybyqid(Map<String, Object> params)
	{
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<device_rank>> resultMap = new HashMap<String, List<device_rank>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Device_rankDao.get_linesname_bybyqid(queryMap));	
		return resultMap;
	}

}
