package com.qt.service.statistics.wf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.util.LinkedNode;
import com.qt.entity.wf.online_monitor;
import com.qt.repository.statistics.StlDayDao;
import com.qt.repository.statistics.wf.online_monitorDao;
import com.qt.service.statistics.StlDayService;


@Service
public class online_monitorService {
	private Logger logger = LoggerFactory.getLogger(online_monitorService.class);
	/**
	 * <AUTHOR> 李强
	 * @date 2017年2月14日23:25:30
	 * @function  指标联系数据获取
	 */
	
	
    @Resource
	private online_monitorDao Online_monitorDao;
    
	public  List<online_monitor> Get_indexassociation(Map<String, Object> params , String target)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		List<online_monitor> resList = new ArrayList<online_monitor>();//封装查询结果		
		queryMap.putAll(params);
		List<online_monitor> List = new ArrayList<online_monitor>();//封装查询结果	
		if(target.equals("line"))
		{
			List = Online_monitorDao.get_indexassociation(queryMap);	
		}else if(target.equals("byq"))
		{
			List = Online_monitorDao.get_BYQindexassociation(queryMap);	
		}
		else
		{
			List = Online_monitorDao.get_Stationindexassociation(queryMap);	
		}
		if(List.size() == 0)
		{
			return resList;
		}
		String tempTime=List.get(0).getSto_sync_time();
//		List<String> xAxisData = new ArrayList<String>();//封装查询结果
//		List<Double> PFList = new ArrayList<Double>();//封装查询结果	
//		List<Float> NPList = new ArrayList<Float>();//封装查询结果	
//		List<Float> HPList = new ArrayList<Float>();//封装查询结果	
//		List<Float> AIList = new ArrayList<Float>();//封装查询结果	
//		List<Float> BIList = new ArrayList<Float>();//封装查询结果	
//		List<Float> CIList = new ArrayList<Float>();//封装查询结果	
//		List<Float> AVList = new ArrayList<Float>();//封装查询结果	
//		List<Float> BVList = new ArrayList<Float>();//封装查询结果	
//		List<Float> CVList = new ArrayList<Float>();//封装查询结果	
		double PFvalue = List.get(0).getPower_factor();
		float NPvalue = List.get(0).getNohave_factor();
		float HPvalue = List.get(0).getHave_power();
		float AIvalue = List.get(0).getAelecCurrent();
		float BIvalue = List.get(0).getBelecCurrent();
		float CIvalue = List.get(0).getCelecCurrent();
		float AVvalue = List.get(0).getAVoltage();
		float BVvalue = List.get(0).getBVoltage();
		float CVvalue = List.get(0).getCVoltage();
		
		//LinkedHashMap<String, >
		
		for(online_monitor arr : List)
		{
			resList .add(arr);
			//System.out.println("arr.getSto_sync_time() = "+ arr.getSto_sync_time()+ "   tempTime"+ tempTime);
			//System.out.println("arr.getPower_factor() = "+arr.getPower_factor()+"  PFvalue="+PFvalue);
			/*if(!arr.getSto_sync_time().equals(tempTime))
			{
			
				online_monitor online_monitorArr = new online_monitor();
				online_monitorArr.setSto_sync_time(tempTime);
				online_monitorArr.setPower_factor(PFvalue);
				online_monitorArr.setHave_power(HPvalue);	
				online_monitorArr.setNohave_factor(NPvalue);
				online_monitorArr.setAelecCurrent(AIvalue);
				online_monitorArr.setBelecCurrent(BIvalue);
				online_monitorArr.setCelecCurrent(CIvalue);
				online_monitorArr.setAVoltage(AVvalue);
				online_monitorArr.setBVoltage(BVvalue);
				online_monitorArr.setCVoltage(CVvalue);				
				resList .add(online_monitorArr);

				tempTime = arr.getSto_sync_time();
				PFvalue = arr.getPower_factor();
				NPvalue = arr.getNohave_factor();
				HPvalue = arr.getHave_power();
				AIvalue = arr.getAelecCurrent();
				BIvalue = arr.getBelecCurrent();
				CIvalue = arr.getCelecCurrent();
				AVvalue = arr.getAVoltage();
				BVvalue = arr.getBVoltage();
				CVvalue = arr.getCVoltage();
			}
			else{
				PFvalue = PFvalue + arr.getPower_factor();
				NPvalue = NPvalue + arr.getNohave_factor();
				HPvalue = HPvalue + arr.getHave_power();
				AIvalue = AIvalue + arr.getAelecCurrent();
				BIvalue = BIvalue + arr.getBelecCurrent();
				CIvalue = CIvalue + arr.getCelecCurrent();
				AVvalue = AVvalue + arr.getAVoltage();
				BVvalue = BVvalue + arr.getBVoltage();
				CVvalue = CVvalue + arr.getCVoltage();		

			}*/
		}
		
//		online_monitor online_monitorArr2 = new online_monitor();
//		online_monitorArr2.setSto_sync_time(tempTime);
//		online_monitorArr2.setPower_factor(PFvalue);
//		online_monitorArr2.setHave_power(HPvalue);	
//		online_monitorArr2.setNohave_factor(NPvalue);
//		online_monitorArr2.setAelecCurrent(AIvalue);
//		online_monitorArr2.setBelecCurrent(BIvalue);
//		online_monitorArr2.setCelecCurrent(CIvalue);
//		online_monitorArr2.setAVoltage(AVvalue);
//		online_monitorArr2.setBVoltage(BVvalue);
//		online_monitorArr2.setCVoltage(CVvalue);
//		resList .add(online_monitorArr2);	
		return  resList;
	}
	
	/*
	 	public Map<String , List<online_monitor>> Get_indexassociation(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<online_monitor>> resultMap = new HashMap<String, List<online_monitor>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", Online_monitorDao.get_indexassociation(queryMap));	
		return resultMap;
	} 
	 */
	
	public  LinkedHashMap<String, Double> GetDay_PowerFactory(Map<String, Object> params )
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		LinkedHashMap<String ,Double> resMap = new 	LinkedHashMap<String ,Double>();
		queryMap.putAll(params);
		List<online_monitor> List = new ArrayList<online_monitor>();//封装查询结果	
		List = Online_monitorDao.get_Stationindexassociation(queryMap);	
		
		if(List.size() == 0)
		{
			return resMap;
		}
		String tempTime=List.get(0).getSto_sync_time();
		double PFvalue = 0.0;	
		for(online_monitor arr : List)
		{
			if(!arr.getSto_sync_time().equals(tempTime))
			{
				resMap.put(tempTime, PFvalue);
				tempTime = arr.getSto_sync_time();
				PFvalue = arr.getHave_power();

			}
			else{
				PFvalue = PFvalue + arr.getHave_power();
			}
		}
		resMap.put(tempTime, PFvalue);
		return  resMap;
	}
	
	//平台首页实时功率图表块
	public  LinkedHashMap<String, Double> GetDay_PowerFactory2(Map<String, Object> params )
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		LinkedHashMap<String ,Double> resMap = new 	LinkedHashMap<String ,Double>();
		queryMap.putAll(params);
		List<online_monitor> List = new ArrayList<online_monitor>();//封装查询结果	
		List = Online_monitorDao.get_Stationindexassociation2(queryMap);	
		if(List.size() == 0)
		{
			return resMap;
		}
		String tempTime;
		double PFvalue = 0.0;	
		for(online_monitor arr : List)
		{
			tempTime = arr.getSto_sync_time();
			PFvalue = arr.getHave_power();
			resMap.put(tempTime, PFvalue);
		}
		return  resMap;
	}
}
