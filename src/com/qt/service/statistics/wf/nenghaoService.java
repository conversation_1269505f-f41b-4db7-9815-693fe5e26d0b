package com.qt.service.statistics.wf;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.wf.device_rank;
import com.qt.entity.wf.nenghao;
import com.qt.repository.statistics.wf.nenghaoDao;

@Service
public class nenghaoService {
private Logger logger = LoggerFactory.getLogger(nenghaoService.class);
	
	/**
	 * <AUTHOR> 
	 * @function  能耗数据提取
	 */
	@Resource
	public nenghaoDao nenghaoDao;
	//获取同比天能耗
	public Map<String , List<nenghao>> Get_nenghaotbday(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaotbday(queryMap));	
		return resultMap;
	}
	
	//获取同比天基准能耗
	public Map<String , List<nenghao>> Get_nenghaojztbday(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaojztbday(queryMap));	
		return resultMap;
	}
	//获取同比周能耗
	public Map<String , List<nenghao>> Get_nenghaotbweek(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaotbweek(queryMap));	
		return resultMap;
	}
	//获取周基准能耗
	public Map<String , List<nenghao>> Get_nenghaojztbweek(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaojztbweek(queryMap));	
		return resultMap;
	}
	
	
	//变压器
	public Map<String , List<nenghao>> Get_nenghaotbday2(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaotbday2(queryMap));	
		return resultMap;
	}
	
	//获取同比天基准能耗
	public Map<String , List<nenghao>> Get_nenghaojztbday2(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaojztbday2(queryMap));	
		return resultMap;
	}
	//获取同比周能耗
	public Map<String , List<nenghao>> Get_nenghaotbweek2(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaotbweek2(queryMap));	
		return resultMap;
	}
	//获取周基准能耗
	public Map<String , List<nenghao>> Get_nenghaojztbweek2(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaojztbweek2(queryMap));	
		return resultMap;
	}
	
	
	//回路
	public Map<String , List<nenghao>> Get_nenghaotbday3(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaotbday3(queryMap));	
		return resultMap;
	}
	
	//获取同比天基准能耗
	public Map<String , List<nenghao>> Get_nenghaojztbday3(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaojztbday3(queryMap));	
		return resultMap;
	}
	//获取同比周能耗
	public Map<String , List<nenghao>> Get_nenghaotbweek3(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaotbweek3(queryMap));	
		return resultMap;
	}
	//获取周基准能耗
	public Map<String , List<nenghao>> Get_nenghaojztbweek3(Map<String, Object> params)
	{
		
		Map<String , Object> queryMap = new HashMap<String, Object>(); //封装查询条件
		Map<String , List<nenghao>> resultMap = new HashMap<String, List<nenghao>>();//封装查询结果		
		queryMap.putAll(params);
		resultMap.put("result", nenghaoDao.get_nenghaojztbweek3(queryMap));	
		return resultMap;
	}
}
