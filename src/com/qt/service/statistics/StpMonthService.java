package com.qt.service.statistics;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.qt.controller.APP.service.StpMonthVo;
import com.qt.entity.statistics.StpYear;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.common.utils.DateUtils;
import com.qt.entity.file.DclLine;
import com.qt.entity.statistics.StpMonth;
import com.qt.entity.statistics.StpMonthCol;
import com.qt.repository.statistics.StlMonthDao;
import com.qt.repository.statistics.StpMonthDao;
import com.qt.service.file.DclLineService;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-15 10:45:44
 */
@Service
public class StpMonthService{

	private Logger logger = LoggerFactory.getLogger(StpMonthService.class);
     
    @Resource
	private StpMonthDao stpMonthDao;
    
    @Resource
	private StlMonthDao stlMonthDao;
    
    @Autowired
	private DclLineService dclLineService;
	
	//根据条件搜索记录
	public Map<String , Object> getStpMonthList(Map<String, Object> params){
		Map<String, Object> queryMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StpMonth> list1 = new ArrayList<StpMonth>();
		List<StpMonth> list2 = new ArrayList<StpMonth>();
		if(params.get("stpByqId") == null){
			list1 =stpMonthDao.selectList(queryMap);
		}else{
			//查询变压器下面的回路
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			list1 =stpMonthDao.selectList(queryMap);
			for (int i = 0; i < list1.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list1.get(i).getStpLineId())){
						list2.add(list1.get(i));
					}
				}
			}
			list1 = list2;
		}
		resultMap.put("rows",list1);
		resultMap.put("total",list1.size());
		return resultMap;
	}
	
	//根据条件搜索记录
	public List<StpMonth> getStpMonthList2(Map<String, Object> params){
		Map<String, Object> queryMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StpMonth> list1 = new ArrayList<StpMonth>();
		List<StpMonth> list2 = new ArrayList<StpMonth>();
		if(params.get("stpByqId") == null){
			list1 =stpMonthDao.selectList(queryMap);
		}else{
			//查询变压器下面的回路
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			list1 =stpMonthDao.selectList(queryMap);
			for (int i = 0; i < list1.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list1.get(i).getStpLineId())){
						list2.add(list1.get(i));
					}
				}
			}
			list1 = list2;
		}
		return list1;
	}
	
	
	public List<StpMonth> getAllStpMonth(Map<String, Object> params){
		//return stpMonthDao.selectList(params);
		Map<String, Object> queryMap = new HashMap<String, Object>();
		List<StpMonth> retList = new ArrayList<StpMonth>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		
		String x=(String) queryMap.get("stpStationId");
		if(params.get("stpByqId") == null){
			if(params.get("stpLineId") == null){
				for(DclLine dclLine:lineList){
					Integer s=dclLine.getDclDcsId();
					String ss=s.toString();
					if(ss.equals(x)){
						queryMap.put("stpLineId", dclLine.getDclId());
						queryMap.put("stpStationId", dclLine.getDclDcsId());
						List<StpMonth> list1 =stpMonthDao.selectList(queryMap);
						retList.addAll(list1);
					}
				}
			}else{
				queryMap.put("stpLineId", params.get("stpLineId"));
				queryMap.put("stpStationId", params.get("stpStationId"));
				
				List<StpMonth> list2 =stpMonthDao.selectList(queryMap);
				retList.addAll(list2);
			}
		}else{
			//查询变压器下面的回路
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			for(DclLine dclLine:allDclLine){
				queryMap.put("stpStationId", dclLine.getDclDcsId());
				queryMap.put("stpLineId", dclLine.getDclId());
				List<StpMonth> list3 =stpMonthDao.selectList(queryMap);
				retList.addAll(list3);
			}
		}
		
		return retList;
	}
	
	
	//系统概览本月累计电量0921
	public Map<String, Object> getMonthSum(Map<String, Object> params){
		Map<String, Object> result = new HashMap<String, Object>();
		params.put("month", 0);
		result.put("benyue", stpMonthDao.selectMonthSum(params));
		params.put("month", 1);
		result.put("syue", stpMonthDao.selectMonthSum(params));
		params.put("month", 2);
		result.put("ssyue", stpMonthDao.selectMonthSum(params));
		return result;
	}
	
	//系统概览本月累计电量0921
		public Map<String, Object> getMonthSum1(Map<String, Object> params){
			Map<String, Object> result = new HashMap<String, Object>();
			params.put("month", 0);
			result.put("yue", stlMonthDao.selectMonthSum1(params));
			params.put("month", 1);
			result.put("nian", stlMonthDao.selectMonthSum2(params));
			return result;
		}
	
	
	/**
	 * 根据站点或者回路查询每天中的总值
	 * @param params
	 * @return
	 */
	public Set<Map<String, Object>> getForMonth(Map<String,Object> params){
		List<StpMonth> stpMonths = stpMonthDao.selectForMonth(params);
		Date stpSyncTime = (Date) params.get("stpSyncTime");
		Date time = stpSyncTime;
		time.setDate(1);
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
		for(int i = 0; i < 31; i++){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			for(StpMonth stpMonth : stpMonths){
				System.out.println("time"+time);
				System.out.println("stpMonth.getStpSyncTime()."+stpMonth.getStpSyncTime());
				if(stpMonth.getStpSyncTime().equals(time)){
					map.put("d", stpMonth.getStpElecValue());
				}
			}
			map.put("t", DateUtils.formatDate(time, "dd"));
			resultLine.add(map);
			time = new Date(time.getTime()+24*60*60*1000);//每隔一天
		}
		return resultLine;
	}
	
	public Set<Map<String, Object>> selecDlMonth(Map<String,Object> params){
		List<StpMonth> stpMonths = stpMonthDao.selecDlMonth(params);
		Date stpSyncTime = (Date) params.get("stpSyncTime");
		Date time = stpSyncTime;
		time.setDate(1);
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
		for(int i = 0; i < 31; i++){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			for(StpMonth stpMonth : stpMonths){
				System.out.println("time"+time);
				System.out.println("stpMonth.getStpSyncTime()."+stpMonth.getStpSyncTime());
				if(stpMonth.getStpSyncTime().equals(time)){
					map.put("d", stpMonth.getStpElecValue());
				}
			}
			map.put("t", DateUtils.formatDate(time, "dd"));
			resultLine.add(map);
			time = new Date(time.getTime()+24*60*60*1000);//每隔一天
		}
		return resultLine;
	}
	
	/**
	 * 
	* <AUTHOR> 
	* @Description  月报表行转列
	* @date 2018年7月3日下午4:43:15  
	 */
	public Map<String , Object> getStpMonthColList(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<StpMonthCol> list =stpMonthDao.selectForMonthToCol(params);
		resultMap.put("rows",list);
		params.remove("offset");
		params.remove("limit");
		List<StpMonthCol> list1 =stpMonthDao.selectForMonthToCol(params);
		resultMap.put("total",list1.size());
		return resultMap;
	}
	
	public Map<String , Object> getStpMonthColByIntervalList(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<StpMonthCol> list =stpMonthDao.selectForMonthToColByIntervalList(params);
		resultMap.put("rows",list);
		params.remove("offset");
		params.remove("limit");
		List<StpMonthCol> list1 =stpMonthDao.selectForMonthToColByIntervalList(params);
		resultMap.put("total",list1.size());
		return resultMap;
	}

	/**
	 * 查询等效小时前五
	 * @param params
	 * @return
	 */
	public List<Map<String,Object>> selHourTopFive(Map<String,Object> params){
		return stpMonthDao.selHourTopFive(params);
	}

	public List<StpMonth> selecElecMonth(Map<String, Object> params){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
		String dayTime = sdf.format(new Date());
		params.put("dayTimeOne",sdf2.format(new Date()));
		params.put("dayTime",dayTime);
		String monthTime = dayTime.substring(0,4);
		params.put("monthTime",monthTime);
		return stpMonthDao.selecElecMonth(params);
	}

	public List<StpMonth> selectYear(Map<String, Object> params) {
		return stpMonthDao.selectYear(params);
	}

	public List<Map<String, Object>> getQuarter(Map<String, Object> params) {
		return stpMonthDao.getQuarter(params);
	}

	public List<StpMonth> getMonthElecByInverterIds(Map<String, Object> params) {
		return stpMonthDao.getMonthElecByInverterIds(params);
	}

	public List<StpMonthVo> getYrarElecByinverterId(Map<String, Object> params) {
		return stpMonthDao.getYrarElecByinverterId(params);
	}
}
