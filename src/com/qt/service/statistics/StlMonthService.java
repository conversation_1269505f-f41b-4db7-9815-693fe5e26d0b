package com.qt.service.statistics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.entity.file.DclLine;
import com.qt.entity.statistics.StlDay;
import com.qt.entity.statistics.StlMonth;
import com.qt.repository.statistics.StlMonthDao;
import com.qt.service.file.DclLineService;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-16 10:07:52
 */
@Service
public class StlMonthService{

	private Logger logger = LoggerFactory.getLogger(StlMonthService.class);
     
    @Resource
	private StlMonthDao stlMonthDao;
    
    @Autowired
	private DclLineService dclLineService;
	
  //根据条件搜索记录
  	public Map<String , Object> getStlMonthFHList(Map<String, Object> params){
  		Map<String , Object> queryMap = new HashMap<String, Object>();
  		Map<String , Object> resultMap = new HashMap<String, Object>();
  		//TODO 根据需要封装查询需要的条件
  		queryMap.putAll(params);
  		List<StlMonth> list = stlMonthDao.selectFHList(queryMap);
  		resultMap.put("rows", list);
  		resultMap.put("total", list.size());
  		return resultMap;
  	}
    
  	public Map<String , Object> getStlFHMonthList(Map<String, Object> params){
  		Map<String , Object> queryMap = new HashMap<String, Object>();
  		Map<String , Object> resultMap = new HashMap<String, Object>();
  		//TODO 根据需要封装查询需要的条件
  		queryMap.putAll(params);
  		List<StlMonth> list = stlMonthDao.selectList(queryMap);
  		resultMap.put("rows", list);
  		resultMap.put("total", list.size());
  		return resultMap;
  	}
  	
  	
	//根据条件搜索记录
	public Map<String , Object> getStlMonthList(Map<String, Object> params){
		/*Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", stlMonthDao.selectList(queryMap));
		resultMap.put("total", stlMonthDao.selectCount(queryMap));
		return resultMap;*/
		System.out.println("--------------进入getStpDayList-------------------");
		Map<String, Object> queryMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StlMonth> list = new ArrayList<StlMonth>();
		List<StlMonth> list1 = new ArrayList<StlMonth>();
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				list =stlMonthDao.selectList(queryMap);
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				list =stlMonthDao.selectList(queryMap);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			//System.out.println("----------allDclLine------------"+allDclLine.size());
			
			list =stlMonthDao.selectList(queryMap);
			for (int i = 0; i < list.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list.get(i).getStlLineId())){
						list1.add(list.get(i));
					}
				}
			}
			list = list1;
		}
		
		resultMap.put("rows",list);
		resultMap.put("total",list.size());
		return resultMap;
	}
	
	//根据条件搜索记录--导出
	public List<StlMonth> getStlMonthList2(Map<String, Object> params){
		System.out.println("--------------进入getStpDayList-------------------");
		Map<String, Object> queryMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StlMonth> list = new ArrayList<StlMonth>();
		List<StlMonth> list1 = new ArrayList<StlMonth>();
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				list =stlMonthDao.selectList(queryMap);
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				list =stlMonthDao.selectList(queryMap);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			//System.out.println("----------allDclLine------------"+allDclLine.size());
			
			list =stlMonthDao.selectList(queryMap);
			for (int i = 0; i < list.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list.get(i).getStlLineId())){
						list1.add(list.get(i));
					}
				}
			}
			list = list1;
		}
		return list;
	}
  
	public List<StlMonth> getAllStlMonth(Map<String, Object> params){
		//return stlMonthDao.selectList(params);
		Map<String, Object> queryMap = new HashMap<String, Object>();
		List<StlMonth> retList = new ArrayList<StlMonth>();
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		
		String x =(String) queryMap.get("stlStationId");
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				for(DclLine dclLine:lineList){
					Integer s =	dclLine.getDclDcsId();
					String ss = s.toString();
					if(ss.equals(x)){
						queryMap.put("stlLineId", dclLine.getDclId());
						queryMap.put("stlStationId", dclLine.getDclDcsId());
						List<StlMonth> list1 =stlMonthDao.selectList(queryMap);
						if(list1.size() !=0){
							retList.addAll(list1);
						}	
					}
				}
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				List<StlMonth> list2 =stlMonthDao.selectList(queryMap);
				retList.addAll(list2);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			//System.out.println("----------allDclLine------------"+allDclLine.size());
			
			for(DclLine d:allDclLine){
				queryMap.put("stlStationId", d.getDclDcsId());
				queryMap.put("stlLineId", d.getDclId());
				List<StlMonth> list3 =stlMonthDao.selectList(queryMap);
				retList.addAll(list3);
			}
		}
		return retList;
	}
	
	
	/**
	 * 负荷分析——查询年报表列表（带条件）   
	 * 因stlmonth存放的是每月数据，所以修复查询条件改为年  20180720
	 * @param params
	 * @return
	 * @throws JSONException
	 */
	public List<StlMonth> selectStlMonthToYearList(Map<String, Object> params) {
		return stlMonthDao.selectStlMonthToYearList(params);
	}
	
	/**
	 * 负荷分析——查询年报表列表总数（带条件）   
	 * 因stlmonth存放的是每月数据，所以修复查询条件改为年  20180720
	 * @param params
	 * @return
	 * @throws JSONException
	 */
	public int selectCountToYear(Map<String, Object> params) {
		return stlMonthDao.selectCountToYear(params);
	}
}
