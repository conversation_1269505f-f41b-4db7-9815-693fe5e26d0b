package com.qt.service.statistics;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.statistics.EvrAlarm;
import com.qt.repository.statistics.EvrAlarmDao;

/**
 * TODO shaobo
 * @date 2016-09-08 10:07:33
 */
@Service
public class EvrAlarmService{

	private Logger logger = LoggerFactory.getLogger(EvrAlarmService.class);
     
    @Resource
	private EvrAlarmDao evrAlarmDao;

	//根据条件搜索记录
	public Map<String , Object> getEvrAlarmList(Map<String, Object> params){
		//Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		//queryMap.putAll(params);
		resultMap.put("rows", evrAlarmDao.selectList(params));
		resultMap.put("total", evrAlarmDao.selectCount(params));
		return resultMap;
	}
	
	//根据条件搜索记录-从登陆开始的时间到现在为止的数据
	public Map<String , Object> list(Map<String, Object> params){
		//Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		//queryMap.putAll(params);
		resultMap.put("rows", evrAlarmDao.selectList2(params));
		resultMap.put("total", evrAlarmDao.selectCount(params));
		return resultMap;
	}
	
	public List<EvrAlarm> list2(Map<String, Object> params){
		return evrAlarmDao.selectList2(params);
	}
	//查当日未处理的报警
	public  Map<String , Object> list10(Map<String, Object> params){
		//Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		//queryMap.putAll(params);
		resultMap.put("rows", evrAlarmDao.selectList10(params));
		resultMap.put("total", evrAlarmDao.selectCount(params));
		return resultMap;
	}
	
	
	public Map<String , Object> list11(Map<String, Object> params){
		//Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		//queryMap.putAll(params);
		resultMap.put("rows", evrAlarmDao.selectList11(params));
		resultMap.put("total", evrAlarmDao.selectCount(params));
		return resultMap;
	}
	//导出报警信息查询
	public List<EvrAlarm> getEvrAlarmForExport(Map<String, Object> params){
		return evrAlarmDao.selectList11(params);
	}
	
	
	public List<EvrAlarm> getAllEvrAlarm(Map<String, Object> params){
		return evrAlarmDao.selectList(params);
	}
	
	//统计实时数据
	public int count(Map<String, Object> params){
		return evrAlarmDao.selectCount(params);
	}
	
	//统计实时总数
	public int count2(Map<String, Object> params){
		return evrAlarmDao.selectCount2(params);
	}
		
	//统计实时总数
	public int count4(Map<String, Object> params){
		return evrAlarmDao.selectCount4(params);
	}
	
	//统计未处理总数
	public int count5(Map<String, Object> params){
		return evrAlarmDao.selectCount5(params);
	}
	
	/*查询站点是否有告警*/ 
	public List<EvrAlarm> selectAlarm(){
		return evrAlarmDao.selectAlarm();
	}
	/*查询所有站点当天是否有告警*/ 
	public List<EvrAlarm> selectTodayAlarm(Map<String, Object> params){
		return evrAlarmDao.selectTodayAlarm(params);
	}
	
	//删除记录
	public int delEvrAlarm(String pk){
		return evrAlarmDao.deleteByPk(pk);
	}
	
	//删除记录
	public int delEvrAlarmByBianhao(String bianhao){
		return evrAlarmDao.deleteByBianhao(bianhao);
	}

	public int deleteByBianhaoList(Map<String, Object> params){
		return evrAlarmDao.deleteByBianhaoList(params);
	}

	//新增记录
	public void addEvrAlarm(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			evrAlarmDao.insert(params);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//报警表格根据条件搜索记录
	public Map<String , Object> getEvrAlarmList2(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", evrAlarmDao.selectList3(queryMap));
		resultMap.put("total", evrAlarmDao.selectCount2(queryMap));
		return resultMap;
	}
	
	
	/*按照时间区间、站点、报警级别查询的新报警*/ 
	public Map<String , Object> getEvrAlarmList3(Map<String, Object> params){
		Map<String , Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows", evrAlarmDao.selectList4(params));
		resultMap.put("total", evrAlarmDao.selectCount3(params));
		return resultMap;
	}
	//原值
	//根据采集编码查询对应的总条数
	public Map<String , Object> count3(Map<String, Object> params){
		Map<String , Object> map = new HashMap<String, Object>();
		//Map<String , Object> map = evhAlarmDao.count(params);
		int value1 = 0;
		if (value1 == 0) {
			params.put("evrBsaCode", 10001);
			int A1 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10002);
			int A2 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10003);
			int A3 = evrAlarmDao.count(params);
			value1 = A1+A2+A3;
		}
		int value2 = 0;
		if (value2 == 0) {
			params.put("evrBsaCode", 10004);
			int A1 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10005);
			int A2 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10006);
			int A3 = evrAlarmDao.count(params);
			value2 = A1+A2+A3;
		}
		int value3 = 0;
		if (value3 == 0) {
			params.put("evrBsaCode", 10010);
			int A1 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10011);
			int A2 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10012);
			int A3 = evrAlarmDao.count(params);
			value3 = A1+A2+A3;
		}
		int value4 = 0;
		if (value4 == 0) {
			params.put("evrBsaCode", 10007);
			int A1 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10008);
			int A2 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10009);
			int A3 = evrAlarmDao.count(params);
			value4 = A1+A2+A3;
		}
		int value5 = 0;
		if (value5 == 0) {
			params.put("evrBsaCode", 10073);
			int A1 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10074);
			int A2 = evrAlarmDao.count(params);
			params.put("evrBsaCode", 10075);
			int A3 = evrAlarmDao.count(params);
			value5 = A1+A2+A3;
		}
		map.put("value5", value5);
		map.put("value4", value4);
		map.put("value3", value3);
		map.put("value2", value2);
		map.put("value1", value1);
		return map;
	}
	
	
	//根据采集编码查询对应的总条数--告警分析环比
		public Map<String , Object> countHB(Map<String, Object> params){
			Map<String , Object> map = new HashMap<String, Object>();
			//Map<String , Object> map = evhAlarmDao.count(params);
			int value1 = 0;
			if (value1 == 0) {
				params.put("evrBsaCode", 10001);
				int A1 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10002);
				int A2 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10003);
				int A3 = evrAlarmDao.count1(params);
				value1 = A1+A2+A3;
			}
			int value2 = 0;
			if (value2 == 0) {
				params.put("evrBsaCode", 10004);
				int A1 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10005);
				int A2 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10006);
				int A3 = evrAlarmDao.count1(params);
				value2 = A1+A2+A3;
			}
			int value3 = 0;
			if (value3 == 0) {
				params.put("evrBsaCode", 10010);
				int A1 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10011);
				int A2 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10012);
				int A3 = evrAlarmDao.count1(params);
				value3 = A1+A2+A3;
			}
			int value4 = 0;
			if (value4 == 0) {
				params.put("evrBsaCode", 10007);
				int A1 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10008);
				int A2 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10009);
				int A3 = evrAlarmDao.count1(params);
				value4 = A1+A2+A3;
			}
			int value5 = 0;
			if (value5 == 0) {
				params.put("evrBsaCode", 10073);
				int A1 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10074);
				int A2 = evrAlarmDao.count1(params);
				params.put("evrBsaCode", 10075);
				int A3 = evrAlarmDao.count1(params);
				value5 = A1+A2+A3;
			}
			map.put("value5", value5);
			map.put("value4", value4);
			map.put("value3", value3);
			map.put("value2", value2);
			map.put("value1", value1);
			return map;
		}
		
		/**
		 * 根据站点查询报警天数
		 * <AUTHOR>
		 */
		public int selectDays(Map<String,Object> params){
			return evrAlarmDao.selectDays(params);
		}
		
		//更新记录
		public void updateEvrAlarm(Map<String, Object> params){
			evrAlarmDao.update(params);
		}
		
		/**
		 * 根据时间查询最近的一次报警，用于首页生成报警提示声
		 * @param params
		 * @return
		 */
		public EvrAlarm getNearEvrAlarm(Map<String, Object> params){
			return evrAlarmDao.getNearEvrAlarm(params);
		}
		
		public List<EvrAlarm> selectToday(Map<String, Object> params){
			return evrAlarmDao.selectToday(params);
		}
		
		//获取evrAlarm中最早未处理报警的时间
		public String getEvrStratTime(){
			return evrAlarmDao.getEvrStratTime();
		}

		public Map<String, Object> getCallPolice(Map<String, Object> params) {
			Map<String , Object> resultMap = new HashMap<String, Object>();
			resultMap.put("rows", evrAlarmDao.getCallPolice(params));
			return resultMap;
		}

	/**
	 * 实时报警-聚合列表
	 * @param params
	 * @return
	 */
	public Map<String , Object> selectListForTyper(Map<String, Object> params){
		Map<String , Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows", evrAlarmDao.selectListForTyperList(params));
		resultMap.put("total", evrAlarmDao.selectListForTyperCount(params));
		return resultMap;
	}
	
	/**
	 * 查询报警的总数
	 * @param params
	 * @return
	 */
	public int selectCountByStation(Map<String,Object> params) {
		return evrAlarmDao.selectCountByStation(params);
	}
	
	/**
	 * 大屏地图的数据查询
	 * @param params
	 * @return
	 */
	public List<EvrAlarm> selectListByStation(Map<String,Object> params) {
		return evrAlarmDao.selectListByStation(params);
	}

	/**
	 * 查全国各省站点数量
	 * @param params
	 * @return
	 */
	public List<Map<String,Object>> selForProvinceNum(Map<String,Object> params) {
		return evrAlarmDao.selForProvinceNum(params);
	}

	/**
	 * 查某省站点数量
	 * @param params
	 * @return
	 */
	public List<Map<String,Object>> selForCityNum(Map<String,Object> params){
		return evrAlarmDao.selForCityNum(params);
	}

	public int selectCountForNbq (Map<String,Object> params){return evrAlarmDao.selectCountForNbq(params);}

	public int selectCountForStation (Map<String,Object> params){return evrAlarmDao.selectCountForStation(params);}
}
