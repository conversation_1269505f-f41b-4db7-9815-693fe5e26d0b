package com.qt.service.statistics;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.statistics.StoOriginal;
import com.qt.entity.statistics.StpOriginal;
import com.qt.repository.statistics.StpOriginalDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-16 11:37:26
 */
@Service
public class StpOriginalService{

	private Logger logger = LoggerFactory.getLogger(StpOriginalService.class);
     
    @Resource
	private StpOriginalDao stpOriginalDao;
	
	//根据条件搜索记录
	public Map<String , Object> getStpOriginalList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", stpOriginalDao.selectList(queryMap));
		resultMap.put("total", stpOriginalDao.selectCount(queryMap));
		return resultMap;
	}
 
	public List<StpOriginal> getAllStpOriginal(Map<String, Object> params){
		return stpOriginalDao.selectList(params);
	}
	
	/**
	 * 查询某一站点一天所有采集因子的总值(电量)
	 * @param params
	 * @return
	 */
	public List<StpOriginal> getStationValue(Map<String,Object> params){
		List<StpOriginal> stpOriginals = stpOriginalDao.selectStationValue(params);
		return stpOriginals;
	}
}
