package com.qt.service.statistics;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.qt.entity.statistics.*;
import org.nutz.dao.TableName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.common.utils.DateUtils;
import com.qt.entity.file.DclLine;
import com.qt.entity.file.DcsOtherInfo;
import com.qt.repository.file.DcsOtherInfoDao;
import com.qt.repository.file.DcsStationDao;
import com.qt.repository.statistics.StpDayDao;
import com.qt.repository.statistics.StpMonthDao;
import com.qt.repository.statistics.StpYearDao;
import com.qt.service.file.DclLineService;

/**
 * TODO 本代码由代码生成工具生成
 * 
 * @date 2016-08-15 06:35:47
 */
@Service
public class StpDayService {
	private Logger logger = LoggerFactory.getLogger(StpDayService.class);
	@Resource
	private StpDayDao stpDayDao;
	@Resource
	private DcsOtherInfoDao dcsOtherInfoDao;
	@Resource
	private DcsStationDao dcsStationDao;
	@Resource
	private StpYearDao stpYearDao;
	@Resource
	private StpMonthDao stpMonthDao;
	@Autowired
	private DclLineService dclLineService;
	
	// 根据条件搜索记录
	public Map<String, Object> getStpDayList(Map<String, Object> params) {
		System.out.println("--------------进入getStpDayList-------------------");
		Map<String, Object> queryMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StpDay> list1 = new ArrayList<StpDay>();
		List<StpDay> list2 = new ArrayList<StpDay>();
		if(params.get("stpByqId") == null || "".equals(params.get("stpByqId"))){
			list1 =stpDayDao.selectList(queryMap);
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			list1 =stpDayDao.selectList(queryMap);
			for (int i = 0; i < list1.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list1.get(i).getStpLineId())){
						list2.add(list1.get(i));
					}
				}
			}
			list1 = list2;
		}
		resultMap.put("rows",list1);
		resultMap.put("total",list1.size());
		return resultMap;
	}
	
	
	
	// 根据条件搜索记录--导出
	public List<StpDay> getStpDayList2(Map<String, Object> params) {
		System.out.println("--------------进入getStpDayList-------------------");
		Map<String, Object> queryMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StpDay> list1 = new ArrayList<StpDay>();
		List<StpDay> list2 = new ArrayList<StpDay>();
		if(params.get("stpByqId") == null || "".equals(params.get("stpByqId"))){
			list1 =stpDayDao.selectList(queryMap);
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			list1 =stpDayDao.selectList(queryMap);
			for (int i = 0; i < list1.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list1.get(i).getStpLineId())){
						list2.add(list1.get(i));
					}
				}
			}
			list1 = list2;
		}
		return list1;
	}
	

	public List<StpDay> getAllStpDay(Map<String, Object> params) {
		//return stpDayDao.selectList(params);
		System.out.println("--------------进入getStpDayList-------------------");
		Map<String, Object> queryMap = new HashMap<String, Object>();
		List<StpDay> retList = new ArrayList<StpDay>();
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		String x =(String) queryMap.get("stpStationId");
		if(params.get("stpByqId") == null || "".equals(params.get("stpByqId"))){
			if(params.get("stpLineId") == null || "".equals(params.get("stpLineId"))){
				for(DclLine dclLine:lineList){
					Integer s =	dclLine.getDclDcsId();
					String ss = s.toString();
					if(ss.equals(x)){
						queryMap.put("stpLineId", dclLine.getDclId());
						queryMap.put("stpStationId", dclLine.getDclDcsId());
						List<StpDay> list1 =stpDayDao.selectList(queryMap);
						if(list1.size() !=0){
							retList.addAll(list1);
						}	
					}
				}
			}else{
				queryMap.put("stpLineId", params.get("stpLineId"));
				queryMap.put("stpStationId", params.get("stpStationId"));
				
				List<StpDay> list2 =stpDayDao.selectList(queryMap);
				retList.addAll(list2);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			System.out.println("----------allDclLine------------"+allDclLine.size());
			
			for(DclLine d:allDclLine){
				queryMap.put("stpStationId", d.getDclDcsId());
				queryMap.put("stpLineId", d.getDclId());
				List<StpDay> list3 =stpDayDao.selectList(queryMap);
				retList.addAll(list3);
			}
		}
		return retList;
	}
	
	//导出
	public List<StpDay> getAllStpDayDC(Map<String, Object> params) {
		return stpDayDao.selectListDC(params);
	}
	
	//原始值查询
	public List<StpDay> listTJ(Map<String, Object> params) {
		return stpDayDao.selectListTJ1(params);
	}
	
	
	//查询一天中电量最大值最小值（带站点ID，回路ID，时间）
	public List<Map<String, Object>> getAllStpDayDL(Map<String, Object> params) {
		return stpDayDao.selectListDL(params);
	}

	/**
	 * 根据站点或者回路查询每个小时中的总值
	 * 
	 * @param params
	 * @return
	 */
	
	public Set<Map<String, Object>> selectDlDay(Map<String,Object> params){
		List<StpDay> stpDays = stpDayDao.selectDlDay(params);
		Date stpSyncTime = (Date) params.get("stpSyncTime");
		Date time = stpSyncTime;
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//链表结构, 进去什么顺序出来就是什么顺序, 不重复
		for(int i = 0; i < 24; i++){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", "");
			for(StpDay stpDay : stpDays){
				if(stpDay.getStpSyncTime().equals(time)){
					map.put("d", stpDay.getStpElecValue());
				}
			}
			map.put("t", DateUtils.formatDate(time, "HH"));
			resultLine.add(map);
			time = new Date(time.getTime()+60*60*1000);//每隔1小时
		}
		return resultLine;
	}
	
	
	public Set<Map<String, Object>> getForDay(Map<String,Object> params){
		Map<String, Object> result = new HashMap<String, Object>();
		List<StpDay> stpDays = stpDayDao.selectForDay(params);
		Date stpSyncTime = (Date) params.get("stpSyncTime");
		Date time = stpSyncTime;
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//链表结构, 进去什么顺序出来就是什么顺序, 不重复
		String time1;
		String time2;
		for(int i = 0; i < 24; i++){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			for(StpDay stpDay : stpDays){
				time1=DateUtils.formatDate(time, "yyyy-MM-dd HH:mm") ;
				time2=stpDay.getStpSyncTime();
				if(time2.equals(time1)){
					map.put("d", stpDay.getStpElecValue());
				}
			}
			map.put("t", DateUtils.formatDate(time, "HH"));
			resultLine.add(map);
			time = new Date(time.getTime()+60*60*1000);//每隔1小时
		}
		return resultLine;
	}
	
	//分时用电月数据原值分析
	public Set<Map<String, Object>> getForDayFenShi(Map<String,Object> params){
		List<StpDay> stpDays = stpDayDao.selectForDayFenShi(params);
		DcsOtherInfo DcsOtherInfo = dcsOtherInfoDao.selectByPk((Integer)params.get("stpStationId"));
		Date stpSyncTime = (Date) params.get("stpSyncTime");
		Date time = stpSyncTime;
		time.setDate(1);
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//链表结构, 进去什么顺序出来就是什么顺序, 不重复
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(time);
		for(int i = 0; i < 31; i++){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("f", 0);//峰总和
			map.put("p", 0);//平总和
			map.put("g", 0);//谷总和
			map.put("j", 0);//尖总和
			float fSum = 0;
			float pSum = 0;
			float gSum = 0;
			float jSum = 0;
			String da1 = sdf.format(calendar.getTime());
			map.put("t", da1.substring(8, 10));
			
			//峰时间1
			String fStart1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFStarttime1();
			String fEnd1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFEndtime1();
			//峰时间2
			String fStart2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFStarttime2();
			String fEnd2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFEndtime2();
			
			//平时间1
			String pStart1 = da1.substring(0, 10) + " " + DcsOtherInfo.getDcsOtherPStarttime1();
			String pEnd1 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherPEndtime1();
			//平时间2
			String pStart2 = da1.substring(0, 10) + " " + DcsOtherInfo.getDcsOtherPStarttime2();
			String pEnd2 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherPEndtime2();
			
			//谷时间1
			String gStart1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherGStarttime1();
			String gEnd1 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherGEndtime1();
			//谷时间2
			String gStart2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherGStarttime2();
			String gEnd2 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherGEndtime2();
			
			//尖时间1
			String jStart1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJStarttime1();
			String jEnd1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJEndtime1();
			//尖时间2
			String jStart2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJStarttime2();
			String jEnd2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJEndtime2();
			
			for(StpDay stpDay : stpDays){
				String bjTime = sdf.format(stpDay.getStpSyncTime());
				bjTime = bjTime.substring(0, 16);
				//峰计算
				if(DcsOtherInfo.getDcsOtherFStarttime1() != null && DcsOtherInfo.getDcsOtherFEndtime1()!= null ){
					if(bjTime.compareTo(fStart1) >=0&&bjTime.compareTo(fEnd1)<=0){
						fSum +=stpDay.getStpElecValue();
					}
				}
				if(DcsOtherInfo.getDcsOtherFStarttime2() != null && DcsOtherInfo.getDcsOtherFEndtime2()!= null ){
					if(bjTime.compareTo(fStart2) >=0&&bjTime.compareTo(fEnd2)<=0){
						fSum +=stpDay.getStpElecValue();
					}
				}
				
				//平计算
				if( DcsOtherInfo.getDcsOtherPStarttime1() != null && DcsOtherInfo.getDcsOtherPEndtime1()!= null ){
					if(bjTime.compareTo(pStart1) >=0&&bjTime.compareTo(pEnd1)<=0){
						pSum +=stpDay.getStpElecValue();
					}
				}
				if( DcsOtherInfo.getDcsOtherPStarttime2() != null && DcsOtherInfo.getDcsOtherPEndtime2()!= null ){
					if(bjTime.compareTo(pStart2) >=0&&bjTime.compareTo(pEnd2)<=0){
						pSum +=stpDay.getStpElecValue();
					}
				}
				
				//谷计算
				if( DcsOtherInfo.getDcsOtherGStarttime1() != null && DcsOtherInfo.getDcsOtherGEndtime1()!= null ){
					if(bjTime.compareTo(gStart1) >=0&&bjTime.compareTo(gEnd1)<=0){
						gSum +=stpDay.getStpElecValue();
					}
				}
				if( DcsOtherInfo.getDcsOtherGStarttime2() != null && DcsOtherInfo.getDcsOtherGEndtime2()!= null ){
					if(bjTime.compareTo(gStart2) >=0&&bjTime.compareTo(gEnd2)<=0){
						gSum +=stpDay.getStpElecValue();
					}
				}
				
				//尖计算
				if( DcsOtherInfo.getDcsOtherJStarttime1() != null && DcsOtherInfo.getDcsOtherJEndtime1()!= null ){
					if(bjTime.compareTo(jStart1) >=0&&bjTime.compareTo(jEnd1)<=0){
						jSum +=stpDay.getStpElecValue();
					}
				}
				if( DcsOtherInfo.getDcsOtherJStarttime2() != null && DcsOtherInfo.getDcsOtherJEndtime2()!= null ){
					if(bjTime.compareTo(jStart2) >=0&&bjTime.compareTo(jEnd2)<=0){
						jSum +=stpDay.getStpElecValue();
					}
				}
			}
			map.put("f", fSum);
			map.put("p", pSum);
			map.put("g", gSum);
			map.put("j", jSum);
			resultLine.add(map);
			calendar.add(Calendar.DAY_OF_MONTH, 1);//每隔一天
		}
		return resultLine;
	}
	//分时用电月数据对比分析
	public Map<String, Object> getForFenShiDuiBi(Map<String,Object> params){
		List<StpDay> stpDays = stpDayDao.selectForDayFenShi(params);
		DcsOtherInfo DcsOtherInfo = dcsOtherInfoDao.selectByPk((Integer)params.get("stpStationId"));
		Date stpSyncTime = (Date) params.get("stpSyncTime");
		Date time = stpSyncTime;
		time.setDate(1);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(time);
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("f", 0);//峰总和
		map.put("p", 0);//平总和
		map.put("g", 0);//谷总和
		map.put("j", 0);//尖总和
		float fSum = 0;
		float pSum = 0;
		float gSum = 0;
		float jSum = 0;
		for(int i = 0; i < 31; i++){
			String da1 = sdf.format(calendar.getTime());
			//峰时间1
			String fStart1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFStarttime1();
			String fEnd1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFEndtime1();
			//峰时间2
			String fStart2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFStarttime2();
			String fEnd2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherFEndtime2();
			
			//平时间1
			String pStart1 = da1.substring(0, 10) + " " + DcsOtherInfo.getDcsOtherPStarttime1();
			String pEnd1 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherPEndtime1();
			//平时间2
			String pStart2 = da1.substring(0, 10) + " " + DcsOtherInfo.getDcsOtherPStarttime2();
			String pEnd2 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherPEndtime2();
			
			//谷时间1
			String gStart1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherGStarttime1();
			String gEnd1 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherGEndtime1();
			//谷时间2
			String gStart2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherGStarttime2();
			String gEnd2 = da1.substring(0, 10) + " " +DcsOtherInfo.getDcsOtherGEndtime2();
			
			//尖时间1
			String jStart1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJStarttime1();
			String jEnd1 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJEndtime1();
			//尖时间2
			String jStart2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJStarttime2();
			String jEnd2 = da1.substring(0, 10) + " "+ DcsOtherInfo.getDcsOtherJEndtime2();
			for(StpDay stpDay : stpDays){
				String bjTime = sdf.format(stpDay.getStpSyncTime());
				bjTime = bjTime.substring(0, 16);
				//峰计算
				if(DcsOtherInfo.getDcsOtherFStarttime1() != null && DcsOtherInfo.getDcsOtherFEndtime1()!= null ){
					if(bjTime.compareTo(fStart1) >=0&&bjTime.compareTo(fEnd1)<=0){
						fSum +=stpDay.getStpElecValue();
					}
				}
				if(DcsOtherInfo.getDcsOtherFStarttime2() != null && DcsOtherInfo.getDcsOtherFEndtime2()!= null ){
					if(bjTime.compareTo(fStart2) >=0&&bjTime.compareTo(fEnd2)<=0){
						fSum +=stpDay.getStpElecValue();
					}
				}
				
				//平计算
				if( DcsOtherInfo.getDcsOtherPStarttime1() != null && DcsOtherInfo.getDcsOtherPEndtime1()!= null ){
					if(bjTime.compareTo(pStart1) >=0&&bjTime.compareTo(pEnd1)<=0){
						pSum +=stpDay.getStpElecValue();
					}
				}
				if( DcsOtherInfo.getDcsOtherPStarttime2() != null && DcsOtherInfo.getDcsOtherPEndtime2()!= null ){
					if(bjTime.compareTo(pStart2) >=0&&bjTime.compareTo(pEnd2)<=0){
						pSum +=stpDay.getStpElecValue();
					}
				}
				
				//谷计算
				if( DcsOtherInfo.getDcsOtherGStarttime1() != null && DcsOtherInfo.getDcsOtherGEndtime1()!= null ){
					if(bjTime.compareTo(gStart1) >=0&&bjTime.compareTo(gEnd1)<=0){
						gSum +=stpDay.getStpElecValue();
					}
				}
				if( DcsOtherInfo.getDcsOtherGStarttime2() != null && DcsOtherInfo.getDcsOtherGEndtime2()!= null ){
					if(bjTime.compareTo(gStart2) >=0&&bjTime.compareTo(gEnd2)<=0){
						gSum +=stpDay.getStpElecValue();
					}
				}
				
				//尖计算
				if( DcsOtherInfo.getDcsOtherJStarttime1() != null && DcsOtherInfo.getDcsOtherJEndtime1()!= null ){
					if(bjTime.compareTo(jStart1) >=0&&bjTime.compareTo(jEnd1)<=0){
						jSum +=stpDay.getStpElecValue();
					}
				}
				if( DcsOtherInfo.getDcsOtherJStarttime2() != null && DcsOtherInfo.getDcsOtherJEndtime2()!= null ){
					if(bjTime.compareTo(jStart2) >=0&&bjTime.compareTo(jEnd2)<=0){
						jSum +=stpDay.getStpElecValue();
					}
				}
			}
			calendar.add(Calendar.DAY_OF_MONTH, 1);//每隔一天
		}
		if((fSum+pSum+gSum+jSum) != 0){
			map.put("f", (float)(Math.round((fSum/(fSum+pSum+gSum+jSum))*100)));
			map.put("p", (float)(Math.round((pSum/(fSum+pSum+gSum+jSum))*100)));
			map.put("g", (float)(Math.round((gSum/(fSum+pSum+gSum+jSum))*100)));
			map.put("j", (float)(Math.round((jSum/(fSum+pSum+gSum+jSum))*100)));
		}
		
		return map;
	}
	
	/**
	 * 
	* <AUTHOR> 
	* @Description  日报表行转列
	* @date 2018年7月3日下午4:43:15  
	 */
	public Map<String , Object> getStpDayColList(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<StpDayCol> list =stpDayDao.selectForDayToCol(params);
		resultMap.put("rows",list);
		params.remove("offset");
		params.remove("limit");
		List<StpDayCol> list1 =stpDayDao.selectForDayToCol(params);
		resultMap.put("total",list1.size());
		return resultMap;
	}
	
	
	public List<Map<String , Object>> getElectricityReports(Map<String, Object> params) throws Exception{
		Map<String, Object> timeMap = getWeekDate(params.get("serachTime").toString());
		params.putAll(timeMap);
		List<Map<String , Object>> additivePowerList= dcsStationDao.getAdditivePower(params);	//电站累计发电量数据
		List<Map<String , Object>> powerList= new ArrayList<Map<String , Object>>();			//电站按照统计条件查询的数据
		List<Map<String , Object>> difList= new ArrayList<Map<String , Object>>();
		String searchType = params.get("searchType").toString();
		if("1".equals(searchType)) {//日报表
			powerList = stpMonthDao.getElectricityReports(params);
			difList = stpMonthDao.getElectricityReportsDif(params);
		}else if ("2".equals(searchType)) {//周报表
			powerList = stpMonthDao.getElectricityReports1(params);
			difList = stpMonthDao.getElectricityReports1Dif(params);
		}else if ("3".equals(searchType)) {//月报表
			powerList = stpYearDao.getElectricityReports(params);
			difList = stpYearDao.getElectricityReportsDif(params);
		}else {//年报表
			powerList = stpYearDao.getElectricityReports1(params);
			difList = stpYearDao.getElectricityReports1Dif(params);
		}
		if(additivePowerList.isEmpty()&&powerList.isEmpty()) {
			return null;
		}
		if(!additivePowerList.isEmpty()) {
			for (Map<String, Object> map : additivePowerList) {
				for (Map<String, Object> map2 : difList) {
					if(map.get("dcsId").toString().equals(map2.get("dcsId").toString())) {
						String elecValue2004 = map.containsKey("20004")?map.get("20004").toString():"0";				//取最新的累计发电量
						String elecValueDif =  map2.containsKey("elecValue")?map2.get("elecValue").toString():"0";		//取经过计算的筛选时期截止到最新时间的累计发电量
						map.put("20004", new BigDecimal(elecValue2004).subtract(new BigDecimal(elecValueDif)));			//计算出截止到筛选时期的累计发电量
					}
				}
			}
			for (Map<String, Object> map : additivePowerList) {
				for (Map<String, Object> map2 : powerList) {
					if(map.get("dcsId").toString().equals(map2.get("dcsId").toString())) {
						map.put("totalValue", map2.containsKey("totalValue")?map2.get("totalValue"):0);
					}
				}
			}
		}
		return additivePowerList;
	}
	
	/**
	 * 获取时间筛选条件
	 * @param serachTime
	 * @return
	 * @throws Exception
	 */
	public static Map<String,Object> getWeekDate(String serachTime) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		if(serachTime.length() == 7) {
			serachTime += "-01";
		}
		if(serachTime.length() == 4) {
			serachTime += "-01-01";
		}
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(serachTime);
		Calendar cal = Calendar.getInstance(); 
		cal.setTime(date);
		cal.setFirstDayOfWeek(Calendar.MONDAY);// 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一  
		int dayWeek = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天  
		if(dayWeek==1){
		    dayWeek = 8;
		}
//		System.out.println("要计算日期为:" + sdf.format(cal.getTime())); // 输出要计算日期  
		cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - dayWeek);// 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值  
		Date mondayDate = cal.getTime();
		String weekBegin = sdf.format(mondayDate);  
//		System.out.println("所在周星期一的日期：" + weekBegin);  
        cal.add(Calendar.DATE, 4 +cal.getFirstDayOfWeek());
        Date sundayDate = cal.getTime();
        String weekEnd = sdf.format(sundayDate);  
//        System.out.println("所在周星期日的日期：" + weekEnd);
        map.put("mondayDate", weekBegin + " 00:00:00");
		map.put("sundayDate", weekEnd + " 23:59:59");
		
		Calendar cal3 = Calendar.getInstance();
		cal3.setTime(sundayDate);
		cal3.set(Calendar.DAY_OF_MONTH, cal3.getActualMaximum(Calendar.DAY_OF_MONTH));
		Date time1 = cal3.getTime();
        String monthLastDay1 = sdf.format(time1);
//        System.out.println("周末所在月最后一天：" + monthLastDay1);
        map.put("monthLastDay1", monthLastDay1 + " 23:59:59");
		
        Calendar calendar4 = Calendar.getInstance();
        calendar4.setTime(sundayDate);
        calendar4.add(Calendar.DATE, 1);
        Date next1 = calendar4.getTime();
        String nextDay1 = sdf.format(next1);
//        System.out.println("周末的下一天：" + nextDay1);
        map.put("nextDay1", nextDay1 + " 00:00:00");
        
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date);
		cal1.set(Calendar.DAY_OF_MONTH, cal1.getActualMaximum(Calendar.DAY_OF_MONTH));
		Date time = cal1.getTime();
        String monthLastDay = sdf.format(time);
//        System.out.println("本月最后一天：" + monthLastDay);
        map.put("monthLastDay", monthLastDay + " 23:59:59");
        
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        calendar2.set(Calendar.MONTH,calendar2.getActualMaximum(Calendar.MONTH));
        calendar2.set(Calendar.DAY_OF_MONTH,calendar2.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date currYearLast = calendar2.getTime();
        String yearLastDay = sdf.format(currYearLast);
//        System.out.println("本年最后一天：" + yearLastDay);
        map.put("yearLastDay", yearLastDay + " 23:59:59");
        
        Calendar calendar3 = Calendar.getInstance();
        calendar3.setTime(date);
        calendar3.add(Calendar.DATE, 1);
        Date next = calendar3.getTime();
        String nextDay = sdf.format(next);
//        System.out.println("下一天：" + nextDay);
        map.put("nextDay", nextDay + " 00:00:00");
        
        return map;
    }
	public static void main(String[] args) throws Exception {
		getWeekDate("2019-10-26");
	}
	
	/**
	 * 查询发电电价
	 * @param params
	 * @return
	 */
	public List<StpDay> selectForGenerationPrice(Map<String, Object> params){
		return stpDayDao.selectForGenerationPrice(params);
	}

	/**
	 * 查询发电统计
	 * @param params
	 * @return
	 */
	public List<StpDay> selectForElecStatistics(Map<String, Object> params){
		return stpDayDao.selectForElecStatistics(params);
	}

	/**
	 * 查询发电统计2(发电效率用)
	 * @param params
	 * @return
	 */
	public List<StpDay> selectForFdEfficiency(Map<String, Object> params){
		return stpDayDao.selectForFdEfficiency(params);
	}
	
	/**
	 * 查询站点发电量总数
	 * @param params
	 * @return
	 */
	public StpDay selGFDZ(Map<String, Object> params){
		return stpDayDao.selGFDZ(params);
	}
	
	/**
	 * 查询日月年站点的统计数据
	 * @param params
	 * @return
	 */
	public List<StpDay> selDayMonthYearForStation(Map<String, Object> params){
		return stpDayDao.selDayMonthYearForStation(params);
	}

	/**
	 * 查询发电电价
	 * @param params
	 * @return
	 */
	public List<StpDay> selectForGenerationPriceForStationId(Map<String, Object> params){
		return stpDayDao.selectForGenerationPriceForStationId(params);
	}

	/**
	 * 根据设备id查询发电统计
	 * @param params
	 * @return
	 */
	public List<StpDay> selectForEquId(Map<String, Object> params){
		return stpDayDao.selectForEquId(params);
	}

	/**
	 * 查询发电统计2(发电效率用)
	 * @param params
	 * @return
	 */
	public List<StpDay> selectForFdEfficiencyForStationId(Map<String, Object> params){
		return stpDayDao.selectForFdEfficiencyForStationId(params);
	}

	/**
	 * 区域统计
	 * @param params
	 * @return
	 */
	public Map<String,Object> selectQYTJList(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows",stpDayDao.selectForElecStatisticsForStation(params));
		resultMap.put("total",stpDayDao.selectForElecStatisticsForStationCount(params));
		return resultMap;
	}

	/**
	 * 区域统计(月-区间)
	 * @param params
	 * @return
	 */
	public Map<String,Object> selectQYTJList_m(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows",stpDayDao.selectForElecStatisticsForStation_m(params));
		resultMap.put("total",stpDayDao.selectForElecStatisticsForStationCount_m(params));
		return resultMap;
	}

	public List<StpStationData> selectForElecStatisticsForStation(Map<String, Object> params){
		return stpDayDao.selectForElecStatisticsForStation(params);
	}

	/**
	 * (月-区间)
	 * @param params
	 * @return
	 */
	public List<StpStationData> selectForElecStatisticsForStation_m(Map<String, Object> params){
		return stpDayDao.selectForElecStatisticsForStation_m(params);
	}

	/**
	 * 查询发电统计(表格)
	 * @param params
	 * @return
	 */
	public Map<String, Object> selectForElecStatisticsForTabList(Map<String, Object> params){
		Map<String, Object> result = new HashMap<>();
		result.put("rows",stpDayDao.selectForElecStatisticsForTab(params));
		result.put("total",stpDayDao.selectForElecStatisticsForTabCount(params));
		return result;
	}
	public List<StpStationData> selectForElecStatisticsForExc(Map<String, Object> params){
		return stpDayDao.selectForElecStatisticsForExc(params);
	}

	/**
	 * 查询收益统计(表格)
	 * @param params
	 * @return
	 */
	public Map<String, Object> selectForGenerationPriceToTab(Map<String, Object> params){
		Map<String, Object> result = new HashMap<>();
		result.put("rows",stpDayDao.selectForGenerationPriceToTab(params));
		result.put("total",stpDayDao.selectForGenerationPriceToTabCount(params));
		return result;
	}

	public List<GenerationPrice> selectForGenerationPriceToTabExc(Map<String, Object> params){
		return stpDayDao.selectForGenerationPriceToTab(params);
	}

	public List<StpDay> selForPower(Map<String, Object> params){
		return stpDayDao.selForPower(params);
	}

	public List<StpDay> selBarEchData(Map<String, Object> params){
		return stpDayDao.selBarEchData(params);
	}

	public List<StpDay> selUserScreenEchData(Map<String, Object> params){
		return stpDayDao.selUserScreenEchData(params);
	}

	public String getUpdateTime(Map<String, Object> params) {
		return stpDayDao.getUpdateTime(params);
	}

	public List<StoDataRecord> getDayDetails(Map<String, Object> params) {
		return stpDayDao.getDayDetails(params);
	}

	public List<Map<String, Object>> getInverterId(String stationId) {
		return stpDayDao.getInverterId(stationId);
	}

	public List<Map<String, String>> getInverterName(Map<String, Object> params) {
		return stpDayDao.getInverterName(params);
	}
}
