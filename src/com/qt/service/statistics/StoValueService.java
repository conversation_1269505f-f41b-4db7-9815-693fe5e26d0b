package com.qt.service.statistics;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.statistics.StoValue;
import com.qt.repository.statistics.StoValueDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-09 05:54:04
 */
@Service
public class StoValueService{

	private Logger logger = LoggerFactory.getLogger(StoValueService.class);
     
    @Resource
	private StoValueDao stoValueDao;
	
	//根据条件搜索记录
	public Map<String , Object> getStoValueList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", stoValueDao.selectList(queryMap));
		resultMap.put("total", stoValueDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addStoValue(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			StoValue temp=MapUtil.toObject(StoValue.class, params);
			stoValueDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateStoValue(StoValue stoValue){
		stoValueDao.update(stoValue);
	}
	
	//删除记录
	public int delStoValue(Map<String, Object> params){
		return stoValueDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delStoValueBat(String[] idArray){
		for(String pk:idArray){
			stoValueDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<StoValue> getAllStoValue(Map<String, Object> params){
		return stoValueDao.selectList(params);
	}
	
	/**
	 * 标杆配置列表
	 * @param params
	 * @return
	 */
	public List<StoValue> selForBenchmarkList(Map<String,Object> params){
		return stoValueDao.selForBenchmarkList(params);
	}

	public Map<String,Object> selForBenchmarkData(Map<String,Object> params){
		Map<String,Object> result = new HashMap<>();
		result.put("rows",stoValueDao.selForBenchmarkList(params));
		result.put("total", stoValueDao.selForBenchmarkListCount(params));
		return  result;
	}

	public void insertBenchmark(StoValue params){
		try {
			//TODO 根据需要封装查询需要的条件
			stoValueDao.insertBenchmark(params);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
		
	public void updateBenchmark(StoValue stoValue){
		stoValueDao.updateBenchmark(stoValue);
	}
	
	public StoValue selBenchmarkOne(Map<String,Object> params){
		return stoValueDao.selBenchmarkOne(params);
	}
	
	
}
