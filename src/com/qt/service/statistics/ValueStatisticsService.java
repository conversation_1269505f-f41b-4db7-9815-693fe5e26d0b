package com.qt.service.statistics;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.statistics.ValueStatistics;
import com.qt.repository.statistics.ValueStatisticsDao;

@Service
public class ValueStatisticsService {
	
	private Logger logger = LoggerFactory.getLogger(ValueStatisticsService.class);
    
    @Resource
	private ValueStatisticsDao valueStatisticsDao;
    
    //根据条件搜索记录
	public List<ValueStatistics> list(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return valueStatisticsDao.selectList(params);
	}
	
	//新增记录
	public void add(Map<String, Object> params){
		try {
			valueStatisticsDao.insert(params);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}

}
