package com.qt.service.statistics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.entity.file.DclLine;
import com.qt.entity.statistics.StlDay;
import com.qt.entity.statistics.StlYear;
import com.qt.repository.statistics.StlYearDao;
import com.qt.service.file.DclLineService;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-16 10:07:33
 */
@Service
public class StlYearService{

	private Logger logger = LoggerFactory.getLogger(StlYearService.class);
     
    @Resource
	private StlYearDao stlYearDao;
    
    @Autowired
	private DclLineService dclLineService;
	
  /* 负荷分析日数据表格数据 */
	public Map<String , Object> getStlYearListYear(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", stlYearDao.selectList(params));
		resultMap.put("total", stlYearDao.selectCount(queryMap));
		return resultMap;
	}
    
    
    
	//根据条件搜索记录
	public Map<String , Object> getStlYearList(Map<String, Object> params){
		Map<String, Object> queryMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StlYear> list = new ArrayList<StlYear>();
		List<StlYear> list1 = new ArrayList<StlYear>();
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				list =stlYearDao.selectList(queryMap);
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				list =stlYearDao.selectList(queryMap);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			//System.out.println("----------allDclLine------------"+allDclLine.size());
			list =stlYearDao.selectList(queryMap);
			for (int i = 0; i < list.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list.get(i).getStlLineId())){
						list1.add(list.get(i));
					}
				}
			}
			list = list1;
		}
		
		resultMap.put("rows",list);
		resultMap.put("total",list.size());
		return resultMap;
	}
	
	
	
	//根据条件搜索记录--导出
	public List<StlYear> getStlYearList2(Map<String, Object> params){
		Map<String, Object> queryMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StlYear> list = new ArrayList<StlYear>();
		List<StlYear> list1 = new ArrayList<StlYear>();
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				list =stlYearDao.selectList(queryMap);
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				list =stlYearDao.selectList(queryMap);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			//System.out.println("----------allDclLine------------"+allDclLine.size());
			list =stlYearDao.selectList(queryMap);
			for (int i = 0; i < list.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list.get(i).getStlLineId())){
						list1.add(list.get(i));
					}
				}
			}
			list = list1;
		}
		return list;
	}
	
	
	
	
	public List<StlYear> getAllStlYear(Map<String, Object> params){
		//return stlYearDao.selectList(params);
		Map<String, Object> queryMap = new HashMap<String, Object>();
		List<StlYear> retList = new ArrayList<StlYear>();
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		String x =(String) queryMap.get("stlStationId");
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				for(DclLine dclLine:lineList){
					Integer s =	dclLine.getDclDcsId();
					String ss = s.toString();
					if(ss.equals(x)){
						queryMap.put("stlLineId", dclLine.getDclId());
						queryMap.put("stlStationId", dclLine.getDclDcsId());
						List<StlYear> list1 =stlYearDao.selectList(queryMap);
						if(list1.size() !=0){
							retList.addAll(list1);
						}	
					}
				}
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				List<StlYear> list2 =stlYearDao.selectList(queryMap);
				retList.addAll(list2);
			}
		}else{
			//查询变压器下面的回路
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);			
			for(DclLine d:allDclLine){
				queryMap.put("stlStationId", d.getDclDcsId());
				queryMap.put("stlLineId", d.getDclId());
				List<StlYear> list3 =stlYearDao.selectList(queryMap);
				retList.addAll(list3);
			}
		}
		
		return retList;
	}
	
}
