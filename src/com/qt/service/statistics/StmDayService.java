package com.qt.service.statistics;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.DateUtils;
import com.qt.entity.statistics.StmDay;
import com.qt.repository.statistics.StmDayDao;


@Service
public class StmDayService {
	private Logger logger = LoggerFactory.getLogger(StoOriginalService.class);
	@Resource
	private StmDayDao stmDayDao;
	
	public void setStmDayDao(StmDayDao stmDayDao) {
		this.stmDayDao = stmDayDao;
	}

	//最值分析月数据
	public Map<String, Object> getYUEData(Map<String, Object> params) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<StmDay> stmDays = stmDayDao.selectRealYueData(params);// 查询一天中所有的数据
		Date stoOrignalTime = (Date) params.get("stmMaxTime");
		stoOrignalTime.setDate(1);
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String, Object>>();// 有序(进去什么顺序出来就是什么顺序)不重复
		Date time = stoOrignalTime;
		for(int i = 0; i < 31; i++){
			//获取曲线数据
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("max", "kongzhi");
			map.put("min", "kongzhi");
			map.put("avg", "kongzhi");
			map.put("t", DateUtils.formatDate(time, "dd"));
			//求最大值 最小值 
			for (StmDay stmDay : stmDays) {
				
				SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
				Date da1 = null;
				try {
					da1 = (Date) sdf.parse(time+"");
				} catch (ParseException e) {
					e.printStackTrace();
				}
				Date da2 = null;
				try {
					da2 = (Date) sdf.parse(new Date(time.getTime()+24*60*60*1000)+"");
				} catch (ParseException e) {
					e.printStackTrace();
				}
				Date da = null;
				try {
					da = (Date) sdf.parse(stmDay.getStmMaxTime()+"");
				} catch (ParseException e) {
					e.printStackTrace();
				}
				sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				
				if(sdf.format(da).compareTo(sdf.format(da1)) >=0&&sdf.format(da).compareTo(sdf.format(da2))<=0){
					map.put("max", stmDay.getStmMaxValue());
					map.put("min", stmDay.getStmMinValue());
					map.put("avg", stmDay.getStmAverValue());
					}
			}
			resultLine.add(map);
			time = new Date(time.getTime()+24*60*60*1000);//每隔一天
			}
		result.put("tableData", resultLine);		
		return result;
	}

	//最值分析年数据
	public Map<String, Object> getYearData(Map<String, Object> params) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<StmDay> stmDays = stmDayDao.selectRealYearData(params);// 查询一天中所有的数据
		Date stoOrignalTime1 = (Date) params.get("stmMaxTime");
		stoOrignalTime1.setMonth(0);
		stoOrignalTime1.setDate(1);
		Date time1 = stoOrignalTime1;
		
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String, Object>>();// 有序(进去什么顺序出来就是什么顺序)不重复
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(time1);
		
		for(int i = 0; i < 12; i++){
				//获取曲线数据
				Map<String,Object> map = new HashMap<String, Object>();
				map.put("max", "kongzhi");
				map.put("min", "kongzhi");
				map.put("avg", "kongzhi");
				//求最大值 最小值 
				String da1 = sdf.format(calendar.getTime());
				map.put("t", da1.substring(5, 7));
		        calendar.add(Calendar.MONTH, 1);
		        String da2 = sdf.format(calendar.getTime());
		        float h = 0;
		        float sum = 0;
				for (StmDay stmDay : stmDays) {
					String da = sdf.format(stmDay.getStmMaxTime());
						if(da.compareTo(da1) >=0&&da.compareTo(da2)<=0){
							if(map.get("max").equals("kongzhi")){
								map.put("max", stmDay.getStmMaxValue());
							}else{
								Float x = ((Float) map.get("max"));
								if(stmDay.getStmMaxValue()>x){
									map.put("max", stmDay.getStmMaxValue());
								}
							}
							
							if(map.get("min").equals("kongzhi")){
								map.put("min", stmDay.getStmMinValue());
							}else{
								Float y = ((Float) map.get("min"));
								if(stmDay.getStmMinValue()<y){
									map.put("min", stmDay.getStmMinValue());
								}
							}
							sum += stmDay.getStmAverValue();
							h++;
							
						}
				}
				if(sum !=0){
					map.put("avg",(float)(Math.round(sum/h*100))/100 );
				}
				resultLine.add(map);
		}
		result.put("tableData", resultLine);
		return result;
	}
	
	
}
