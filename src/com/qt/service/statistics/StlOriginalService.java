package com.qt.service.statistics;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.bean.statistics.StlValue;
import com.qt.common.utils.DateUtils;
import com.qt.entity.file.DclLine;
import com.qt.entity.statistics.FuHeDay;
import com.qt.entity.statistics.FuHeDayDetail;
import com.qt.entity.statistics.FuHeMonth;
import com.qt.entity.statistics.FuHeMonthDetail;
import com.qt.entity.statistics.StlOriginal;
import com.qt.entity.statistics.StoDataRecord;
import com.qt.repository.file.DclLineDao;
import com.qt.repository.statistics.StlOriginalDao;
import com.qt.service.file.DclLineService;

@Service
public class StlOriginalService {
	private Logger logger = LoggerFactory.getLogger(StlOriginalService.class);
	@Resource
	private StlOriginalDao stlOriginalDao;
	@Autowired
	private DclLineService dclLineService;
	@Resource
	private DclLineDao dclLineDao;
	/**
	 * 根据站点查询一天的所有数据(负荷)
	 * @param params
	 * @return
	 */
	public List<Map<String,Object>> selectStationValue(Map<String,Object> params){
		List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
		List<StlOriginal> stlOriginals = stlOriginalDao.selectStationValue(params);
		return result;
	}
	/**
	 * 查询某一站点一天有功功率的总值
	 * @param params
	 * @return
	 */
	public Set<Map<String, Object>> getStationYGGLValue(Map<String,Object> params){
		List<StlOriginal> stlOriginals = stlOriginalDao.selectStationYGGLValueDay(params);//查询一天中所有的数据
		Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
		Date time = stoOrignalTime; 
		for(int i = 0; i < 24; i++){
			//获取曲线数据
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			map.put("t", DateUtils.formatDate(time, "HH"));
			for(StlOriginal stlOriginal : stlOriginals){
				if(stlOriginal.getStlSyncTime().equals(time)){
					map.put("d", stlOriginal.getStlOrignalValue());
				}
			}
			resultLine.add(map);
			time = new Date(time.getTime()+60*60*1000);//每隔1小时
		}
		return resultLine;
	}
	
	
	/**
	 * 日负荷分析表格
	 * @param params
	 * @return
	 */
	public List<StlValue> selectListFX (Map<String, Object> params){
		List<StlValue> result = new ArrayList<StlValue>();
		StlValue stlValue = null;
		List<DclLine> dclLineList = dclLineDao.selectListDB(params);
		for (DclLine dclLine : dclLineList) {
			stlValue = new StlValue();
			params.put("stlLineId", dclLine.getDclId());
			Map<String,Object> resultMap = stlOriginalDao.selectListFX(params);
			stlValue.setStationId(dclLine.getDclDcsId());
			stlValue.setLineId(dclLine.getDclId());
			stlValue.setDcsName(dclLine.getDcsName());
			stlValue.setDclName(dclLine.getDclName());
			String value1 = "";
			try {
				value1 = resultMap.get("value1").toString();
				stlValue.setValue1(value1);
			} catch (Exception e) {
				stlValue.setValue1(value1);
			}
			String value2 = "";
			try {
				value1 = resultMap.get("value2").toString();
				stlValue.setValue2(value2);
			} catch (Exception e) {
				stlValue.setValue2(value2);
			}
			String value3 = "";
			try {
				value3 = resultMap.get("value3").toString();
				stlValue.setValue3(value3);
			} catch (Exception e) {
				stlValue.setValue3(value3);
			}
			String maxTime = "-";
			try {
				maxTime = resultMap.get("maxTime").toString();
				maxTime = maxTime.substring(0, maxTime.length()-2);
				stlValue.setMaxTime(maxTime);
			} catch (Exception e) {
				stlValue.setMaxTime(maxTime);
			}
			String minTime = "-";
			try {
				minTime = resultMap.get("minTime").toString();
				minTime = minTime.substring(0, minTime.length()-2);
				stlValue.setMinTime(minTime);
			} catch (Exception e) {
				stlValue.setMinTime(minTime);
			}
			result.add(stlValue);
		}
		return result;
	}
	
	//负荷分析日数据 回路
	public Set<Map<String, Object>> getStationYGGLValueDay(Map<String,Object> params){
		List<StlOriginal> stlOriginals = stlOriginalDao.selectStationYGGLValueDay(params);//查询一天中所有的数据
		Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
		Date time = stoOrignalTime; 
		for(int i = 0; i < 24; i++){
			//获取曲线数据
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", "kongzhi");
			map.put("t", DateUtils.formatDate(time, "HH"));
			for(StlOriginal stlOriginal : stlOriginals){
				if(stlOriginal.getStlSyncTime().equals(time)){
					map.put("d", stlOriginal.getStlOrignalValue());
				}
			}
			resultLine.add(map);
			time = new Date(time.getTime()+60*60*1000);//每隔1小时
		}
		return resultLine;
	}
	//负荷分析日数据 站点
	public Map<String, Object> selectStationLValueDaySta(Map<String,Object> params){
		List<FuHeDay> fuHeDay = stlOriginalDao.selectStationYGGLValueDaySta(params);//查询一天中所有的数据
		Map<String,Object> jieguo = new HashMap<String, Object>();
		List<Map<String, Object>> mapli = new ArrayList<Map<String, Object>>();
		String byqid = (String) params.get("byqid");
		if(null != byqid){
			//查询变压器下面的回路
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("byqid"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			for (int y = 0; y < fuHeDay.size(); y++) {
				Map<String, Object> gggg = new HashMap<String, Object>();
				int sfs = 0;
				for (DclLine dclLine : allDclLine) {
					if(dclLine.getDclId().toString().equals(fuHeDay.get(y).getStl_line_id()) ){
						sfs = 1;
					}
				}
				if(sfs == 1){
					List<FuHeDayDetail> list = fuHeDay.get(y).getDetailList();
					Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
					Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
					Date time = stoOrignalTime; 
					for(int i = 0; i < 24; i++){
						//获取曲线数据
						Map<String,Object> map = new HashMap<String, Object>();
						map.put("d", "kongzhi");
						map.put("t", DateUtils.formatDate(time, "HH"));
						for(FuHeDayDetail stlOriginal : list){
							map.put("name", stlOriginal.getDcl_name());
							if(stlOriginal.getSlt_sync_time().equals(time)){
								map.put("d", stlOriginal.getStl_orignal_value());
							}
						}
						resultLine.add(map);
						time = new Date(time.getTime()+60*60*1000);//每隔1小时
					}
					gggg.put("data", resultLine);
					mapli.add(gggg);
				}
			}
		}else{
			for (int y = 0; y < fuHeDay.size(); y++) {
				Map<String, Object> gggg = new HashMap<String, Object>();
				List<FuHeDayDetail> list = fuHeDay.get(y).getDetailList();
				Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
				Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
				Date time = stoOrignalTime; 
				for(int i = 0; i < 24; i++){
					//获取曲线数据
					Map<String,Object> map = new HashMap<String, Object>();
					map.put("d", "kongzhi");
					map.put("t", DateUtils.formatDate(time, "HH"));
					for(FuHeDayDetail stlOriginal : list){
						map.put("name", stlOriginal.getDcl_name());
						if(stlOriginal.getSlt_sync_time().equals(time)){
							map.put("d", stlOriginal.getStl_orignal_value());
						}
					}
					resultLine.add(map);
					time = new Date(time.getTime()+60*60*1000);//每隔1小时
				}
				gggg.put("data", resultLine);
				mapli.add(gggg);
			}
		}
		
		jieguo.put("jieguo", mapli);
		return jieguo;
	}
	//负荷分析月数据回路
	public Set<Map<String, Object>> getStationYGGLValueMonthLine(Map<String,Object> params){
		List<StlOriginal> stlOriginals = stlOriginalDao.selectStationYGGLValueMonthLine(params);//查询一天中所有的数据
		Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
		Date time = stoOrignalTime; 
		for(int i = 0; i < 31; i++){
			//获取曲线数据
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			map.put("t", DateUtils.formatDate(time, "dd"));
			for(StlOriginal stlOriginal : stlOriginals){
				if(stlOriginal.getStlSyncTime().equals(time)){
					map.put("d", stlOriginal.getStlOrignalValue());
				}
			}
			resultLine.add(map);
			time = new Date(time.getTime()+24*60*60*1000);//每隔一天
		}
		return resultLine;
	}
	//负荷分析月数据站点
		public Map<String, Object> getStationYGGLValueMonthStation(Map<String,Object> params){
			List<FuHeMonth> fuHeMonth = stlOriginalDao.selectStationYGGLValueMonthStation(params);//查询一天中所有的数据
			Map<String,Object> jieguo = new HashMap<String, Object>();
			List<Map<String, Object>> mapli = new ArrayList<Map<String, Object>>();
			String byqid = (String) params.get("byqid");
			if(null != byqid){
				//查询变压器下面的回路
				Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
				dclLineParams.put("dclDcsId", params.get("stlStationId"));
				dclLineParams.put("dclDcbId", params.get("byqid"));
				List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
				for (int y = 0; y < fuHeMonth.size(); y++) {
					Map<String, Object> gggg = new HashMap<String, Object>();
					int sfs = 0;
					for (DclLine dclLine : allDclLine) {
						if(dclLine.getDclId().toString().equals(fuHeMonth.get(y).getStl_line_id()) ){
							sfs = 1;
						}
					}
					if(sfs == 1){
						List<FuHeMonthDetail> list = fuHeMonth.get(y).getDetailList();
						Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
						Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
						Date time = stoOrignalTime; 
						for(int i = 0; i < 31; i++){
							//获取曲线数据
							Map<String,Object> map = new HashMap<String, Object>();
							map.put("d", "kongzhi");
							map.put("t", DateUtils.formatDate(time, "dd"));
							for(FuHeMonthDetail stlOriginal : list){
								map.put("name", stlOriginal.getDcl_name());
								if(stlOriginal.getSlt_sync_time().equals(time)){
									map.put("d", stlOriginal.getStl_orignal_value());
								}
							}
							resultLine.add(map);
							time = new Date(time.getTime()+24*60*60*1000);//每隔一天
						}
						gggg.put("data", resultLine);
						mapli.add(gggg);
					}
				}
			}else{
				for (int y = 0; y < fuHeMonth.size(); y++) {
					Map<String, Object> gggg = new HashMap<String, Object>();
					List<FuHeMonthDetail> list = fuHeMonth.get(y).getDetailList();
					Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
					Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
					Date time = stoOrignalTime; 
					for(int i = 0; i < 31; i++){
						//获取曲线数据
						Map<String,Object> map = new HashMap<String, Object>();
						map.put("d", "kongzhi");
						map.put("t", DateUtils.formatDate(time, "dd"));
						for(FuHeMonthDetail stlOriginal : list){
							map.put("name", stlOriginal.getDcl_name());
							if(stlOriginal.getSlt_sync_time().equals(time)){
								map.put("d", stlOriginal.getStl_orignal_value());
							}
						}
						resultLine.add(map);
						time = new Date(time.getTime()+24*60*60*1000);//每隔一天
					}
					gggg.put("data", resultLine);
					mapli.add(gggg);
				}
			}
			
			jieguo.put("jieguo", mapli);
			return jieguo;
		}
	/**
	 * 查询某一站点一月有功功率的总值
	 * @param params
	 * @return
	 */
	public Set<Map<String, Object>> getStationYGGLValueMonth(Map<String,Object> params){
		List<StlOriginal> stlOriginals = stlOriginalDao.selectStationYGGLValueMonth(params);//查询一天中所有的数据
		Date stoOrignalTime = DateUtils.parseDate(params.get("time"));
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
		Date time = stoOrignalTime; 
		for(int i = 0; i < 31; i++){
			//获取曲线数据
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			map.put("t", DateUtils.formatDate(time, "dd"));
			for(StlOriginal stlOriginal : stlOriginals){
				if(stlOriginal.getStlSyncTime().equals(time)){
					map.put("d", stlOriginal.getStlOrignalValue());
				}
			}
			resultLine.add(map);
			time = new Date(time.getTime()+24*60*60*1000);//每隔一天
		}
		return resultLine;
	}
	/*实时数据总有功功率曲线0922*/
	public Map<String, Object> getXianRealData(Map<String, Object> params) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<StlOriginal> stlOriginals = stlOriginalDao.selectRealTimeData(params);// 查询一天中所有的数据
		Date stoOrignalTime = (Date)( params.get("stlOrignalTime"));
			
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String, Object>>();// 有序(进去什么顺序出来就是什么顺序)不重复
		Date time = stoOrignalTime;
		float maxValue = 0;
		String maxTime = DateUtils.formatDate(time, "MM-dd HH:mm");// 初始化时间
		float minValue = 0;
		String minTime = DateUtils.formatDate(time, "MM-dd HH:mm");
		float avgValue = 0;
		float sum = 0;
		for (int i = 0; i < 96; i++) {// 一天中有96个15分钟
			// 获取曲线数据
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			map.put("t", DateUtils.formatDate(time, "HH:mm"));
			for (StlOriginal stlOriginal : stlOriginals) {
				if (stlOriginal.getStlOrignalTime().equals(time)) {
					map.put("d", stlOriginal.getStlOrignalValue());
				} 
				// 获取表格最大值最小值平均值
				if (i == 0) {
					sum += stlOriginal.getStlOrignalValue();
					float tempMax = stlOriginal.getStlOrignalValue();
					float tempMin = stlOriginal.getStlOrignalValue();
					if (tempMax > maxValue) {
						maxValue = tempMax;
						maxTime = DateUtils.formatDate(stlOriginal.getStlOrignalTime(), "MM-dd HH:mm");
					}
					if (tempMin < minValue) {
						minValue = tempMin;
						minTime = DateUtils.formatDate(stlOriginal.getStlOrignalTime(), "MM-dd HH:mm");
					}
				}
			}
			resultLine.add(map);
			time = new Date(time.getTime() + 15 * 60 * 1000);// 每隔15分钟
		}
		avgValue = sum / 96f;
		List<Map<String, Object>> tableList = new ArrayList<Map<String, Object>>();
		Map<String, Object> tableMap = new HashMap<String, Object>();
		tableMap.put("bsaDesc", params.get("bsaDesc"));
		tableMap.put("maxValue", maxValue);
		tableMap.put("maxTime", maxTime);
		tableMap.put("minValue", minValue);
		tableMap.put("minTime", minTime);
		tableMap.put("avgValue", avgValue);
		tableList.add(tableMap);
		result.put("tableData", tableList);
		result.put("lineData", resultLine);
		return result;
	}
	
	//根据条件搜索记录--原始值
	public List<StlOriginal> list(Map<String, Object> params){
		return stlOriginalDao.selectListTJ(params);
	}
}
