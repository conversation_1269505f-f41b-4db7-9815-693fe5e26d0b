package com.qt.service.statistics;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.bean.statistics.UserEfficiensyForm;
import com.qt.bean.statistics.ValueStaticsForm;
import com.qt.common.utils.DateUtils;
import com.qt.entity.file.DccInfo;
import com.qt.entity.file.DclLine;
import com.qt.entity.file.DcsStation;
import com.qt.entity.statistics.StpDay;
import com.qt.entity.statistics.StpMonth;
import com.qt.entity.statistics.StpYear;
import com.qt.entity.statistics.StpYearCol;
import com.qt.repository.file.DccInfoDao;
import com.qt.repository.file.DcsStationDao;
import com.qt.repository.statistics.StpYearDao;
import com.qt.service.file.DclLineService;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-15 11:36:50
 */
@Service
public class StpYearService{

	private Logger logger = LoggerFactory.getLogger(StpYearService.class);
     
    @Resource
	private StpYearDao stpYearDao;
    
    @Resource
	private DccInfoDao dccInfoDao;
    
    @Resource
	private DcsStationDao dcsStationDao;
    
    @Autowired
	private DclLineService dclLineService;
	
	//根据条件搜索记录
	public Map<String , Object> getStpYearList(Map<String, Object> params){
		Map<String, Object> queryMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StpYear> list1 = new ArrayList<StpYear>();
		List<StpYear> list2 = new ArrayList<StpYear>();
		if(params.get("stpByqId") == null || "".equals(params.get("stpByqId"))){
			list1 =stpYearDao.selectList(queryMap);
		}else{
			//查询变压器下面的回路
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			list1 =stpYearDao.selectList(queryMap);
			for (int i = 0; i < list1.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(list1.get(i).getStpLineId())){
						list2.add(list1.get(i));
					}
				}
			}
			list1 = list2;
		}
		resultMap.put("rows",list1);
		resultMap.put("total",list1.size());
		return resultMap;
	}
  
	public List<StpYear> getAllStpYear(Map<String, Object> params){
		//return stpYearDao.selectList(params);
		Map<String, Object> queryMap = new HashMap<String, Object>();
		List<StpYear> retList = new ArrayList<StpYear>();
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StpYear> list1 = new ArrayList<StpYear>();
		if(params.get("stpByqId") == null){
			retList =stpYearDao.selectList(queryMap);
		}else{
			//查询变压器下面的回路
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stpStationId"));
			dclLineParams.put("dclDcbId", params.get("stpByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			retList =stpYearDao.selectList(queryMap);
			for (int i = 0; i < retList.size(); i++) {
				for(DclLine d:allDclLine){
					if(d.getDclId().equals(retList.get(i).getStpLineId())){
						list1.add(retList.get(i));
					}
				}
			}
			retList = list1;
		}
		return retList;
	}
	
	/**
	 * 系统排名
	 * @param params
	 * @return
	 */
	public List<UserEfficiensyForm> getAllStpYearToSystem(Map<String, Object> params){
		List<DccInfo> dccInfos = dccInfoDao.selectListSystem(params);
		List<UserEfficiensyForm> resultList = new ArrayList<UserEfficiensyForm>(); 
		for (DccInfo dccInfo : dccInfos) {
			String dccId = String.valueOf(dccInfo.getDccId());
			if (dccId!=null) {
				params.put("dcsDccId", dccId);
				List<DcsStation> dcsStations = dcsStationDao.selectListSystem(params);
				for (DcsStation dcsStation : dcsStations) {
					String dcsId = String.valueOf(dcsStation.getDcsId());
					if (dcsId!=null) {
						params.put("stpStationId", dcsId);
						List<Map<String,Object>> stpYears = stpYearDao.selectForYDNX(params);
						UserEfficiensyForm userEfficiensy = new UserEfficiensyForm();
						userEfficiensy.setDccFirstName(dccInfo.getDccFirstName());
						userEfficiensy.setStationName(dcsStation.getDcsName());
						for (Map<String,Object> stpYear : stpYears) {
							float  a= 0;
							try {
								a = (float)stpYear.get("stpElecValueA");
							} catch (Exception e) {

							}
							userEfficiensy.setStpElecValueA(a);
							float  b= 0;
							try {
								b = (float)stpYear.get("stpElecValueB");
							} catch (Exception e) {

							}
							userEfficiensy.setStpElecValueB(b);
							if (userEfficiensy.getStpElecValueA()>userEfficiensy.getStpElecValueB()) {
								userEfficiensy.setMC("下降");
							} else if(userEfficiensy.getStpElecValueA()<userEfficiensy.getStpElecValueB()){
								userEfficiensy.setMC("上升");
							}else {
								userEfficiensy.setMC("不变");
							}
						}
						resultList.add(userEfficiensy);
					}
				}
			}
		}
		return resultList;
	}
	
	/**
	 * 根据站点或者回路查询每月中的总值
	 * @param params
	 * @return
	 */
	public Set<Map<String, Object>> getForYear(Map<String,Object> params){
		List<StpYear> stpYears = stpYearDao.selectForYear(params);
		Date stpSyncTime = (Date) params.get("stpSyncTime");
		Date time = stpSyncTime;
		time.setMonth(0);
		time.setDate(1);
		Set<Map<String, Object>> resultLine = new LinkedHashSet<Map<String,Object>>();//有序(进去什么顺序出来就是什么顺序)不重复
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(time);
		for(int i = 0; i < 12; i++){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("d", 0);
			String da1 = sdf.format(calendar.getTime());
			map.put("t", da1.substring(5, 7));
			calendar.add(Calendar.MONTH, 1);
			String da2 = sdf.format(calendar.getTime());
			for(StpYear stpYear : stpYears){
				String da = sdf.format(stpYear.getStpSyncTime());
				if(da.compareTo(da1) >=0&&da.compareTo(da2)<=0){
					map.put("d", stpYear.getStpElecValue()); 
				}
			}
			resultLine.add(map);
			
		}
		return resultLine;
	}
	
	/**
	 * 
	* <AUTHOR> 
	* @Description  年报表行转列
	* @date 2018年7月3日下午5:45:25  
	 */
	public Map<String , Object> getStpYearColList(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		List<StpYearCol> list =stpYearDao.selectForYearToCol(params);
		resultMap.put("rows",list);
		params.remove("offset");
		params.remove("limit");
		List<StpYearCol> list1 =stpYearDao.selectForYearToCol(params);
		resultMap.put("total",list1.size());
		return resultMap;
	}
}
