package com.qt.service.statistics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.entity.file.DclLine;
import com.qt.entity.statistics.StlDay;
import com.qt.entity.statistics.StoDayCol;
import com.qt.entity.statistics.StpDay;
import com.qt.repository.statistics.StlDayDao;
import com.qt.service.file.DclLineService;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-16 10:07:33
 */
@Service
public class StlDayService{

	private Logger logger = LoggerFactory.getLogger(StlDayService.class);
     
    @Resource
	private StlDayDao stlDayDao;
	@Autowired
	private DclLineService dclLineService;
	
  /* 负荷分析日数据表格数据 */
	public Map<String , Object> getStlDayListDay(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", stlDayDao.selectDayList(queryMap));
		resultMap.put("total", stlDayDao.selectCount(queryMap));
		return resultMap;
	}
	//根据条件搜索记录
		public Map<String , Object> getStlDayFhList(Map<String, Object> params){
			Map<String , Object> queryMap = new HashMap<String, Object>();
			Map<String , Object> resultMap = new HashMap<String, Object>();
			//TODO 根据需要封装查询需要的条件
			queryMap.putAll(params);
			List<StlDay> list =stlDayDao.selectFHList(queryMap);
			resultMap.put("rows", list);
			resultMap.put("total", list.size());
			return resultMap;
		}
    
		
		public Map<String , Object> selectDayFh(Map<String, Object> params){
			Map<String, Object> queryMap = new HashMap<String, Object>();
			Map<String, Object> resultMap = new HashMap<String, Object>();
			// TODO 根据需要封装查询需要的条件
			queryMap.putAll(params);
			List<DclLine> lineList = dclLineService.getAllDclLine(null);
			List<StlDay> list1 = new ArrayList<StlDay>();
			List<StlDay> list2 = new ArrayList<StlDay>();
			if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
				if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
					list1 = stlDayDao.selectDayFh(queryMap);
						
				}else{
					list1 = stlDayDao.selectDayFh(queryMap);
				}
			}else{
				//查询变压器下面的回路
				Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
				dclLineParams.put("dclDcsId", params.get("stlStationId"));
				dclLineParams.put("dclDcbId", params.get("stlByqId"));
				List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
				//System.out.println("----------allDclLine------------"+allDclLine.size());
				list1 = stlDayDao.selectDayFh(queryMap);
				
					for (int i = 0; i < list1.size(); i++) {
						for(DclLine d:allDclLine){
							if(d.getDclId().equals(list1.get(i).getStlLineId())){
								list2.add(list1.get(i));
							}
						}
					}
					list1 = list2;
			}
			resultMap.put("rows",list1);
			resultMap.put("total",list1.size());
			return resultMap;
		}
		
		
		public List<StlDay> selectDayFh2(Map<String, Object> params){
			Map<String, Object> queryMap = new HashMap<String, Object>();
			// TODO 根据需要封装查询需要的条件
			queryMap.putAll(params);
			List<DclLine> lineList = dclLineService.getAllDclLine(null);
			List<StlDay> list1 = new ArrayList<StlDay>();
			List<StlDay> list2 = new ArrayList<StlDay>();
			if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
				if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
					list1 = stlDayDao.selectDayFh(queryMap);
						
				}else{
					list1 = stlDayDao.selectDayFh(queryMap);
				}
			}else{
				//查询变压器下面的回路
				Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
				dclLineParams.put("dclDcsId", params.get("stlStationId"));
				dclLineParams.put("dclDcbId", params.get("stlByqId"));
				List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
				//System.out.println("----------allDclLine------------"+allDclLine.size());
				list1 = stlDayDao.selectDayFh(queryMap);
				
					for (int i = 0; i < list1.size(); i++) {
						for(DclLine d:allDclLine){
							if(d.getDclId().equals(list1.get(i).getStlLineId())){
								list2.add(list1.get(i));
							}
						}
					}
					list1 = list2;
			}
			return list1;
		}
		
		
		
		
    
	//根据条件搜索记录
	public Map<String , Object> getStlDayList(Map<String, Object> params){
		/*Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", stlDayDao.selectList(queryMap));
		resultMap.put("total", stlDayDao.selectCount(queryMap));
		return resultMap;*/
		System.out.println("--------------进入getStpDayList-------------------");
		Map<String, Object> queryMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		List<StlDay> list = new ArrayList<StlDay>();
		String x =(String) queryMap.get("stlStationId");
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				for(DclLine dclLine:lineList){
					Integer s =	dclLine.getDclDcsId();
					String ss = s.toString();
					if(ss.equals(x)){
						queryMap.put("stlLineId", dclLine.getDclId());
						queryMap.put("stlStationId", dclLine.getDclDcsId());
						List<StlDay> list1 =stlDayDao.selectList(queryMap);
						if(list1.size() !=0){
							list.addAll(list1);
						}	
					}
				}
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				List<StlDay> list2 =stlDayDao.selectList(queryMap);
				list.addAll(list2);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			//System.out.println("----------allDclLine------------"+allDclLine.size());
			
			for(DclLine d:allDclLine){
				queryMap.put("stlStationId", d.getDclDcsId());
				queryMap.put("stlLineId", d.getDclId());
				List<StlDay> list3 =stlDayDao.selectList(queryMap);
				list.addAll(list3);
			}
		}
		
		resultMap.put("rows",list);
		resultMap.put("total",list.size());
		return resultMap;
	}
	
	
	public List<StlDay> getAllStlDay(Map<String, Object> params){
		//return stlDayDao.selectList(params);
		System.out.println("--------------进入getStpDayList-------------------");
		Map<String, Object> queryMap = new HashMap<String, Object>();
		List<StlDay> retList = new ArrayList<StlDay>();
		queryMap.putAll(params);
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		
		String x =(String) queryMap.get("stlStationId");
		if(params.get("stlByqId") == null || "".equals(params.get("stlByqId"))){
			if(params.get("stlLineId") == null || "".equals(params.get("stlLineId"))){
				for(DclLine dclLine:lineList){
					Integer s =	dclLine.getDclDcsId();
					String ss = s.toString();
					if(ss.equals(x)){
						queryMap.put("stlLineId", dclLine.getDclId());
						queryMap.put("stlStationId", dclLine.getDclDcsId());
						List<StlDay> list1 =stlDayDao.selectList(queryMap);
						if(list1.size() !=0){
							retList.addAll(list1);
						}	
					}
				}
			}else{
				queryMap.put("stlLineId", params.get("stlLineId"));
				queryMap.put("stlStationId", params.get("stlStationId"));
				
				List<StlDay> list2 =stlDayDao.selectList(queryMap);
				retList.addAll(list2);
			}
		}else{
			//查询变压器下面的回路
			System.out.println("--------------进入byq方法-------------------");
			Map<String, Object> dclLineParams = new HashMap<String, Object>();// 查询回路
			dclLineParams.put("dclDcsId", params.get("stlStationId"));
			dclLineParams.put("dclDcbId", params.get("stlByqId"));
			List<DclLine> allDclLine = dclLineService.getAllDclLine(dclLineParams);
			//System.out.println("----------allDclLine------------"+allDclLine.size());
			
			for(DclLine d:allDclLine){
				queryMap.put("stlStationId", d.getDclDcsId());
				queryMap.put("stlLineId", d.getDclId());
				List<StlDay> list3 =stlDayDao.selectList(queryMap);
				retList.addAll(list3);
			}
		}
		
		return retList;
	}
	
	//带条件查询负荷对比
	public List<StlDay> getAllStlDayFH(Map<String, Object> params){
		return stlDayDao.selectListFH(params);
	}
	
	/**
	 * 负荷分析——查询月报表列表（带条件）   
	 * 因stlday存放的是每日数据，所以修复查询条件改为月  20180720
	 * @param params
	 * @return
	 * @throws JSONException
	 */
	public List<StlDay> selectStlDayToMonthList(Map<String, Object> params) {
		return stlDayDao.selectStlDayToMonthList(params);
	}
	
	/**
	 * 负荷分析——查询月报表列表总数（带条件）   
	 * 因stlday存放的是每日数据，所以修复查询条件改为月  20180720
	 * @param params
	 * @return
	 * @throws JSONException
	 */
	public int selectCountToMonth(Map<String, Object> params) {
		return stlDayDao.selectCountToMonth(params);
	}
	
	/**
	 * 负荷分析——查询日报表（负荷原始值）列表（带条件）   
	 * 因stlday存放的是每日最值数据，所以查询stodatarecord
	 * @param params
	 * @return
	 * @throws JSONException
	 */
	public Map<String , Object> selectStoDayToColList(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<StoDayCol> list =stlDayDao.selectStoDayToColList(params);
		resultMap.put("rows",list);
		params.remove("offset");
		params.remove("limit");
		List<StoDayCol> list1 =stlDayDao.selectStoDayToColList(params);
		resultMap.put("total",list1.size());
		return resultMap;
	}
	
}
