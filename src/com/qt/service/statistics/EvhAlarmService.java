package com.qt.service.statistics;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DccInfo;
import com.qt.entity.statistics.EvhAlarm;
import com.qt.entity.statistics.EvrAlarm;
import com.qt.entity.statistics.StlDay;
import com.qt.repository.statistics.EvhAlarmDao;
import com.qt.repository.statistics.EvrAlarmDao;
import com.qt.repository.statistics.StlDayDao;

/**
 * TODO shaobo
 * @date 2016-09-08 10:07:33
 */
@Service
public class EvhAlarmService{

	private Logger logger = LoggerFactory.getLogger(EvhAlarmService.class);
     
    @Resource
	private EvhAlarmDao evhAlarmDao;
	
	//根据条件搜索记录
	public Map<String , Object> getEvhAlarmList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", evhAlarmDao.selectList(queryMap));
		resultMap.put("total", evhAlarmDao.selectCount(queryMap));
		return resultMap;
	}
	public List<EvhAlarm> getAllEvhAlarm(Map<String, Object> params){
		return evhAlarmDao.selectList(params);
	}
	
	public int count1(Map<String, Object> params){
		return evhAlarmDao.selectCount(params);
	}
	
	//根据采集编码查询对应的总条数
	public Map<String , Object> count(Map<String, Object> params){
		Map<String , Object> map = new HashMap<String, Object>();
		//Map<String , Object> map = evhAlarmDao.count(params);
		int value1 = 0;
		if (value1 == 0) {
			params.put("evhBsaCode", 10001);
			int A1 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10002);
			int A2 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10003);
			int A3 = evhAlarmDao.count(params);
			value1 = A1+A2+A3;
		}
		int value2 = 0;
		if (value2 == 0) {
			params.put("evhBsaCode", 10004);
			int A1 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10005);
			int A2 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10006);
			int A3 = evhAlarmDao.count(params);
			value2 = A1+A2+A3;
		}
		int value3 = 0;
		if (value3 == 0) {
			params.put("evhBsaCode", 10010);
			int A1 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10011);
			int A2 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10012);
			int A3 = evhAlarmDao.count(params);
			value3 = A1+A2+A3;
		}
		int value4 = 0;
		if (value4 == 0) {
			params.put("evhBsaCode", 10007);
			int A1 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10008);
			int A2 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10009);
			int A3 = evhAlarmDao.count(params);
			value4 = A1+A2+A3;
		}
		int value5 = 0;
		if (value5 == 0) {
			params.put("evhBsaCode", 10073);
			int A1 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10074);
			int A2 = evhAlarmDao.count(params);
			params.put("evhBsaCode", 10075);
			int A3 = evhAlarmDao.count(params);
			value5 = A1+A2+A3;
		}
		map.put("value5", value5);
		map.put("value4", value4);
		map.put("value3", value3);
		map.put("value2", value2);
		map.put("value1", value1);
		return map;
	}
	
	//根据采集编码查询对应的总条数--告警分析环比
	public Map<String , Object> countHB(Map<String, Object> params){
		Map<String , Object> map = new HashMap<String, Object>();
		//Map<String , Object> map = evhAlarmDao.count(params);
		int value1 = 0;
		if (value1 == 0) {
			params.put("evhBsaCode", 10001);
			int A1 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10002);
			int A2 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10003);
			int A3 = evhAlarmDao.count1(params);
			value1 = A1+A2+A3;
		}
		int value2 = 0;
		if (value2 == 0) {
			params.put("evhBsaCode", 10004);
			int A1 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10005);
			int A2 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10006);
			int A3 = evhAlarmDao.count1(params);
			value2 = A1+A2+A3;
		}
		int value3 = 0;
		if (value3 == 0) {
			params.put("evhBsaCode", 10010);
			int A1 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10011);
			int A2 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10012);
			int A3 = evhAlarmDao.count1(params);
			value3 = A1+A2+A3;
		}
		int value4 = 0;
		if (value4 == 0) {
			params.put("evhBsaCode", 10007);
			int A1 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10008);
			int A2 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10009);
			int A3 = evhAlarmDao.count1(params);
			value4 = A1+A2+A3;
		}
		int value5 = 0;
		if (value5 == 0) {
			params.put("evhBsaCode", 10073);
			int A1 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10074);
			int A2 = evhAlarmDao.count1(params);
			params.put("evhBsaCode", 10075);
			int A3 = evhAlarmDao.count1(params);
			value5 = A1+A2+A3;
		}
		map.put("value5", value5);
		map.put("value4", value4);
		map.put("value3", value3);
		map.put("value2", value2);
		map.put("value1", value1);
		return map;
	}
	
	//新增记录
	public void addEvhAlarm(Map<String, Object> params){
			//TODO 根据需要封装查询需要的条件
			try {
				evhAlarmDao.insert(params);
			} catch (Exception e) {
				logger.error("添加失败",e);
			}
	}
	
	//修改历史报警表
	public void updateEvhAlarm(Map<String, Object> params){
		evhAlarmDao.update(params);
	}
	
	/**
	 * 大屏列表返回30条最新的报警记录
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> getEvhAlarmListForBigScreen(Map<String, Object> params){
		return evhAlarmDao.getEvhAlarmListForBigScreen(params);
	}
	
	public int selectAlarmStationCount(Map<String, Object> params){
		return evhAlarmDao.selectCount1(params);
	}
	
	public int selectAlarmCount(Map<String, Object> params){
		return evhAlarmDao.selectCount2(params);
	}
	
	public List<Map<String,Object>> selectAlarmStations(Map<String, Object> params){
		return evhAlarmDao.selectEvhStation(params);
	}

	/**
	 * 根据时间维度查询报警数量
	 * @param params
	 * @return
	 */
	public List<EvhAlarm> selectCountsForTime(Map<String,Object> params){
		return evhAlarmDao.selectCountsForTime(params);
	}
	/**
	 * 根据bsa_code的遥信、遥测查询报警数量
	 * @param params
	 * @return
	 */
	public List<EvhAlarm> selectCountsForYType(Map<String,Object> params) {
		return evhAlarmDao.selectCountsForYType(params);
	}
	/**
	 * 根据设备查询报警数量
	 * @param params
	 * @return
	 */
	public List<EvhAlarm> selectCountsForLine(Map<String,Object> params) {
		return evhAlarmDao.selectCountsForLine(params);
	}
	/**
	 * 根据报警等级查询报警数量
	 * @param params
	 * @return
	 */
	public List<EvhAlarm> selectCountsForEvhType(Map<String,Object> params) {
		return evhAlarmDao.selectCountsForEvhType(params);
	}
	/**
	 * 报警监控表格数据
	 * @param params
	 * @return
	 */
	public List<EvhAlarm> selectCountsForTables(Map<String,Object> params) {
		return evhAlarmDao.selectCountsForTables(params);
	}
	
	/**
	 * 将实时报警写入历史报警
	 * @param params
	 * @return
	 */
	public boolean selectEvrToEvh(Map<String,Object> params) {
		try {
			evhAlarmDao.selectEvrToEvh(params);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}

	/**
	 * 将实时报警List写入历史报警
	 * @param params
	 * @return
	 */
	public boolean selectEvrToEvhList(Map<String,Object> params) {
		try {
			evhAlarmDao.selectEvrToEvhList(params);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}

}
