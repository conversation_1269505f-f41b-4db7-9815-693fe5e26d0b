package com.qt.service.base;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.baselib.BsaCode;
import com.qt.repository.base.BsaCodeDao;

@Service
public class BsaCodeService {

	private Logger logger = LoggerFactory.getLogger(BsaCodeService.class);

	@Resource
	private BsaCodeDao bsaCodeDao;
	
	//查询指标码，带分页功能
	public Map<String, Object> bsaCodeList(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		resultMap.put("rows", bsaCodeDao.getBsaCodeList(queryMap));
		resultMap.put("total", bsaCodeDao.GetCountBsaCode(queryMap));
		return resultMap;   
	}
	
	//查询所有的指标码
	public List<BsaCode> getAllBsaCode(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		return bsaCodeDao.getAllBsaCode(queryMap);   
	}
	
	//根据逗号分隔的指标码  10001,10002等 查询已关联、未关联的指标
	public Map<String, Object> getCheckAndUncheckCodes(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		List<BsaCode> checkAndUncheckCodes = bsaCodeDao.getCheckAndUncheckCodes(params);
		
		List<BsaCode>  checkedBsaCode= new ArrayList<BsaCode>();
		List<BsaCode>  noCheckedBsaCode= new ArrayList<BsaCode>();
		/*for (BsaCode bsaCode : checkAndUncheckCodes) {
			if(bsaCode.getBsaFlag()==1){//已关联
				checkedBsaCode.add(bsaCode);
			}else{
				noCheckedBsaCode.add(bsaCode);//未关联
			}
		}	*/				
		queryMap.put("checkedBsaCode", checkedBsaCode);
		queryMap.put("noCheckedBsaCode", noCheckedBsaCode);
		return queryMap;   
	}
	
	//据指标分组bsaCategory查询已关联和未关联的指标码,每个指标只能有一个分组
	public Map<String, Object> getCheckAndUncheckCodesForCategory(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		List<BsaCode> checkAndUncheckCodes = bsaCodeDao.getCheckAndUncheckCodesForCategory(params);
		
		List<BsaCode>  checkedBsaCode= new ArrayList<BsaCode>();
		List<BsaCode>  noCheckedBsaCode= new ArrayList<BsaCode>();
		/*for (BsaCode bsaCode : checkAndUncheckCodes) {
			if(bsaCode.getBsaFlag()==1){//已关联
				checkedBsaCode.add(bsaCode);
			}else{
				noCheckedBsaCode.add(bsaCode);//未关联
			}
		}	*/				
		queryMap.put("checkedBsaCode", checkedBsaCode);
		queryMap.put("noCheckedBsaCode", noCheckedBsaCode);
		return queryMap;   
	}
	
	
	
	
	
	
	//查询已关联的指标码所有信息
	public List<BsaCode> getCheckedBsaCode(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		return bsaCodeDao.getCheckedBsaCode(queryMap);   
	}
	
	//根据已关联的指标码，查询其他的指标码
	public List<BsaCode> getUnCheckedBsaCode(Map<String, Object> params) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		return bsaCodeDao.getUnCheckedBsaCode(queryMap);   
	}

	//删除
	public boolean delBsaCode(String[] idArray) {
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Integer[] bsaIds = new Integer[idArray.length];
		for (int i = 0; i < idArray.length; i++) {
			bsaIds[i]=Integer.parseInt(idArray[i]);
		}
		queryMap.put("bsaIds", bsaIds);
		try {
		bsaCodeDao.delBsaCode(queryMap);
		return true;
		} catch (Exception e) {
			// TODO: handle exception
			return false;
		}
	}

	//新增
	public boolean addBsaCode(Map<String, Object> params) {
		try {
			//TODO 根据需要封装查询需要的条件
			BsaCode temp=MapUtil.toObject(BsaCode.class, params);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			temp.setBsaTime(sdf.format(new Date()));
			bsaCodeDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//获取新增指标码用到的ID
	public Integer getBsaIdForInsert() {
		return bsaCodeDao.getBsaIdForInsert();
	}
	
	
	//修改
	public boolean updateBsaCode(BsaCode bsaCode) {
		try {
			bsaCodeDao.update(bsaCode);
			return true;
		} catch (Exception e) {
			// TODO: handle exception
			return false;
		}
	}
	
	//调用普通查询方法，未传分页参数limit，不进行分页
	public List<BsaCode> getBsaCodeForExport(Map<String, Object> params) {
		return bsaCodeDao.getBsaCodeList(params);
	}

	public BsaCode checkBsaCode(Map<String, Object> params) {
		// TODO Auto-generated method stub
		return bsaCodeDao.checkBsaCode(params);
	}

	public List<BsaCode> getBsaCodeByGroupId(Map<String,Object> params){
		return bsaCodeDao.getBsaCodeByGroupId(params);
	}

	public List<BsaCode> getBsaCodeByGroupIds(Map<String,Object> params){
		return bsaCodeDao.getBsaCodeByGroupIds(params);
	}

	public List<BsaCode> getBsaCodeByYtype(Map<String,Object> params){
		return bsaCodeDao.getBsaCodeByYtype(params);
	}

	public List<BsaCode> selectList(Map<String, Object> params){
		return bsaCodeDao.selectList(params);
	}
}
