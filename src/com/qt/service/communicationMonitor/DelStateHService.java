package com.qt.service.communicationMonitor;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.communicationMonitor.DelStateHDao;
import com.qt.entity.communicationMonitor.DelStateH;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-27 02:56:12
 */
@Service
public class DelStateHService{

	private Logger logger = LoggerFactory.getLogger(DelStateHService.class);
     
    @Resource
	private DelStateHDao delStateHDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDelStateHList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", delStateHDao.selectList(queryMap));
		resultMap.put("total", delStateHDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDelStateH(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DelStateH temp=MapUtil.toObject(DelStateH.class, params);
			delStateHDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDelStateH(DelStateH delStateH){
		delStateHDao.update(delStateH);
	}
	
	//删除记录
	public int delDelStateH(Map<String, Object> params){
		return delStateHDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDelStateHBat(String[] idArray){
		for(String pk:idArray){
			delStateHDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DelStateH> getAllDelStateH(Map<String, Object> params){
		return delStateHDao.selectList(params);
	}
	
}
