package com.qt.service.communicationMonitor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.communicationMonitor.DevStateR;
import com.qt.repository.communicationMonitor.DevStateRDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-27 02:55:14
 */
@Service
public class DevStateRService{

	private Logger logger = LoggerFactory.getLogger(DevStateRService.class);
     
    @Resource
	private DevStateRDao devStateRDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDevStateRList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devStateRDao.selectList(queryMap));
		resultMap.put("total", devStateRDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDevStateR(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevStateR temp=MapUtil.toObject(DevStateR.class, params);
			devStateRDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDevStateR(DevStateR devStateR){
		devStateRDao.update(devStateR);
	}
	
	//删除记录
	public int delDevStateR(Map<String, Object> params){
		return devStateRDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDevStateRBat(String[] idArray){
		for(String pk:idArray){
			devStateRDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DevStateR> getAllDevStateR(Map<String, Object> params){
		return devStateRDao.selectList(params);
	}
	
	
	/**
	 * 查找网关和设备在线离线的list
	 * @param params
	 * @return
	 */
	public Map<String , Object> getDelAndDevList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devStateRDao.selectDelAndDevCounts(queryMap));
		resultMap.put("total", devStateRDao.selectDelAndDevCountsCount(queryMap));
		return resultMap;
	}
	
}
