package com.qt.service.communicationMonitor;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.communicationMonitor.DelStateRDao;
import com.qt.entity.communicationMonitor.DelStateR;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-27 02:55:59
 */
@Service
public class DelStateRService{

	private Logger logger = LoggerFactory.getLogger(DelStateRService.class);
     
    @Resource
	private DelStateRDao delStateRDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDelStateRList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", delStateRDao.selectList(queryMap));
		resultMap.put("total", delStateRDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDelStateR(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DelStateR temp=MapUtil.toObject(DelStateR.class, params);
			delStateRDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDelStateR(DelStateR delStateR){
		delStateRDao.update(delStateR);
	}
	
	//删除记录
	public int delDelStateR(Map<String, Object> params){
		return delStateRDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDelStateRBat(String[] idArray){
		for(String pk:idArray){
			delStateRDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DelStateR> getAllDelStateR(Map<String, Object> params){
		return delStateRDao.selectList(params);
	}
	
}
