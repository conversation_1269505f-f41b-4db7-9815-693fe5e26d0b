package com.qt.service.communicationMonitor;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.communicationMonitor.DevStateHDao;
import com.qt.entity.communicationMonitor.DevStateH;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-27 02:55:36
 */
@Service
public class DevStateHService{

	private Logger logger = LoggerFactory.getLogger(DevStateHService.class);
     
    @Resource
	private DevStateHDao devStateHDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDevStateHList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devStateHDao.selectList(queryMap));
		resultMap.put("total", devStateHDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDevStateH(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevStateH temp=MapUtil.toObject(DevStateH.class, params);
			devStateHDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDevStateH(DevStateH devStateH){
		devStateHDao.update(devStateH);
	}
	
	//删除记录
	public int delDevStateH(Map<String, Object> params){
		return devStateHDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDevStateHBat(String[] idArray){
		for(String pk:idArray){
			devStateHDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DevStateH> getAllDevStateH(Map<String, Object> params){
		return devStateHDao.selectList(params);
	}
	
}
