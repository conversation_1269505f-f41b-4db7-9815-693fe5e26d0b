package com.qt.service.operations;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.operations.OptIptMnt;
import com.qt.repository.operations.OptIptMntDao;


/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-12-13 02:27:12
 */
@Service
public class OptIptMntService{

	private Logger logger = LoggerFactory.getLogger(OptIptMntService.class);
     
    @Resource
	private OptIptMntDao optIptMntDao;
	

	//根据条件搜索记录
	public Map<String , Object> getOptIptMntList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", optIptMntDao.selectList(queryMap));
		resultMap.put("total", optIptMntDao.selectCount(queryMap));
		return resultMap;
	}
       
/*	//新增记录
	public void addOptIptMnt(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			OptIptMnt temp=MapUtil.toObject(OptIptMnt.class, params);
			temp.setId(SerialNo.getUNID());
			optIptMntDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}*/
	
	//更新记录
	public void updateOptIptMnt(OptIptMnt optIptMnt){
		optIptMntDao.update(optIptMnt);
	}
	
	//删除记录
	public int delOptIptMnt(Map<String, Object> params){
		return optIptMntDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delOptIptMntBat(String[] idArray){
		for(String pk:idArray){
			optIptMntDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<OptIptMnt> getAllOptIptMnt(Map<String, Object> params){
		return optIptMntDao.selectList(params);
	}
	
}
