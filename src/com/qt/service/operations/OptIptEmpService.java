package com.qt.service.operations;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.operations.OptIptEmp;
import com.qt.repository.operations.OptIptEmpDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-12-14 04:28:50
 */
@Service
public class OptIptEmpService{

	private Logger logger = LoggerFactory.getLogger(OptIptEmpService.class);
     
    @Resource
	private OptIptEmpDao optIptEmpDao;
	
	//根据条件搜索记录
	public Map<String , Object> getOptIptEmpList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", optIptEmpDao.selectList(queryMap));
		resultMap.put("total", optIptEmpDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addOptIptEmp(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			OptIptEmp temp=MapUtil.toObject(OptIptEmp.class, params);
			optIptEmpDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateOptIptEmp(OptIptEmp optIptEmp){
		optIptEmpDao.update(optIptEmp);
	}
	
	//删除记录
	public int delOptIptEmp(Map<String, Object> params){
		return optIptEmpDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delOptIptEmpBat(String[] idArray){
		for(String pk:idArray){
			optIptEmpDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<OptIptEmp> getAllOptIptEmp(Map<String, Object> params){
		return optIptEmpDao.selectList(params);
	}
	
}
