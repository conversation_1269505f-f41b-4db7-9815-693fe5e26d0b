package com.qt.service.baselib;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.baselib.BscType;
import com.qt.repository.baselib.BscTypeDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-06 04:16:38
 */
@Service
public class BscTypeService{

	private Logger logger = LoggerFactory.getLogger(BscTypeService.class);
     
    @Resource
	private BscTypeDao bscTypeDao;
	
	//根据条件搜索记录
	public Map<String , Object> getBscTypeList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", bscTypeDao.selectList(queryMap));
		resultMap.put("total", bscTypeDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addBscType(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			BscType temp=MapUtil.toObject(BscType.class, params);
//			temp.setId(SerialNo.getUNID());
			bscTypeDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateBscType(BscType bscType){
		bscTypeDao.update(bscType);
	}
	
	//删除记录
	public int delBscType(Map<String, Object> params){
		return bscTypeDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delBscTypeBat(String[] idArray){
		for(String pk:idArray){
			bscTypeDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<BscType> getAllBscType(Map<String, Object> params){
		return bscTypeDao.selectList(params);
	}
	
	//不带分页查询所有
	public List<BscType> getAllBscTypePM(Map<String, Object> params){
		return bscTypeDao.selectListPM(params);
	}
	
}
