package com.qt.service.system.account;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.entity.system.account.OpeIndexSetting;
import com.qt.repository.system.account.OpeIndexSettingDao;

/**
 * 运行指标设置
 * <AUTHOR>
 *
 */
@Service
public class OpeIndexSettingService {

	@Autowired
	private OpeIndexSettingDao opeIndexSettingDao;
	
	/**
	 * 查询
	 * @param params
	 * @return
	 */
	public Map<String,Object> queryList(Map<String,Object> params){
		Map<String,Object> reMap = new HashMap<String, Object>();
		List<OpeIndexSetting> list = opeIndexSettingDao.queryList(params);
		reMap.put("results", list);
		reMap.put("total", opeIndexSettingDao.queryListCount(params));
		return reMap;
	}
	
	/**
	 * 用电参数查找
	 * @param params
	 * @return
	 */
	public List<OpeIndexSetting> selYDCSData(Map<String,Object> params){
		return opeIndexSettingDao.selYDCSData(params);
	}
	
	/**
	 * 根据lineTypeId查找列表
	 * @param params
	 * @return
	 */
	public List<OpeIndexSetting> selByLineTypeId(Map<String,Object> params){
		return opeIndexSettingDao.queryList(params);
	}
	
	/**
	 * 查单个
	 * @param params
	 * @return
	 */
	public OpeIndexSetting findById(Map<String,Object> params){
		return opeIndexSettingDao.findById(params);
	}
	
	/**
	 * 查询Code选中和未选中
	 * @param params
	 * @return
	 */
	public Map<String, Object> selectCodeCheck(Map<String,Object> params){
		Map<String, Object> maps = new HashMap<>();
		maps.put("codeChecked", opeIndexSettingDao.getCodeChecked(params));
		maps.put("codeNoChecked", opeIndexSettingDao.getCodeNoChecked(params));
		return maps;
	}
	
	/**
	 * code已经选择的
	 * @param params
	 * @return
	 */
	public List<OpeIndexSetting> getCodeChecked(Map<String,Object> params){
		return opeIndexSettingDao.getCodeChecked(params);
	}
	
	/**
	 * 新增
	 * @param ope
	 */
	public void insert(OpeIndexSetting ope){
		opeIndexSettingDao.insert(ope);
	}
	
	/**
	 * 修改
	 * @param ope
	 */
	public void update(OpeIndexSetting ope){
		opeIndexSettingDao.update(ope);
	}
	
	/**
	 * 删除
	 * @param params
	 */
	public void delete(Map<String,Object> params){
		opeIndexSettingDao.delete(params);
	}
	
	/**
	 * 用电参数下拉框用
	 * @param params
	 * @return
	 */
	public List<OpeIndexSetting> getBsaCodeSelForRealTime(Map<String,Object> params){
		return opeIndexSettingDao.getBsaCodeSelForRealTime(params);
	}
}
