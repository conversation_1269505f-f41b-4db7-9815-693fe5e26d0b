package com.qt.service.system.account;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.qt.entity.system.account.AccountStation;
import com.qt.repository.system.account.AccountStationDao;
import com.qt.service.statistics.StlMonthService;

/**
 * TODO yanghui
 * @date 2016-9-18 15:00:09
 */
@Service
public class AccountStationService {
	
	private Logger logger = LoggerFactory.getLogger(StlMonthService.class);
	
	@Autowired
	private AccountStationDao accountStationDao;
	
	public List<AccountStation> getStationById(String accountId){
		return accountStationDao.findById(accountId);
	}
	
	public Map<String, Object> find(String accountId){
		return accountStationDao.find(accountId);
	}
	
	public List<AccountStation> selectList(Map<String, Object> params){
		return accountStationDao.selectList(params);
	}
	
	public void update(AccountStation o){
		accountStationDao.update(o);
	}
	
	public void insert(AccountStation o){
		accountStationDao.insert(o);
	}

}
