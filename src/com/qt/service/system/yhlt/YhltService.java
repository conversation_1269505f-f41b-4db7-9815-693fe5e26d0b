package com.qt.service.system.yhlt;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.qt.common.utils.MapUtil;
import com.qt.entity.system.yhlt.Yhlt;
import com.qt.repository.system.yhlt.YhltDao;


/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-16 10:07:33
 */
@Service
public class YhltService{

	private Logger logger = LoggerFactory.getLogger(YhltService.class);
     
    @Resource
	private YhltDao yhltDao;
	
    public void addYhlt(Map<String, Object> params){
    	try {
			//TODO 根据需要封装查询需要的条件
			Yhlt temp=MapUtil.toObject(Yhlt.class, params);
			yhltDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
		
		
	} ;
	
	
	public void insertList(List<Yhlt> list){
		yhltDao.insertList(list);
		
	} ;
	
	
	public void update(Yhlt object){
		yhltDao.update(object);
		
	};
	
	public Yhlt selectByPk(String pk){
		return yhltDao.selectByPk(pk);
		
		
	} ;
	
	public int deleteByMap(Map<String,Object> params){
		return yhltDao.deleteByMap(params);
		
		
	};
	
	
	public int deleteByPk(String pk){
		return yhltDao.deleteByPk(pk);
		
		
	};
	//根据主键批量删除
	public int delYhltBat(String[] idArray){
		for(String pk:idArray){
				yhltDao.deleteByPk(pk);
		}
		return idArray.length;
	};
	
	//在service层处理业务逻辑，调用底层dao的方法，封装业务逻辑
	public Map<String , Object> getYhltInfoList(Map<String,Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		resultMap.put("total", yhltDao.selectCount(params));
		resultMap.put("rows", yhltDao.selectList(params));
		
		return resultMap;
	} ;
	
	
	public List<Yhlt> selectListFH(Map<String,Object> params){
		return yhltDao.selectListFH(params);
		
		
	} ;
	
	
	public int selectCount(Map<String,Object> params){
		return yhltDao.selectCount(params);
		
		
	};
    
    
}
