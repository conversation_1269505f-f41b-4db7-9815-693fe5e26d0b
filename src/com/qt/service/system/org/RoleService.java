package com.qt.service.system.org;

import java.util.List;

import com.qt.common.mybatis.Page;
import com.qt.common.utils.tree.entity.ZNodes;
import com.qt.entity.system.org.Role;
import com.qt.service.base.BaseService;

public interface RoleService extends BaseService<Role>{

	 /**
     * 权限列表包含按钮
     * @param roleId 角色Id
     * @param layer  显示层级
     * @return
     */
	public List<ZNodes> listAuthorized(String roleId,String layer);
	 /**
     * 根据角色Id保存权限列表
     * @param roleId 角色Id
     * @param auss 权限数组
     * @return
     */
	public void saveAuthorized(String roleId,String auss,String layer);
	 /**
     * 获取所有角色
     * @return
     */
	public Page<Role> findAllRoleByPage(Role o,Page<Role> page);
	
	/**
	 * 获取角色是注册用户的id
	 * @return
	 */
	public List<Role> findZhuCeId();
	
	/**
	 * 根据电话获取用户id
	 * @return
	 */
	public String findLoginNameById(String loginName);
}
