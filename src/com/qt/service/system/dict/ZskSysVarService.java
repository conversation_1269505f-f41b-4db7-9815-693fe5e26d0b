package com.qt.service.system.dict;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.system.dict.ZskSysVar;
import com.qt.repository.system.dict.ZskSysVarDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-10 09:49:39
 */
@Service
public class ZskSysVarService{

	private Logger logger = LoggerFactory.getLogger(ZskSysVarService.class);
     
    @Resource
	private ZskSysVarDao zskSysVarDao;
	
	//根据条件搜索记录
	public Map<String , Object> list(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		
		resultMap.put("rows", zskSysVarDao.selectList(queryMap));
		resultMap.put("total", zskSysVarDao.selectCount(queryMap));
		return resultMap;
	}
	
	//根据条件搜索记录
	public ZskSysVar find(String pk){
		return zskSysVarDao.find(pk);
	}
	
	public List<ZskSysVar> listTo(Map<String, Object> params){
		return zskSysVarDao.selectList(params);
	}
       
	//新增记录
	public void add(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			ZskSysVar temp=MapUtil.toObject(ZskSysVar.class, params);
//			temp.setId(SerialNo.getUNID());
			zskSysVarDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void update(ZskSysVar zskSysVar){
		zskSysVarDao.update(zskSysVar);
	}
	
	//根据主键批量删除
	public int del(String[] idArray){
		for(String pk:idArray){
			zskSysVarDao.delete(pk);
		}
		return idArray.length;
	}
	
}
