package com.qt.service.system.log;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.qt.entity.system.log.SystemInfo;
import com.qt.repository.system.log.SystemInfoDao;

@Service
public class SystemInfoService {
	
	@Resource
	private SystemInfoDao systemInfoDao;

	public SystemInfo select(){
		return systemInfoDao.select();
	}

	public void updLogoPic(SystemInfo systemInfo){systemInfoDao.updLogoPic(systemInfo);}
	
}
