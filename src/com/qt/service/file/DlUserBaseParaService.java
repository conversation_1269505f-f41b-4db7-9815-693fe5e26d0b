package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.file.DlUserBaseInfo;
import com.qt.entity.statistics.DlUserBasePara;
import com.qt.repository.statistics.DlUserBaseParaDao;

@Service
public class DlUserBaseParaService {
	private Logger logger = LoggerFactory.getLogger(DccAreaService.class);
	@Resource
	private DlUserBaseParaDao dlUserBaseParaDao;
	
	public int insertDlUserBasePara(DlUserBasePara dlUserBasePara){
		return dlUserBaseParaDao.insertList(dlUserBasePara);
	}
	
	public List<DlUserBasePara> selectByDlUserBasePara(DlUserBasePara dlUserBasePara){
		return dlUserBaseParaDao.selectByDlUserBasePara(dlUserBasePara);
	}

	public List<DlUserBasePara> selectAllDlUserBasePara(){
		return dlUserBaseParaDao.selectAllDlUserBasePara();
	}
	
	public List<DlUserBasePara> list(){
		return dlUserBaseParaDao.selectAllDlUserBasePara();
	}
	
	//根据条件搜索记录
	public Map<String , Object> list(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dlUserBaseParaDao.selectList(queryMap));
		resultMap.put("total", dlUserBaseParaDao.selectCount(queryMap));
		return resultMap;
	}
	public List<DlUserBasePara> listEXL(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return dlUserBaseParaDao.selectList(params);
	}

}
