package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DccInfo;
import com.qt.repository.file.DccInfoDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-04 02:28:44
 */
@Service
public class DccInfoService{

	private Logger logger = LoggerFactory.getLogger(DccInfoService.class);
     
    @Resource
	private DccInfoDao dccInfoDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDccInfoList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dccInfoDao.selectList(queryMap));
		resultMap.put("total", dccInfoDao.selectCount(queryMap));
		return resultMap;
	}
	
	//查询所有没有关联站点的公司
	public Map<String , Object> getDccInfoListNoStaiton(){
		Map<String , Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows", dccInfoDao.getDccInfoListNoStaiton());
		return resultMap;
	}
	
	
	//根据条件搜索记录---站点
	public Map<String , Object> getDccInfoListZD(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dccInfoDao.selectListZD(queryMap));
		resultMap.put("total", dccInfoDao.selectCountZD(queryMap));
		return resultMap;
	}
	
	//根据条件搜索记录---用电人员
	public List<DccInfo> getDccInfoListRY(Map<String, Object> params){
		return dccInfoDao.selectListRY(params);
	}
       
	//新增记录
	public void addDccInfo(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DccInfo temp=MapUtil.toObject(DccInfo.class, params);
			dccInfoDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDccInfo(DccInfo dccInfo){
		dccInfoDao.update(dccInfo);
	}
	
	//删除记录
	public int delDccInfo(Map<String, Object> params){
		return dccInfoDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDccInfoBat(String[] idArray){
		for(String pk:idArray){
			dccInfoDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DccInfo> getAllDccInfo(Map<String, Object> params){
		return dccInfoDao.selectList(params);
	}
}
