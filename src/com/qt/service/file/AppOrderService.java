package com.qt.service.file;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.AppOrder;
import com.qt.repository.file.AppOrderDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2018-04-24 11:42:37
 */
@Service
public class AppOrderService{

	private Logger logger = LoggerFactory.getLogger(AppOrderService.class);
     
    @Resource
	private AppOrderDao appOrderDao;
	
	//根据条件搜索记录
	public Map<String , Object> getAppOrderList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", appOrderDao.selectList(queryMap));
		resultMap.put("total", appOrderDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addAppOrder(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			AppOrder temp=MapUtil.toObject(AppOrder.class, params);
			appOrderDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateAppOrder(AppOrder appOrder){
		appOrderDao.update(appOrder);
	}
	
	//删除记录
	public int delAppOrder(Map<String, Object> params){
		return appOrderDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delAppOrderBat(String[] idArray){
		for(String pk:idArray){
			appOrderDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<AppOrder> getAllAppOrder(Map<String, Object> params){
		return appOrderDao.selectList(params);
	}
	
}
