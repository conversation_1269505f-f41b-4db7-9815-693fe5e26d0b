package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DlUserBaseInfo;
import com.qt.repository.file.DlUserBaseInfoDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-06 09:49:20
 */
@Service
public class DlUserBaseInfoService{

	private Logger logger = LoggerFactory.getLogger(DlUserBaseInfoService.class);
     
    @Resource
	private DlUserBaseInfoDao dlUserBaseInfoDao;
	
	//根据条件搜索记录
	public Map<String , Object> list(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dlUserBaseInfoDao.selectList(queryMap));
		resultMap.put("total", dlUserBaseInfoDao.selectCount(queryMap));
		return resultMap;
	}
	
	//根据条件搜索记录
	public List<DlUserBaseInfo> listEXL(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return dlUserBaseInfoDao.selectList(params);
	}
	
	//根据条件数据库对应数据
	public int count(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return dlUserBaseInfoDao.selectCountCX(params);
	}
       
	//新增记录
	public void add(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DlUserBaseInfo temp=MapUtil.toObject(DlUserBaseInfo.class, params);
			dlUserBaseInfoDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void update(DlUserBaseInfo dlUserBaseInfo){
		dlUserBaseInfoDao.update(dlUserBaseInfo);
	}
	
	//删除记录
	public int del(String[] idArray){
		for(String pk:idArray){
			dlUserBaseInfoDao.delete(pk);
		}
		return idArray.length;
	}
	
}
