package com.qt.service.file;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DclLine;
import com.qt.entity.file.DcsCjq;
import com.qt.entity.file.SysUser;
import com.qt.repository.file.SysUserDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2018-04-23 03:40:16
 */
@Service
public class SysUserService{

	private Logger logger = LoggerFactory.getLogger(SysUserService.class);
     
    @Resource
	private SysUserDao sysUserDao;
	
	//根据条件搜索记录
	public Map<String , Object> getSysUserList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", sysUserDao.selectList(queryMap));
		resultMap.put("total", sysUserDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addSysUser(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			SysUser temp=MapUtil.toObject(SysUser.class, params);
			/*temp.setId(SerialNo.getUNID());*/
			sysUserDao.insert(temp);
			
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateSysUser(SysUser sysUser){
		sysUserDao.update(sysUser);
	}
	
	//删除记录
	public int delSysUser(Map<String, Object> params){
		return sysUserDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delSysUserBat(String[] idArray){
		for(String pk:idArray){
			sysUserDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<SysUser> getAllSysUser(Map<String, Object> params){
		return sysUserDao.selectList(params);
	}
	
	


//根据站点ID查询
	public Map<String, Object> selectByDcsId(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		queryMap.putAll(params);
		List<SysUser> list=sysUserDao.selectByDcsId(queryMap);
		if(list!=null && list.size()>0){
			resultMap.put("total", list.size());
		}else{
			resultMap.put("total", 0);
		}
		resultMap.put("rows", list);
		
		return resultMap;
	}
}
