package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.StationSb;
import com.qt.repository.file.StationSbDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-06 09:49:20
 */
@Service
public class StationSbService{

	private Logger logger = LoggerFactory.getLogger(StationSbService.class);
     
    @Resource
	private StationSbDao stationSbDao;
	
	//根据条件搜索记录
	public Map<String , Object> list(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", stationSbDao.selectList(queryMap));
		resultMap.put("total", stationSbDao.selectCount(queryMap));
		return resultMap;
	}
	
	//根据条件搜索记录
	public List<StationSb> listEXL(Map<String, Object> params){
		//TODO 根据需要封装查询需要的条件
		return stationSbDao.selectList(params);
	}
	
	//根据条件搜索记录
		public List<StationSb> getAllStationSb(Map<String, Object> params){
			//TODO 根据需要封装查询需要的条件
			return stationSbDao.selectList(params);
		}
       
	//新增记录
	public void add(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			StationSb temp=MapUtil.toObject(StationSb.class, params);
			stationSbDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void update(StationSb stationSb){
		stationSbDao.update(stationSb);
	}
	
	//删除记录
	public int del(String[] idArray){
		for(String pk:idArray){
			stationSbDao.delete(pk);
		}
		return idArray.length;
	}
	
}
