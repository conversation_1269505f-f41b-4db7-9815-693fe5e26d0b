package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DcaAcquis;
import com.qt.repository.file.DcaAcquisDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-06 03:25:22
 */
@Service
public class DcaAcquisService{

	private Logger logger = LoggerFactory.getLogger(DcaAcquisService.class);
     
    @Resource
	private DcaAcquisDao dcaAcquisDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcaAcquisList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcaAcquisDao.selectList(queryMap));
		resultMap.put("total", dcaAcquisDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDcaAcquis(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcaAcquis temp=MapUtil.toObject(DcaAcquis.class, params);
			dcaAcquisDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcaAcquis(DcaAcquis dcaAcquis){
		dcaAcquisDao.update(dcaAcquis);
	}
	
	//删除记录
	public int delDcaAcquis(Map<String, Object> params){
		return dcaAcquisDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcaAcquisBat(String[] idArray){
		for(String pk:idArray){
			dcaAcquisDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DcaAcquis> getAllDcaAcquis(Map<String, Object> params){
		return dcaAcquisDao.selectList(params);
	}
	
}
