package com.qt.service.file;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import com.qt.entity.file.DcSiteData;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.bean.baselib.DateUtil;
import com.qt.common.utils.DateUtils;
import com.qt.common.utils.MapUtil;
import com.qt.entity.baselib.QtTableConfigure;
import com.qt.entity.file.DcsStation;
import com.qt.entity.statistics.StoDataRecord;
import com.qt.repository.base.BsaCodeDao;
import com.qt.repository.base.QtTableConfigureDao;
import com.qt.repository.file.DcsStationDao;
import com.qt.repository.newFile.DceEquipmentDao;
import com.qt.repository.newFile.DcgGatewayDao;
import com.qt.repository.statistics.EvrAlarmDao;
import com.qt.repository.statistics.StoDataRecordDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-06 03:24:25
 */
@Service
public class DcsStationService{

	private Logger logger = LoggerFactory.getLogger(DcsStationService.class);
     
    @Resource
	private DcsStationDao dcsStationDao;
    
    @Resource
	private BsaCodeDao bsaCodeDao;
    
    @Resource
   	private QtTableConfigureDao configureDao;
    
    @Resource
    private EvrAlarmDao evrAlarmDao;
    @Resource
	private StoDataRecordDao stoDataRecordDao;
    @Resource
  	private DcgGatewayDao dcgGatewayDao;
    @Resource
	private DceEquipmentDao dceEquipmentDao;
    
    
    public Map<String , Object> selectListByDccId(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsStationDao.selectListByDccId(queryMap));
		return resultMap;
	}
    
    /**
	 * 根据企业id获取站id
	 * @param accountId
	 * @return String[]
	 */
	public String[] getStationByDccInfo(String dccId){
		List<DcsStation> list = dcsStationDao.getStationByDccInfo(dccId);
		String[] stationArr = null;
		if (list.size()!=0) {
			stationArr = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				stationArr[i] = list.get(i).getDcsId().toString();
			}
		}
		return stationArr;
	}
    
    //检查站点ID是否重复
  	public List<DcsStation> check(Map<String, Object> params){
  		return dcsStationDao.check(params);
  	}
  	
    //带站点ID查询
  	public List<DcsStation> selectPic(Map<String, Object> params){
  		return dcsStationDao.selectPic(params);
  	}
    
	//根据条件搜索记录
	public Map<String , Object> getDcsStationList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsStationDao.selectList(queryMap));
		resultMap.put("total", dcsStationDao.selectCount(queryMap));
		return resultMap;
	}
	
	
	//平台首页列表数据结构处理
	public Map<String , Object> getDcsStationListForHome(Map<String, Object> params,String type){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<Map<String , Object>> list = dcsStationDao.selectListForHome(params);
		if("1".equals(type)) {
			resultMap.put("rows", list);
			resultMap.put("total", dcsStationDao.selectCountForHome(queryMap));
		}else {
			BigDecimal day = new BigDecimal(0);
			BigDecimal sum = new BigDecimal(0);
			for (Map<String, Object> map : list) {
				BigDecimal dayPower = new BigDecimal(map.get("20001").toString());
				BigDecimal sumPower = new BigDecimal(map.get("20004").toString());
				day = day.add(dayPower);
				sum = sum.add(sumPower);
			}
			resultMap.put("dayPowerTotal", day);
			resultMap.put("sumPowerTotal", sum);
			resultMap.put("dayCO2Total", day.multiply(new BigDecimal(0.997)).floatValue());
			resultMap.put("sumCO2Total", sum.multiply(new BigDecimal(0.997)).floatValue());
			resultMap.put("dayCoalTotal", day.multiply(new BigDecimal(0.328)).floatValue());
			resultMap.put("sumCoalTotal", sum.multiply(new BigDecimal(0.328)).floatValue());
		}
		return resultMap;
	}
	
	
	//平台首页站点概览数据处理
	public Map<String , Object> getDcsStationIndex(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); 
		Calendar cal_1=Calendar.getInstance();//获取当前日期
		cal_1.add(Calendar.MONTH, -1);
		Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
	    int dayNum=aCalendar.getActualMaximum(Calendar.DATE);//当月天数
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		//当天时间
		String dayTime = DateUtils.getAfterDayDate("0").substring(0,10);
		queryMap.put("dayTime", dayTime);
		queryMap.put("monthTime", dayTime.substring(0, 7));
		queryMap.put("yearTime", dayTime.substring(0, 4));
		Map<String , Object> map = dcsStationDao.getDcsStationIndex(queryMap);
		Map<String , Object> mapDay = dcsStationDao.selForDayTime(queryMap);
		//当月
		queryMap.put("times", DateUtils.getAfterDayDate("0").substring(0,7));
		List<Map<String, Object>> list = dcsStationDao.getStoDcsDayDataList(queryMap);
		List<Map<String, Object>> alarmList = evrAlarmDao.getEvhAlarmListForStationInfo(queryMap);
		//上月
		queryMap.put("times", format.format(cal_1.getTime()).substring(0,7));
		List<Map<String, Object>> list1 = dcsStationDao.getStoDcsDayDataList(queryMap);
		List<Double> generateList=new ArrayList<Double>();
		List<Double> generateList2=new ArrayList<Double>();
		List<String> timeList=new ArrayList<String>();
		for(int i=1;i<=dayNum;i++){
			String times = i<10?"0"+i:i+"";
			timeList.add(times);
			Double num1 = 0.0,num2 = 0.0;
			for(Map<String, Object> map3 : list1) {
				String time = ((String) map3.get("time")).substring(8, 10);
				if(times.equals(time)){
					BigDecimal generate = new BigDecimal(map3.get("generate").toString());
					num1 = generate.doubleValue();
					break;
				}
			}
			generateList2.add(num1);
			for (Map<String, Object> map2 : list) {
				String time = ((String) map2.get("time")).substring(8, 10);
				if(times.equals(time)){
					BigDecimal generate = new BigDecimal(map2.get("generate").toString());
					num2 = generate.doubleValue();
					break;
				}
			}
			generateList.add(num2);
		}
		Double dayPower = 0.0;
		Double monthPower = 0.0;
		Double yearPower = 0.0;
		if(map!=null){
			if(mapDay!=null){
				map.putAll(mapDay);
			}
			if(map.containsKey("dayElec")) {
				dayPower = Double.valueOf(map.get("dayElec").toString());
			}
			if(map.containsKey("monthElec")) {
				monthPower = Double.valueOf(map.get("monthElec").toString());
			}
			if(map.containsKey("yearElec")) {
				yearPower = Double.valueOf(map.get("yearElec").toString());
			}
		}else{
			 map = new HashMap<>(); 
		}
		map.put("dayCO2", dayPower*0.997);		//日co2减排量
		map.put("monthCO2", monthPower*0.997);	//月co2减排量
		map.put("yearCO2", yearPower*0.997);	//年co2减排量
		
		map.put("dayCoal", dayPower*0.328);		//日节煤量
		map.put("monthCoal", monthPower*0.328);	//月节煤量
		map.put("yearCoal", yearPower*0.328);	//年节煤量
		
		resultMap.put("info", map);
		resultMap.put("generateArr", generateList);
		resultMap.put("generateArr2", generateList2);
		resultMap.put("timeArr", timeList);
		resultMap.put("alarmList", alarmList);
		
		params.put("stoStationId", params.get("dcsId"));
		params.put("stoSyncTime", dayTime);
		//查询原始值表负荷
		String tableName = "sto_data_record_" + dayTime.replace("-","");
		//查询分表是否存在
		String table = stoDataRecordDao.selectTableExist(tableName);
		List<StoDataRecord> stoDataRecordList = null;
		if (table!=null){
			params.put("tableName", table);
			stoDataRecordList = stoDataRecordDao.selectLoadListForDcsId(params);
		}
		resultMap.put("todayLoadList",stoDataRecordList);//今日负荷
		//取昨日的负荷
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.DATE, -1);
		Date yesterDay = calendar.getTime();
		String yesterDayTime = DateUtil.format(yesterDay,"yyyy-MM-dd");
		params.put("stoSyncTime", yesterDayTime);
		//查询原始值表负荷
		String yesterDayTableName = "sto_data_record_" + yesterDayTime.replace("-","");
		//查询分表是否存在
		String  yesterDayTable = stoDataRecordDao.selectTableExist(yesterDayTableName);
		List<StoDataRecord> stoDataRecordList2 = null;
		if (yesterDayTable != null){
				params.put("tableName", yesterDayTable);
				stoDataRecordList2 = stoDataRecordDao.selectLoadListForDcsId(params);
		}
		resultMap.put("yesterDayLoadList",stoDataRecordList2);//昨日负荷
		String[] stationIds ={params.get("dcsId").toString()};
		params.put("stationIds", stationIds);
		int countEqu=dcgGatewayDao.selectCount(params);
		resultMap.put("countEqu", countEqu);//接入设备
		params.put("detNodeName", "逆变器");
		int countNBQ = dceEquipmentDao.selForEquTypeNameCount(params);
		resultMap.put("countNBQ", countNBQ);
		//查询正常逆变器
		params.put("delState", "1");
		int countZXNBQ = dceEquipmentDao.selForEquTypeNameAndDelStateRCount(params);
		resultMap.put("countZXNBQ", countZXNBQ);
		//查询总的报警数量
		int countEvrAlarm = evrAlarmDao.selectCountByStation(params);
		resultMap.put("countEvrAlarm", countEvrAlarm);
		
		return resultMap;
	}

	//用户大屏站点概览数据处理
	public Map<String , Object> getDcsStationIndexTouser(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		//当天时间
		String dayTime = DateUtils.getAfterDayDate("0").substring(0,10);
		queryMap.put("dayTime", dayTime);
		queryMap.put("monthTime", dayTime.substring(0, 7));
		queryMap.put("yearTime", dayTime.substring(0, 4));
		Map<String , Object> map = dcsStationDao.getDcsStationIndex(queryMap);
		Map<String , Object> mapDay = dcsStationDao.selForDayTime(queryMap);
		Double dayPower = 0.0;
		Double monthPower = 0.0;
		Double yearPower = 0.0;
		if(mapDay!=null){
			map.putAll(mapDay);
		}
		if(map!=null){
			if(map.containsKey("dayElec")) {
				dayPower = Double.valueOf(map.get("dayElec").toString());
			}
			if(map.containsKey("monthElec")) {
				monthPower = Double.valueOf(map.get("monthElec").toString());
			}
			if(map.containsKey("yearElec")) {
				yearPower = Double.valueOf(map.get("yearElec").toString());
			}
		}else{
			map = new HashMap<>();
		}
		map.put("dayCO2", dayPower*0.997);		//日co2减排量
		map.put("monthCO2", monthPower*0.997);	//月co2减排量
		map.put("yearCO2", yearPower*0.997);	//年co2减排量

		map.put("dayCoal", dayPower*0.328);		//日节煤量
		map.put("monthCoal", monthPower*0.328);	//月节煤量
		map.put("yearCoal", yearPower*0.328);	//年节煤量

		resultMap.put("info", map);

		params.put("stoStationId", params.get("dcsId"));
		params.put("stoSyncTime", dayTime);
		//查询原始值表负荷
		String tableName = "sto_data_record_" + dayTime.replace("-","");
		//查询分表是否存在
		String table = stoDataRecordDao.selectTableExist(tableName);
		List<StoDataRecord> stoDataRecordList = null;
		if (table!=null){
			params.put("tableName", table);
			stoDataRecordList = stoDataRecordDao.selectLoadListForDcsId(params);
		}
		resultMap.put("todayLoadList",stoDataRecordList);//今日负荷

		String[] stationIds ={params.get("dcsId").toString()};
		params.put("stationIds", stationIds);
		params.put("detNodeName", "逆变器");
		int countNBQ = dceEquipmentDao.selForEquTypeNameCount(params);
		resultMap.put("countNBQ", countNBQ);
		//查询正常逆变器
		params.put("delState", "1");
		int countZXNBQ = dceEquipmentDao.selForEquTypeNameAndDelStateRCount(params);
		resultMap.put("countZXNBQ", countZXNBQ);
		//查询总的报警数量
		int countEvrAlarm = evrAlarmDao.selectCountByStation(params);
		resultMap.put("countEvrAlarm", countEvrAlarm);

		return resultMap;
	}
       
	//新增记录
	public boolean addDcsStation(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsStation temp=MapUtil.toObject(DcsStation.class, params);
			dcsStationDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//新增记录---站点
	public void addDcsStationZD(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			//DcsStation temp=MapUtil.toObject(DcsStation.class, params);
			dcsStationDao.insertZD(params);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public boolean updateDcsStation(DcsStation dcsStation){
		try {
			dcsStationDao.update(MapUtil.toObject(DcsStation.class, MapUtil.toMap(dcsStation)));
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
	
	public void updatePic(DcsStation object){
		dcsStationDao.updatePic(object);
	}
	
	public void updateLogo(DcsStation object){
		dcsStationDao.updateLogo(object);
	}
	
	//修改app所需展示的站点图片
	public void updateAppPic(DcsStation object){
		dcsStationDao.updateAppPic(object);
	}
	
	//删除记录
	public int delDcsStation(Map<String, Object> params){
		return dcsStationDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public boolean delDcsStationBat(String[] idArray){
		try {
			for(String pk:idArray){
				dcsStationDao.deleteByPk(pk);
			}
			return true;
		} catch (Exception e) {
			// TODO: handle exception
			return false;
		}
		
	}
	
	public List<DcsStation> getAllDcsStation(Map<String, Object> params) {
		return dcsStationDao.selectList(params);
	}

	/**
	 * 查询所有站点
	 * 
	 * @param ids
	 * @return
	 */
	public List<DcsStation> getAllDcsStationDB(Map<String, Object> params) {
		return dcsStationDao.selectListDB(params);
	}

	// 2017-11-22绑定站点功能重做 开始
	/**
	 * 根据accountId查询对应qt_account_station表中绑定stationId的站点
	 * 
	 * <AUTHOR>
	 * @date 2017年11月2日上午10:05:39
	 * @param
	 * @return
	 */
	public List<DcsStation> getToStationChecked(Map<String, Object> params) {
		return dcsStationDao.getToStationChecked(params);
	}

	/**
	 * 查询所有站点，其中根据accountId查询对应qt_account_station表中绑定stationId的站点除外
	 * 
	 * <AUTHOR>
	 * @date 2017年11月1日上午11:07:50
	 * @param
	 * @return
	 */
	public List<DcsStation> getToStationNoChecked(Map<String, Object> params) {
		return dcsStationDao.getToStationNoChecked(params);
	}

	/**
	 * 根据前端右边‘已选站点’框子里的站点，将这类站点除外剩下的站点中模糊筛选
	 * 
	 * <AUTHOR>
	 * @date 2017年11月2日上午10:56:13
	 * @param
	 * @return
	 */
	public List<DcsStation> getTheStationInNoChecked(Map<String, Object> params) {
		return dcsStationDao.getTheStationInNoChecked(params);
	}
	// 2017-11-22绑定站点功能重做 结束

	public DcsStation getById(String id) {
		DcsStation selectByPk = dcsStationDao.selectByPk(id);
		return selectByPk;
	}

	/**
	 * 根据数组查询站点
	 * @param ids
	 * @return
	 */
	public List<DcsStation> getByIds(String ids[]){
		List<DcsStation> list = new ArrayList<DcsStation>();
		for(String id : ids){
			DcsStation selectByPk = dcsStationDao.selectByPk(id);
			if(selectByPk != null) {
				list.add(selectByPk);
			}
		}
		return list;
	}
	
	public List<DcsStation> getByIds2(Map<String, Object> params){
		String [] ids=(String[]) params.get("ids");
		String type=null;
		if(params.get("type")!=null){
		    type=params.get("type").toString();
		}
		 
		List<DcsStation> list = new ArrayList<DcsStation>();
		for(String id : ids){
			Map<String, Object> params2=new HashMap<String, Object>();
			params2.put("id",id);
			params2.put("type",type);
			List<DcsStation> selectByPk = dcsStationDao.selectByPk2(params2);
			if(selectByPk!=null && selectByPk.size()>=1){
				list.addAll(selectByPk);
			}
			
		}
		return list;
	}
	
	public List<DcsStation> getByIds3(Map<String, Object> params){
		String [] ids=(String[]) params.get("ids");
		String type=null;
		if(params.get("type")!=null){
		    type=params.get("type").toString();
		}
		 
		List<DcsStation> list = new ArrayList<DcsStation>();
		for(String id : ids){
			Map<String, Object> params2=new HashMap<String, Object>();
			params2.put("id",id);
			params2.put("type",type);
			List<DcsStation> selectByPk = dcsStationDao.selectByPk4(params2);
			if(selectByPk!=null && selectByPk.size()>=1){
				list.addAll(selectByPk);
			}
			
		}
		return list;
	}
	
	/**
	 * 根据账户id获取站id
	 * @param accountId
	 * @return String[]
	 */
	public String[] getStationByAccount(String accountId){
		String stations = dcsStationDao.getStationByAccount(accountId);
		String[] stationArr = null;
		if(stations != null){
			stationArr = stations.split(",");
		}
		return stationArr;
	}
	
    public void insertList(List<DcsStation> list){
	    	dcsStationDao.insertList(list);
	    }
    
    public int selectCountCX(Map<String,Object> params){
    	return dcsStationDao.selectCountCX(params);
    }
    
    /*public List<Map<String,Object>> selectBsaCodeByTypeId(Map<String,Object> params){
    	return bsaCodeDao.selectBsaCodeByTypeId(params);
    }*/
    
    public QtTableConfigure getSelectedColumns(Map<String,Object> params){
    	return configureDao.selectConfigureCode(params);
    }
    
    public void updateSelectedColumns(Map<String,Object> params){
    	configureDao.upadteConfigureCode(params);
    }
    
    public  List<Map<String,Object>> getDcsStationsList(Map<String,Object> params){
    	Map<String,Object> paramTmp = new HashMap<String, Object>();
    	paramTmp.put("module", "/onlineMonitor/**");
    	QtTableConfigure configure = getSelectedColumns(paramTmp);
    	if(configure != null && StringUtils.isNotBlank(configure.getCodeConfigure())) {
    		params.put("stoGfCode", "("+configure.getCodeConfigure()+")");
    	}
    	return dcsStationDao.getDcsStationsList(params);
    }
    
    public Map<String,Object> getDefaultProvince(Map<String,Object> params){
    	return dcsStationDao.getDefaultProvince(params);
    }
    
    public List<Map<String,Object>> getAllDcsStations(Map<String,Object> params){
    	return dcsStationDao.getAllDcsStations(params);
    }
    
    public void updateWeatherStationId(Map<String,Object> params){
    	 dcsStationDao.updateWeatherStationId(params);
    }
    
    /**
	 * 站点概览表格数据
	 * @param params
	 * @return
	 */
	public Map<String ,Object> selSiteOverviewTabData(Map<String, Object> params){
		Map<String ,Object> result = new HashMap<>();
		result.put("rows",dcsStationDao.selSiteOverviewTabData(params));
		result.put("total",dcsStationDao.selSiteOverviewTabDataCount(params));
		return result;
	}


	public int delStationFollow(Map<String,Object> params){
		return dcsStationDao.delStationFollow(params);
	}

	public int selStationCount(Map<String,Object> params){return dcsStationDao.selectCount(params);}

	public String getContactByAccount(String stationId) {

		return dcsStationDao.getContactByAccount(stationId);
	}



	public void delStationFolloww(DcsStation object) {
		dcsStationDao.delStationFolloww(object);
	}

	public void QuerySiteInformation(DcsStation object) {
		dcsStationDao.QuerySiteInformation(object);
	}



	public void SelectWisdomOperator(Map<String, Object> params) {
		dcsStationDao.SelectWisdomOperator(params);
	}

	public void Smartreport(Map<String, Object> params) {
		dcsStationDao.Smartreport(params);
	}



	public int insertStationFollow(DcsStation station) {
		return dcsStationDao.insertStationFollow(station);
	}

	public Map<String, Object> getStationByAccountt(DcsStation object) {
		return dcsStationDao.getStationByAccountt(object);
	}

	public void UserPowerStationModification(Map<String, Object> params) {
		dcsStationDao.UserPowerStationModification(params);
	}

	public void isnertwisdomOperator(Map<String, Object> params) {
		dcsStationDao.isnertwisdomOperator(params);
	}



	public List<Map<String,Object>> SELUserPowerStationModification(Map<String, Object> params) {
		return dcsStationDao.SELUserPowerStationModification(params);
	}


	public List<Map<String,Object>> AlarmList(Map<String, Object> params) {
		return 	dcsStationDao.AlarmList(params);
	}

	public List<Map<String,Object>>  StationFollowList(Map<String, Object> params) {
		return dcsStationDao.StationFollowList(params);
	}

	public List<Map<String,Object>> yueSmartreportShang(Map<String, Object> params) {
		return dcsStationDao.yueSmartreportShang(params);
	}
	public List<Map<String,Object>> yueSmartreportXia(Map<String, Object> params) {
		return dcsStationDao.yueSmartreportXia(params);
	}

	public List<Map<String,Object>> NianSmartreportShang(Map<String, Object> params) {
		return dcsStationDao.NianSmartreportShang(params);
	}
	public List<Map<String,Object>> NianSmartreportXia(Map<String, Object> params) {
		return dcsStationDao.NianSmartreportXia(params);
	}

	public List<Map<String,Object>> zongSmartreport(Map<String, Object> params) {
		return dcsStationDao.zongSmartreport(params);
	}

	public  String  panDuan(String params) {
		return dcsStationDao.panDuan(params);
	}

	public String gaoPanDuan2(String params) {
		return dcsStationDao.gaoPanDuan2(params);
	}

	public Object panDuan3(String stationId) {
		return dcsStationDao.panDuan3(stationId);
	}

	public Object RY(Map<String, Object> params) {
		return dcsStationDao.RY(params);
	}

	public List<Map<String, Object>> PowerGenerationStatisticsList(Map<String, Object> params) {
		return dcsStationDao.PowerGenerationStatisticsList(params);
	}

	public List<Map<String, Object>> PowerGenerationStatistics(Map<String, Object> params) {
		return dcsStationDao.FNian(params);
	}
}
