package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DccArea;
import com.qt.repository.file.DccAreaDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-04 02:28:44
 */
@Service
public class DccAreaService{

	private Logger logger = LoggerFactory.getLogger(DccAreaService.class);
     
    @Resource
	private DccAreaDao dccAreaDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDccAreaList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dccAreaDao.selectList(queryMap));
		resultMap.put("total", dccAreaDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDccArea(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DccArea temp=MapUtil.toObject(DccArea.class, params);
			dccAreaDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDccArea(DccArea dccArea){
		dccAreaDao.update(dccArea);
	}
	
	//删除记录
	public int delDccArea(Map<String, Object> params){
		return dccAreaDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDccAreaBat(String[] idArray){
		for(String pk:idArray){
			dccAreaDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DccArea> getAllDccArea(Map<String, Object> params){
		return dccAreaDao.selectList(params);
	}
}
