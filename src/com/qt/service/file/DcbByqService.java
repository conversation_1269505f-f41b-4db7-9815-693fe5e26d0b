package com.qt.service.file;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DcbByq;
import com.qt.repository.file.DcbByqDao;
@Service
public class DcbByqService {
	private Logger logger = LoggerFactory.getLogger(DcbByqService.class);
    
    @Resource
	private DcbByqDao dcbByqDao;
    
	public List<DcbByq> getAllByq(Map<String, Object> params){
		return dcbByqDao.selectListzj(params);
	}
	
	
	//根据条件搜索记录
	public Map<String , Object> getDcbByqList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcbByqDao.selectList(queryMap));
		resultMap.put("total", dcbByqDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDcbByq(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcbByq temp=MapUtil.toObject(DcbByq.class, params);
			dcbByqDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcbByq(DcbByq form){
		dcbByqDao.update(form);
	}
	
	//根据主键批量删除
	public int delDcbByqBat(String[] idArray){
		for(String pk:idArray){
			dcbByqDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	//根据站点Id删除变压器
	public int delDcbByStationId(String[] idArray){
			//删除
			dcbByqDao.deleteByStationId(idArray);
		return idArray.length;
	}
	
	public List<DcbByq> getAllDcbByq(Map<String, Object> params){
		return dcbByqDao.selectList(params);
	}
	
	public DcbByq getById(String id){
		DcbByq selectByPk = dcbByqDao.selectByPk(id);
		return selectByPk;
	}
	
	public DcbByq selectByDcsId(String dcbDcsId){
		DcbByq selectByPk = dcbByqDao.selectByDcsId(dcbDcsId);
		return selectByPk;
	}
	
	//根据站点查询下属变压器
	public List<DcbByq> selectDcsId(Map<String, Object> params){
		List<DcbByq> list = dcbByqDao.selectDcsId(params);
		return list;
	}
	
	/**
	 * 根据数组查询站点
	 * @param ids
	 * @return
	 */
	public List<DcbByq> getByIds(String ids[]){
		List<DcbByq> list = new ArrayList<DcbByq>();
		for(String id : ids){
			DcbByq selectByPk = dcbByqDao.selectByPk(id);
			list.add(selectByPk);
		}
		return list;
	}
	
	
	
	 public void insertList(List<DcbByq> list){
  	   dcbByqDao.insertList(list);
	 }



}
