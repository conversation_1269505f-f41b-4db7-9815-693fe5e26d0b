package com.qt.service.file;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DcsOtherInfo;
import com.qt.repository.file.DcsOtherInfoDao;

@Service
public class DcsOtherInfoService {
	
	private Logger logger = LoggerFactory.getLogger(DcsOtherInfoService.class);
	
	@Resource
	private DcsOtherInfoDao dcsOtherInfoDao;
	
	
	    
		//根据条件搜索记录
		public Map<String , Object> getDcsOtherInfoList(Map<String, Object> params){
			Map<String , Object> queryMap = new HashMap<String, Object>();
			Map<String , Object> resultMap = new HashMap<String, Object>();
			//TODO 根据需要封装查询需要的条件
			queryMap.putAll(params);
			resultMap.put("rows", dcsOtherInfoDao.selectList(queryMap));
			resultMap.put("total", dcsOtherInfoDao.selectCount(queryMap));
			return resultMap;
		}
	       
		//新增记录
		public void addDcsOtherInfo(Map<String, Object> params){
			try {
				//TODO 根据需要封装查询需要的条件
				DcsOtherInfo temp=MapUtil.toObject(DcsOtherInfo.class, params);
				dcsOtherInfoDao.insert(temp);
			} catch (Exception e) {
				logger.error("添加失败",e);
			}
		}
		
		//更新记录
		public void updateDcsOtherInfo(DcsOtherInfo DcsOtherInfo){
			dcsOtherInfoDao.update(DcsOtherInfo);
		}
		
		//删除记录
		/*public int delDcsOtherInfo(Map<String, Object> params){
			return dcsOtherInfoDao.deleteByMap(params);
		}*/
		
		//根据主键批量删除
		public int delDcsOtherInfoBat(String[] idArray){
			for(String pk:idArray){
				dcsOtherInfoDao.deleteByPk(Integer.parseInt(pk));
			}
			return idArray.length;
		}
		
		public List<DcsOtherInfo> getAllDcsOtherInfo(Map<String, Object> params){
			return dcsOtherInfoDao.selectList(params);
		}
		

		
		public DcsOtherInfo getByPk(Integer id){
			DcsOtherInfo selectByPk = dcsOtherInfoDao.selectByPk(id);
			return selectByPk;
		}
		
		
		/**
		 * 根据数组查询站点
		 */
		public List<DcsOtherInfo> getByIds(String ids[]){
			List<DcsOtherInfo> list = new ArrayList<DcsOtherInfo>();
			for(String id : ids){
				DcsOtherInfo selectByPk = dcsOtherInfoDao.selectByPk(Integer.parseInt(id));
				list.add(selectByPk);
			}
			return list;
		}
		
		
	    public void insertList(List<DcsOtherInfo> list){
		    	dcsOtherInfoDao.insertList(list);
		    }
	    

}
