package com.qt.service.file;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DclLine;
import com.qt.entity.file.DcsStation;
import com.qt.repository.file.DclLineDao;
import com.qt.repository.file.DcsStationDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-10 09:49:39
 */
@Service
public class DclLineService{

	private Logger logger = LoggerFactory.getLogger(DclLineService.class);
     
    @Resource
	private DclLineDao dclLineDao;
    
    @Resource
	private DcsStationDao dcsStationDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDclLineList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		
		resultMap.put("rows", dclLineDao.selectList(queryMap));
		resultMap.put("total", dclLineDao.selectCount(queryMap));
		return resultMap;
	}
	
	public Map<String , Object> getDclLineListByDclType(Map<String, Object> params){
		Map<String , Object> resultMap = new HashMap<String, Object>();
		dclLineDao.selectList(params);
		return resultMap;
	}
	
	
	//根据站点和回路ID查询
	public List<DclLine> check(Map<String, Object> params){
		return dclLineDao.check(params);
	}
	
	//根据站点和回路ID查询
	public List<DclLine> check2(Map<String, Object> params){
		return dclLineDao.check2(params);
	}
	
		//根据站点和变压器ID查询回路返回回路ID的list
	public List<String> selectDclId(Map<String, Object> params){
		List<DclLine> dclLines = dclLineDao.selectDclId(params);
		List<String> list = new ArrayList<String>();
		if (dclLines.size()>0) {
			for (DclLine dclLine : dclLines) {
				list.add(dclLine.getDclId().toString());
			}
		}else{
			list = null;
		}
		return list;
	}
	
	//根据站点ID查询
		public Map<String, Object> selectByDcsId(Map<String, Object> params){
			Map<String , Object> queryMap = new HashMap<String, Object>();
			Map<String , Object> resultMap = new HashMap<String, Object>();
			queryMap.putAll(params);
			List<DclLine> list=dclLineDao.selectByDcsId(queryMap);
			if(list!=null && list.size()>0){
				resultMap.put("total", list.size());
			}else{
				resultMap.put("total", 0);
			}
			resultMap.put("rows", list);
			
			return resultMap;
		}
	
	//根据条件搜索记录
	public Map<String , Object> getDclLineListLB(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		
		resultMap.put("rows", dclLineDao.selectListLB(queryMap));
		resultMap.put("total", dclLineDao.selectCount(queryMap));
		return resultMap;
	}
	
	//根据条件搜索记录
		public Map<String , Object> getDclLineListLB2(Map<String, Object> params){
			Map<String , Object> queryMap = new HashMap<String, Object>();
			Map<String , Object> resultMap = new HashMap<String, Object>();
			//TODO 根据需要封装查询需要的条件
			queryMap.putAll(params);
			
			resultMap.put("rows", dclLineDao.selectListLB2(queryMap));
			resultMap.put("total", dclLineDao.selectCount2(queryMap));
			return resultMap;
		}
		
		//根据条件搜索记录
	public Map<String , Object> getDclLineListLB3(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		
		resultMap.put("rows", dclLineDao.selectListLB3(queryMap));
		resultMap.put("total", dclLineDao.selectCount2(queryMap));
		return resultMap;
	}   
	
	/**
	 * 根据企业id获取站id
	 * @param accountId
	 * @return String[]
	 */
	public String[] getStationByDccInfo(String dccId){
		List<DcsStation> list = dcsStationDao.getStationByDccInfo(dccId);
		String[] stationArr = null;
		if (list.size()!=0) {
			stationArr = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				stationArr[i] = list.get(i).getDcsId().toString();
			}
		}
		return stationArr;
	}
       
	//新增记录
	public void addDclLine(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DclLine temp=MapUtil.toObject(DclLine.class, params);
//			temp.setId(SerialNo.getUNID());
			dclLineDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDclLine(DclLine dclLine){
		dclLineDao.update(dclLine);
	}
	
	//删除记录
	public int delDclLine(Map<String, Object> params){
		return dclLineDao.deleteByMap(params);
	}
	
	//根据站点删除
	public int delDclLineByStationId(String[] idArray){
		return dclLineDao.deleteByStationId(idArray);
	}

	//根据主键批量删除
	public int delDclLineBat(String[] idArray){
		for(String pk:idArray){
			dclLineDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DclLine> getAllDclLine(Map<String, Object> params){
		return dclLineDao.selectList(params);
	}
	
	public List<DclLine> getAllDclLineLB(Map<String, Object> params){
		return dclLineDao.selectListLB(params);
	}
	
	public List<DclLine> getAllDclLineDB(Map<String, Object> params){
		return dclLineDao.selectListDB(params);
	}
	
	
	public DclLine getByIndex(String id){
		DclLine selectByIndex = dclLineDao.selectByIndex(id);
		return selectByIndex;
	}
	
	public Map<String , Object> countDclLine(Map<String, Object> params){
		Map<String , Object> resultMap = new HashMap<String, Object>();
		List<Map<String,Object>> mapCompany = dclLineDao.countDclLineByCompany(params);
		List<Map<String,Object>> mapCapacity = dclLineDao.countDclLineByCapacity(params);
		List<Map<String,Object>> mapCompany1 = dclLineDao.countDclLineByCompany1(params);
		List<Map<String,Object>> mapCapacity1 = dclLineDao.countDclLineByCapacity1(params);
		//这里需要对mapCompany1按照capacity排序
		List<Map<String,Object>> mapCompanyList = new ArrayList<>();
		if(mapCompany1.size()>0) {
			Map<String, List<Map<String,Object>>> company1Map = new HashMap<>();
			for (Map<String,Object> map :mapCompany1) {
				List<Map<String,Object>> list = company1Map.get(map.get("capacity").toString());
				if(list == null) {
					list = new ArrayList<Map<String,Object>>();
					list.add(map);
					company1Map.put(map.get("capacity").toString(), list);
				}else {
					list.add(map);
				}
			}
		
			for(String capacity : company1Map.keySet()){
				Map<String,Object> map = new HashMap<String, Object>();
				map.put("capacity", capacity);
				int totalNum = 0;
				List<Map<String,Object>> list = company1Map.get(capacity);
				for (int i = 0; i < list.size(); i++) {
					totalNum += Integer.parseInt(list.get(i).get("num").toString());
				}
				map.put("num", totalNum);
				map.put("info", list);
				mapCompanyList.add(map);
	        }
		}
		resultMap.put("countByCompany", mapCompany);
		resultMap.put("countByCapacity", mapCapacity);
		resultMap.put("countByCompany1", mapCompanyList);
		resultMap.put("countByCapacity1", mapCapacity1);
		return resultMap;
	}
	
	public Map<String , Object> getDclLineByCompanyAndType(Map<String, Object> params){
		Map<String, Object> resultMap = new HashMap<>();
		Map<String , Object> paramtMap = new HashMap<String, Object>();
		List<Map<String,Object>> mapCompany = dclLineDao.selectListByDclType(params);
		String dclType = (String) params.get("dclType");
		if(mapCompany.size()>0) {
			JSONArray array = new JSONArray();
			for (int i = 0; i < mapCompany.size(); i++) {
				Map<String,Object> result = mapCompany.get(i);
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("dcsName", result.get("name"));
				jsonObject.put("dclTypeName", result.get("company").toString()+result.get("capacity").toString());
				jsonObject.put("dclNum", result.get("num"));
				paramtMap.put("dclType", dclType);
				paramtMap.put("dclDeviceCompany", result.get("company").toString());
				paramtMap.put("dclDcsId", result.get("dcsId"));
				paramtMap.put("dclCapacity", result.get("capacity"));
				//获取满足要求的dclLineList
				List<DclLine> list = dclLineDao.selectListByDclType1(paramtMap);
				jsonObject.put("dclInfo", list);
				array.add(jsonObject);
			}
			
			//对数据按照站点进行分组
			for (Object object : array) {
				JSONObject object2 = (JSONObject)object;
				JSONArray arrayt = (JSONArray) resultMap.get(object2.getString("dcsName"));
				if(arrayt == null) {
					arrayt = new JSONArray();
					arrayt.add(object2);
					resultMap.put(object2.getString("dcsName"), arrayt);
				}else {
					arrayt.add(object2);
				}
			}
			
		}
		return resultMap;
	}

	public int selectDclLineForCount(Map<String, Object> checkParam) {
		return dclLineDao.selectDclLineForCount(checkParam);
	}
	
	public int selectDclLineForCount1(Map<String, Object> checkParam) {
		return dclLineDao.selectDclLineForCount1(checkParam);
	}
	
	public List<Map<String,Object>> getAllInverters(Map<String, Object> param){
		List<Map<String,Object>> resultList = new ArrayList<Map<String,Object>>();
		List<Map<String,Object>> list  = dclLineDao.getAllInverters(param);
		//根据站点分组
		Map<String, List<Map<String,Object>>> inverters = new HashMap<>();
		if(!list.isEmpty()) {
			for (Map<String, Object> map : list) {
				List<Map<String,Object>> listTmp = inverters.get(map.get("dcsName").toString());
				if(listTmp == null) {
					listTmp = new ArrayList<Map<String,Object>>();
					listTmp.add(map);
					inverters.put(map.get("dcsName").toString(), listTmp);
				}else {
					listTmp.add(map);
				}
			}
			for(String dcsName : inverters.keySet()){
				Map<String,Object> map = new HashMap<String, Object>();
				map.put("dcsName", dcsName);
				List<Map<String,Object>> listtmp = inverters.get(dcsName);
				map.put("num", listtmp.size());
				map.put("info", listtmp);
				resultList.add(map);
	        }
		}
		return resultList;
	}

	public int selLineCount(Map<String,Object> map){return dclLineDao.selectCount2(map);}
}
