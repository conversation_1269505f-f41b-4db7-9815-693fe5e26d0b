package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.entity.statistics.DlUserReport;
import com.qt.repository.statistics.DlUserReportDao;

@Service
public class DlUserReportService {
	private Logger logger = LoggerFactory.getLogger(DccAreaService.class);
	@Resource
	private DlUserReportDao dlUserReportDao;
	
	public int insertDlUserReport(DlUserReport dlUserReport){
		return dlUserReportDao.insertList(dlUserReport);
	}
	
	public List<DlUserReport> selectByDlUserReport(DlUserReport dlUserReport){
		return dlUserReportDao.selectByDlUserReport(dlUserReport);
	}

	public List<DlUserReport> selectAllDlUserReport(){
		return dlUserReportDao.selectAllDlUserReport();
	}
	
	//根据条件搜索记录
	public Map<String , Object> list(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dlUserReportDao.selectList(queryMap));
		resultMap.put("total", dlUserReportDao.selectCount(queryMap));
		return resultMap;
	}
}
