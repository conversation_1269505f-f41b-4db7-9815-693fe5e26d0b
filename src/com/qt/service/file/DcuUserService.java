package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DcuUser;
import com.qt.repository.file.DcuUserDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-06 09:49:20
 */
@Service
public class DcuUserService{

	private Logger logger = LoggerFactory.getLogger(DcuUserService.class);
     
    @Resource
	private DcuUserDao dcuUserDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcuUserList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcuUserDao.selectList(queryMap));
		resultMap.put("total", dcuUserDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录--用电人员
	public void addDcuUserRY(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcuUser temp=MapUtil.toObject(DcuUser.class, params);
			dcuUserDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//新增记录
		public void addDcuUser(Map<String, Object> params){
			try {
				//TODO 根据需要封装查询需要的条件
				DcuUser temp=MapUtil.toObject(DcuUser.class, params);
				dcuUserDao.insert(temp);
			} catch (Exception e) {
				logger.error("添加失败",e);
			}
		}
	
	//更新记录
	public void updateDcuUser(DcuUser dcuUser){
		dcuUserDao.update(dcuUser);
	}
	
	//删除记录
	public int delDcuUser(Map<String, Object> params){
		return dcuUserDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcuUserBat(String[] idArray){
		for(String pk:idArray){
			dcuUserDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DcuUser> getAllDcuUser(Map<String, Object> params){
		return dcuUserDao.selectList(params);
	}
	
}
