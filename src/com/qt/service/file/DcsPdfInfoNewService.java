package com.qt.service.file;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.file.DcsPdfInfoNew;
import com.qt.repository.file.DcsPdfInfoNewDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2016-08-10 09:49:39
 */
@Service
public class DcsPdfInfoNewService{

	private Logger logger = LoggerFactory.getLogger(DcsPdfInfoNewService.class);
     
    @Resource
	private DcsPdfInfoNewDao dcsPdfInfoNewDao;
	
	//根据条件搜索记录
	public Map<String , Object> list(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		
		resultMap.put("rows", dcsPdfInfoNewDao.selectList(queryMap));
		resultMap.put("total", dcsPdfInfoNewDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void add(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsPdfInfoNew temp=MapUtil.toObject(DcsPdfInfoNew.class, params);
//			temp.setId(SerialNo.getUNID());
			dcsPdfInfoNewDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void update(DcsPdfInfoNew dcsPdfInfo){
		dcsPdfInfoNewDao.update(dcsPdfInfo);
	}
	
	//更新记录
	public List<DcsPdfInfoNew> listExl(Map<String,Object> params){
		return dcsPdfInfoNewDao.selectList(params);
	}
	
	//根据主键批量删除
	public int del(String[] idArray){
		for(String pk:idArray){
			dcsPdfInfoNewDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
}
