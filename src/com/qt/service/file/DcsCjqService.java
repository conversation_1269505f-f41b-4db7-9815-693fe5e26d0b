package com.qt.service.file;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.file.DcsCjqDao;
import com.qt.entity.file.DcsCjq;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2020-01-20 09:32:25
 */
@Service
public class DcsCjqService{

	private Logger logger = LoggerFactory.getLogger(DcsCjqService.class);
     
    @Resource
	private DcsCjqDao dcsCjqDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcsCjqList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsCjqDao.selectList(queryMap));
		resultMap.put("total", dcsCjqDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDcsCjq(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsCjq temp=MapUtil.toObject(DcsCjq.class, params);
			dcsCjqDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcsCjq(DcsCjq dcsCjq){
		dcsCjqDao.update(dcsCjq);
	}
	
	//删除记录
	public int delDcsCjq(Map<String, Object> params){
		return dcsCjqDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcsCjqBat(String[] idArray){
		for(String pk:idArray){
			dcsCjqDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DcsCjq> getAllDcsCjq(Map<String, Object> params){
		return dcsCjqDao.selectList(params);
	}
	
}
