package com.qt.service.file;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.file.DcsWeatherStationDao;
import com.qt.entity.file.DcsStation;
import com.qt.entity.file.DcsWeatherStation;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2020-01-16 11:35:16
 */
@Service
public class DcsWeatherStationService{

	private Logger logger = LoggerFactory.getLogger(DcsWeatherStationService.class);
     
    @Resource
	private DcsWeatherStationDao dcsWeatherStationDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcsWeatherStationList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsWeatherStationDao.selectList(queryMap));
		resultMap.put("total", dcsWeatherStationDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDcsWeatherStation(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsWeatherStation temp=MapUtil.toObject(DcsWeatherStation.class, params);
			dcsWeatherStationDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcsWeatherStation(DcsWeatherStation dcsWeatherStation){
		dcsWeatherStationDao.update(dcsWeatherStation);
	}
	
	//删除记录
	public int delDcsWeatherStation(Map<String, Object> params){
		return dcsWeatherStationDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcsWeatherStationBat(String[] idArray){
		for(String pk:idArray){
			dcsWeatherStationDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DcsWeatherStation> getAllDcsWeatherStation(Map<String, Object> params){
		return dcsWeatherStationDao.selectList(params);
	}
	
	public DcsWeatherStation getById(String id) {
		DcsWeatherStation selectByPk = dcsWeatherStationDao.selectByPk(id);
		return selectByPk;
	}
	
}
