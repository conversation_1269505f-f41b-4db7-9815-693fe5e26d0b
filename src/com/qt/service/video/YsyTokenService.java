package com.qt.service.video;

import com.qt.common.utils.MapUtil;
import com.qt.common.utils.ReadPropertyUtil;
import com.qt.controller.video.ys7API;
import com.qt.entity.video.YsyToken;
import com.qt.repository.video.YsyTokenDao;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2017年12月4日上午10:11:23
 * @instruction 
 */
@Service
public class YsyTokenService {

	private Logger logger = LoggerFactory.getLogger(YsyTokenService.class);
     
    @Resource
	private YsyTokenDao ysyTokenDao;
    
    @Resource
    private ys7API ys7API;
	
    //萤石云token过期，调用接口获取最新并新增记录
  	public Map<String, Object> addYsyToken(){
		Map<String, Object> result = new HashMap<String, Object>();
		try{
			//根据配置文件ys7API.properties查询相关数据
	    	ReadPropertyUtil readPropertyutil=new ReadPropertyUtil();
	    	Map<String,String> properties=readPropertyutil.getProperty("/ys7API.properties");
	    	String appKey = "appKey="+ URLEncoder.encode(properties.get("appKey"), "utf-8");
		    String appSecret = "&appSecret="+ URLEncoder.encode(properties.get("appSecret"), "utf-8"); 
			String url=properties.get("tokenUrl");
			String param =appKey+appSecret;
			result =ys7API.ys7API(param, url, "other");
			if(("200").equals(result.get("code"))){//获取token接口调用成功后，将最新token增添加到数据库表ysy_token中
				JSONObject jsonObject =  JSONObject.fromObject(result.get("data").toString());
				String accessToken=jsonObject.getString("accessToken");
				Long expireTime=jsonObject.getLong("expireTime");
				Map<String,Object> ysyToken=new HashMap<String, Object>();
				ysyToken.put("accessToken", accessToken);
				ysyToken.put("expireTime", new Date(expireTime));
				ysyToken.put("updateTime", new Date());
				/*DateUtil dateUtil=new DateUtil();
				ysyToken.put("expireTime", dateUtil.format(new  Date(expireTime), "yyyy-MM-dd HH:mm:ss"));
				ysyToken.put("updateTime", dateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));*/
				YsyToken temp= MapUtil.toObject(YsyToken.class, ysyToken);
				ysyTokenDao.insert(temp);
			}
	    }catch (Exception e ) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	    return 	result;   
  	}
    
	/*//新增记录
	public void addYsyToken(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			YsyToken temp=MapUtil.toObject(YsyToken.class, params);
			ysyTokenDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}*/
	
	public YsyToken selectLastOne(){
		YsyToken lastOne = ysyTokenDao.selectLastOne();
		return lastOne;
	}
	
 
}
