package com.qt.service.devAccount;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.devAccount.DevAccountTypeDao;
import com.qt.entity.devAccount.DevAccountType;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-06-22 04:15:56
 */
@Service
public class DevAccountTypeService{

	private Logger logger = LoggerFactory.getLogger(DevAccountTypeService.class);
     
    @Resource
	private DevAccountTypeDao devAccountTypeDao;
	
    public List<DevAccountType> selectAllType(Map<String,Object> params){
    	return devAccountTypeDao.selectAll(params);
    }
    
	//根据条件搜索记录
	public Map<String , Object> getDevAccountTypeList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devAccountTypeDao.selectList(queryMap));
		resultMap.put("total", devAccountTypeDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public boolean addDevAccountType(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevAccountType temp=MapUtil.toObject(DevAccountType.class, params);
			temp.setDetNodeId(UUID.randomUUID().toString().replaceAll("-",""));
			devAccountTypeDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//更新记录
	public boolean updateDevAccountType(DevAccountType devAccountType){
		try {
			//TODO 根据需要封装查询需要的条件
			devAccountTypeDao.update(devAccountType);
			return true;
		} catch (Exception e) {
			logger.error("修改失败",e);
			return false;
		}
	}
	
	//删除记录
	public int delDevAccountType(Map<String, Object> params){
		return devAccountTypeDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDevAccountTypeBat(String[] idArray){
		for(String pk:idArray){
			devAccountTypeDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	//根据主键删除
	public int delDevAccountTypeId(String id){
		return devAccountTypeDao.deleteByPk(id);
	}
	
	public List<DevAccountType> getAllDevAccountType(Map<String, Object> params){
		return devAccountTypeDao.selectList(params);
	}

	public DevAccountType getOneByNodeId(String id){
		return devAccountTypeDao.selectByPk(id);
	}
	
	public DevAccountType getDevAccountTypeInfos(String id){
		return devAccountTypeDao.selectDevAccountTypeInfos(id);
	}
}
