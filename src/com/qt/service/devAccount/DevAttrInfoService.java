package com.qt.service.devAccount;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.devAccount.DevAttrInfoDao;
import com.qt.entity.devAccount.DevAttrInfo;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-06-25 01:30:17
 */
@Service
public class DevAttrInfoService{

	private Logger logger = LoggerFactory.getLogger(DevAttrInfoService.class);
     
    @Resource
	private DevAttrInfoDao devAttrInfoDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDevAttrInfoList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devAttrInfoDao.selectList(queryMap));
		resultMap.put("total", devAttrInfoDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public boolean addDevAttrInfo(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevAttrInfo temp=MapUtil.toObject(DevAttrInfo.class, params);
			devAttrInfoDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	public boolean addDevAttrInfoList(List<DevAttrInfo> list){
		try {
			//TODO 根据需要封装查询需要的条件
			devAttrInfoDao.insertList(list);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//更新记录
	public void updateDevAttrInfo(DevAttrInfo devAttrInfo){
		devAttrInfoDao.update(devAttrInfo);
	}
	
	//删除记录
	public int delDevAttrInfo(Map<String, Object> params){
		return devAttrInfoDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDevAttrInfoBat(String[] idArray){
		for(String pk:idArray){
			devAttrInfoDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DevAttrInfo> getAllDevAttrInfo(Map<String, Object> params){
		return devAttrInfoDao.selectList(params);
	}
	
}
