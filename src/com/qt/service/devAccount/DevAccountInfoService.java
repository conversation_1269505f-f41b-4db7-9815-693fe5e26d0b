package com.qt.service.devAccount;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.devAccount.DevAccountInfoDao;
import com.qt.entity.devAccount.DevAccountInfo;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-06-22 04:16:39
 */
@Service
public class DevAccountInfoService{

	private Logger logger = LoggerFactory.getLogger(DevAccountInfoService.class);
     
    @Resource
	private DevAccountInfoDao devAccountInfoDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDevAccountInfoList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devAccountInfoDao.selectList(queryMap));
		resultMap.put("total", devAccountInfoDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public boolean addDevAccountInfo(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevAccountInfo temp=MapUtil.toObject(DevAccountInfo.class, params);
			devAccountInfoDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	public boolean addDevAccountInfoList(List<DevAccountInfo> list){
		try {
			devAccountInfoDao.insertList(list);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//更新记录
	public boolean updateDevAccountInfo(DevAccountInfo devAccountInfo){
		try {
			devAccountInfoDao.update(devAccountInfo);
			return true;
		} catch (Exception e) {
			logger.error("修改失败",e);
			return false;
		}
	}
	
	//删除记录
	public int delDevAccountInfo(Map<String, Object> params){
		return devAccountInfoDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDevAccountInfoBat(String[] idArray){
		for(String pk:idArray){
			devAccountInfoDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	//根据主键批量删除
		public int delDevAccountInfoPk(String pk){
			return devAccountInfoDao.deleteByPk(pk);
		}
		//根据节点删除
		public int delDevAccountInfoId(String id){
			return devAccountInfoDao.deleteById(id);
		}
	public List<DevAccountInfo> getAllDevAccountInfo(Map<String, Object> params){
		return devAccountInfoDao.selectList(params);
	}
	
	public List<DevAccountInfo> selectAttrList(Map<String, Object> params){
		return devAccountInfoDao.selectAttrList(params);
	}
	
}
