package com.qt.service.devAccount;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.devAccount.DevMaintainRecordDao;
import com.qt.entity.devAccount.DevMaintainRecord;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-06-29 11:54:28
 */
@Service
public class DevMaintainRecordService{

	private Logger logger = LoggerFactory.getLogger(DevMaintainRecordService.class);
     
    @Resource
	private DevMaintainRecordDao devMaintainRecordDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDevMaintainRecordList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devMaintainRecordDao.selectList(queryMap));
		resultMap.put("total", devMaintainRecordDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public boolean addDevMaintainRecord(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevMaintainRecord temp=MapUtil.toObject(DevMaintainRecord.class, params);
			devMaintainRecordDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//更新记录
	public boolean updateDevMaintainRecord(DevMaintainRecord devMaintainRecord){
		try {
			devMaintainRecordDao.update(devMaintainRecord);
			return true;
		} catch (Exception e) {
			logger.error("修改失败",e);
			return false;
		}
	}
	
	//删除记录
	public int delDevMaintainRecord(Map<String, Object> params){
		return devMaintainRecordDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDevMaintainRecordBat(String[] idArray){
		for(String pk:idArray){
			devMaintainRecordDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DevMaintainRecord> getAllDevMaintainRecord(Map<String, Object> params){
		return devMaintainRecordDao.selectList(params);
	}
	
}
