package com.qt.service.devAccount;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.devAccount.DevDefectRecordDao;
import com.qt.entity.devAccount.DevDefectRecord;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-06-29 11:54:46
 */
@Service
public class DevDefectRecordService{

	private Logger logger = LoggerFactory.getLogger(DevDefectRecordService.class);
     
    @Resource
	private DevDefectRecordDao devDefectRecordDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDevDefectRecordList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", devDefectRecordDao.selectList(queryMap));
		resultMap.put("total", devDefectRecordDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public boolean addDevDefectRecord(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevDefectRecord temp=MapUtil.toObject(DevDefectRecord.class, params);
			devDefectRecordDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//更新记录
	public boolean updateDevDefectRecord(DevDefectRecord devDefectRecord){
		try {
			devDefectRecordDao.update(devDefectRecord);
			return true;
		} catch (Exception e) {
			logger.error("修改失败",e);
			return false;
		}
	}
	
	//删除记录
	public int delDevDefectRecord(Map<String, Object> params){
		return devDefectRecordDao.deleteByMap(params);
		
	}
	
	//根据主键批量删除
	public int delDevDefectRecordBat(String[] idArray){
		for(String pk:idArray){
			devDefectRecordDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DevDefectRecord> getAllDevDefectRecord(Map<String, Object> params){
		return devDefectRecordDao.selectList(params);
	}
	
}
