package com.qt.service.devAccount;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.common.utils.PageUtil;
import com.qt.common.utils.APPUtils.QrCodeCreateUtil;
import com.qt.entity.devAccount.DevArchives;
import com.qt.entity.file.DfcDirectory;
import com.qt.repository.devAccount.DevArchivesDao;
import com.qt.repository.file.DfcDirectoryDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-06-25 01:29:48
 */
@Service
public class DevArchivesService{

	private Logger logger = LoggerFactory.getLogger(DevArchivesService.class);
     
    @Resource
	private DevArchivesDao devArchivesDao;
    @Resource
	private DfcDirectoryDao dfcDirectoryDao;
    @Autowired
    private QrCodeCreateUtil qrCodeCreateUtil;
	@Value("${dfcDirectory_path}")
	private String dfcDirectory_path;
    
	
    public List<DevArchives> selectAllArchives(Map<String,Object> params){
    	return devArchivesDao.selectAll(params);
    }
	
	public DevArchives selectAllInfo(Map<String,Object> params){
		return devArchivesDao.selectAllInfo(params);
	}
	
	public List<DevArchives> getChildList(String nodeId,List<DevArchives> allLists,List<DevArchives> childList) {
    	for (DevArchives nav : allLists) {
            //遍历出父id等于参数的id，add进子节点集合
            if (null!=nav.getDeaNodeParentId()&&nav.getDeaNodeParentId().equals(nodeId)) {
            	getChildList(nav.getDeaNodeId(), allLists,childList);
                childList.add(nav);
            }
        }
   		return childList;
    }
    
	//根据条件搜索记录
	public Map<String , Object> getDevArchivesList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		List<DevArchives> list=devArchivesDao.selectList(queryMap);
		if(params.get("pid")!=null && params.get("pid")!=""){
			List<DevArchives> childList = new ArrayList<DevArchives>();
			Map<String, Object> params1=new HashMap<String, Object>();
			params1.put("deaNodeId", params.get("pid"));
			List<DevArchives> dev=devArchivesDao.selectList(params1);
			if(dev.size()>0){
				childList.addAll(dev);
			}
			childList.addAll(getChildList(params.get("pid").toString(),list,new ArrayList<DevArchives>()));
			Integer total=childList.size();
			PageUtil<DevArchives> pageData=new PageUtil<DevArchives>(childList,Integer.parseInt(params.get("limit").toString()));
			resultMap.put("rows", pageData.getPagedData(Integer.parseInt(params.get("offset").toString())));
			resultMap.put("total", total);
		}else{
			Integer total=list.size();
			PageUtil<DevArchives> pageData=new PageUtil<DevArchives>(list,Integer.parseInt(params.get("limit").toString()));
			resultMap.put("rows", pageData.getPagedData(Integer.parseInt(params.get("offset").toString())));
			resultMap.put("total", total);
		}
		return resultMap;
	}
    public String getRandomNo() {  
    	String nums = "";
    	String[] codeChars = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
        for (int i = 0 ; i < 13 ; i++) {
            int charNum = (int)Math.floor(Math.random() * codeChars.length);
            nums += codeChars[charNum];
        }
        return nums;
   }     
	
	//新增记录
	public boolean addDevArchives(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DevArchives temp=MapUtil.toObject(DevArchives.class, params);
			String nodeID=UUID.randomUUID().toString().replaceAll("-","");
			temp.setDeaNodeId(nodeID);
			temp.setDeaIsleaf(1);
			devArchivesDao.insert(temp);
			String pid=temp.getDeaNodeParentId();
			if(StringUtils.isNotBlank(pid)) {//更新父节点
				DevArchives dev=new DevArchives();
				dev.setDeaNodeId(pid);
				dev.setDeaIsleaf(0);
				devArchivesDao.update(dev);
			}	
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//更新记录
	public boolean updateDevArchives(DevArchives devArchives){
		try {
			String id=devArchives.getDeaNodeId();
			devArchivesDao.update(devArchives);
			DevArchives dev=devArchivesDao.selectByPk(id);
			String deaQrcodeUrl=dev.getDeaQrcodeUrl();
			if(!StringUtils.isNotBlank(deaQrcodeUrl)){
				String dcsId=dev.getDeaNodeRelationDcs();
				//查询平台资料管理中心数据中是否有“设备资料”这个文件夹
				Map<String,Object> params_2=new HashMap<String, Object>();
				Map<String,Object> params_3=new HashMap<String, Object>();
				params_2.put("dfcName", "设备资料");
				params_2.put("dfcParentId", "0");//根目录
				List<DfcDirectory> list1=dfcDirectoryDao.selectListByMap(params_2);
				int dcfId1=-1;
				if(list1.size()>0){//若有
					dcfId1=list1.get(0).getDfcId();
				}else{//若无新增数据进中心库
					DfcDirectory dcf1=new DfcDirectory();
					dcf1.setDfcName("设备资料");
					dcf1.setDfcParentId(0);
					dcf1.setDfcTime(new Date());
					dcfId1=dfcDirectoryDao.insertGetId(dcf1);
				}
				if(dcfId1!=-1){
					//查询平台资料管理中心数据中是否有“二维码”这个文件夹
					params_2.clear();
					params_2.put("dfcName", "二维码");
					params_2.put("dfcParentId", dcfId1);
					List<DfcDirectory> list2=dfcDirectoryDao.selectListByMap(params_2);
					int dcfId2=-1;
					if(list2.size()>0){//若有
						dcfId2=list1.get(0).getDfcId();
					}else{//若无新增数据进中心库
						DfcDirectory dcf2=new DfcDirectory();
						dcf2.setDfcName("二维码");
						dcf2.setDfcParentId(dcfId1);
						dcf2.setDfcTime(new Date());
						dcfId2=dfcDirectoryDao.insertGetId(dcf2);
					}
					String path="//"+dcsId+"//"+dcfId1+"//"+dcfId2;
					try {
						File file =new File(dfcDirectory_path+path);   
			            if(!file.exists()  && !file.isDirectory()){      
			        		file.mkdirs();   
			        	}
			            boolean isRepeat = true;
			    		Long deaQrcodeId=1l;
			    		//判断生成的二维码id是否重复
			    		while(isRepeat){
			    			deaQrcodeId = System.currentTimeMillis();
			    			params_3.put("deaQrcodeId", deaQrcodeId);
			    			List<DevArchives> lis = devArchivesDao.selectList(params_3);
			    			if(lis==null||lis.size()==0){
			    				isRepeat = false;
			    			}
			    		}
			            qrCodeCreateUtil.createQrCode(new FileOutputStream(new File(dfcDirectory_path+path+"//"+deaQrcodeId+".png")),"{\"from\":\"SITC\",\"deaQrcodeId\":\""+deaQrcodeId+"\"}",900,"png");
			            DevArchives dev1=new DevArchives();
			            dev1.setDeaNodeId(id);
			            dev1.setDeaQrcodeId(deaQrcodeId+"");
			            dev1.setDeaQrcodeUrl(path+"//"+deaQrcodeId+".png");
			            devArchivesDao.update(dev1);
					} catch (Exception e) {
						 e.printStackTrace();
					}				
				}
			}
			return true;
		} catch (Exception e) {
			logger.error("修改失败",e);
			return false;
		}
	}
	
	//删除记录
	public int delDevArchives(Map<String, Object> params){
		int i=0;
		DevArchives dev = devArchivesDao.selectByPk(params.get("deaNodeId")+"");
		if(dev!=null){
			i=devArchivesDao.deleteByMap(params);
			//删除二维码照片
		     if(i>0){
		    	 File file = new File(dfcDirectory_path+dev.getDeaQrcodeUrl());
		    	 if (file.isFile() && file.exists()) {  
			         file.delete();
			     }
		     }
		}
		return i;
	}
	
	//根据主键批量删除
	public int delDevArchivesBat(String[] idArray){
		for(String pk:idArray){
			devArchivesDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DevArchives> getAllDevArchives(Map<String, Object> params){
		List<DevArchives> list=devArchivesDao.selectList(params);
		if(params.get("pid")!=null && params.get("pid")!=""){
			List<DevArchives> childList = new ArrayList<DevArchives>();
			Map<String, Object> params1=new HashMap<String, Object>();
			params1.put("deaNodeId", params.get("pid"));
			List<DevArchives> dev=devArchivesDao.selectList(params1);
			if(dev.size()>0){
				childList.addAll(dev);
			}
			childList.addAll(getChildList(params.get("pid").toString(),list,new ArrayList<DevArchives>()));
			return childList;
		}else{
			return list;
		}
	}
	

	public DevArchives getOneByNodeId(String id){
		return devArchivesDao.selectByPk(id);
	}
	
}
