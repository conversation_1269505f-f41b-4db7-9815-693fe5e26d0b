package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.FileOptUtil;
import com.qt.common.utils.MapUtil;
import com.qt.entity.newFile.DcsStationGf;
import com.qt.entity.newFile.SunshineHours;
import com.qt.repository.newFile.DcsStationGfDao;
import com.qt.utils.MultipartFileToFile;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-14 02:16:55
 */
@Service
public class DcsStationGfService{

	private Logger logger = LoggerFactory.getLogger(DcsStationGfService.class);
     
    @Resource
	private DcsStationGfDao dcsStationGfDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcsStationGfList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsStationGfDao.selectList(queryMap));
		resultMap.put("total", dcsStationGfDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDcsStationGf(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsStationGf temp=MapUtil.toObject(DcsStationGf.class, params);
			dcsStationGfDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	/**
	 * 查看
	 * @param stationId
	 * @return
	 * @throws Exception
	 */
	public DcsStationGf selectByStationId(String stationId){
		return dcsStationGfDao.selectByStationId(stationId);
	}
	
	//更新记录
	public void updateDcsStationGf(DcsStationGf dcsStationGf){
		dcsStationGfDao.update(dcsStationGf);
	}
	
	//删除记录
	public int delDcsStationGf(Map<String, Object> params){
		return dcsStationGfDao.deleteByMap(params);
	}
	
	/**
	 * 根据stationId批量删除
	 * @param idArray
	 * @param request
	 * @return
	 */
	public void delDcsStationGfBat(String stationId,HttpServletRequest request){
		FileOptUtil fileOptUtil = new FileOptUtil();
		String  threeDPath =  request.getServletContext().getRealPath("\\static\\3D\\"+ stationId);//系统路径 +\站点编号
		String dcsPath =  "C:\\firstPhoto\\pd\\"+ stationId;
		MultipartFileToFile.deleteDirectory(threeDPath);
		fileOptUtil.deleteDirectory(dcsPath);
		dcsStationGfDao.deleteByPk(stationId);
	}
	
	public List<DcsStationGf> getAllDcsStationGf(Map<String, Object> params){
		return dcsStationGfDao.selectList(params);
	}
	
	/**
	 * 查找光伏电价
	 * @return
	 */
	public List<DcsStationGf> selectGFPrice(){
		return dcsStationGfDao.selectGFPrice();
	}
	
	/**
	 * 根据stationId数组查询
	 * @param params
	 * @return
	 */
	public List<DcsStationGf> selectForStationIdList(Map<String,Object> params){
		return dcsStationGfDao.selectForStationIdList(params);
	}
	
	/**
	 * 查找光伏峰值日照时数
	 * @return
	 */
	public List<SunshineHours> selectForSunShine(){
		return dcsStationGfDao.selectForSunShine();
	}
	
	/**
	 * 根据stationId查询光伏基本信息和日照时数
	 * @param params
	 * @return
	 */
	public DcsStationGf selectGfAndSunShine(Map<String,Object> params){
		return dcsStationGfDao.selectGfAndSunShine(params);
	}
	
	/**
	 * 查询总装机容量
	 */
	public String countInstallCapacity(Map<String,Object> params){
		return dcsStationGfDao.countInstallCapacity(params);
	}
	
	/**
	 * 根据stationId数组查询光伏基本信息和日照时数列表
	 * @param params
	 * @return
	 */
	public List<DcsStationGf> selectGfAndSunShineToList(Map<String,Object> params){
		return dcsStationGfDao.selectGfAndSunShineToList(params);
	}
	
	/**
	 * 根据stationId查询sto_station_view_gf表电量
	 * @param params
	 * @return
	 */
	public List<DcsStationGf> selStationViewGF(Map<String,Object> params){
		return dcsStationGfDao.selStationViewGF(params);
	}

	/**
	 * 查询总装机容量
	 * @param params
	 * @return
	 */
	public String selSumCapacity(Map<String,Object> params){return dcsStationGfDao.selSumCapacity(params);}
	
}
