package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.DateUtils;
import com.qt.common.utils.MapUtil;
import com.qt.entity.communicationMonitor.DevStateR;
import com.qt.entity.newFile.DcgGateway;
import com.qt.repository.communicationMonitor.DevStateRDao;
import com.qt.repository.newFile.DcgGatewayDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-22 09:34:02
 */
@Service
public class DcgGatewayService{

	private Logger logger = LoggerFactory.getLogger(DcgGatewayService.class);
     
    @Resource
	private DcgGatewayDao dcgGatewayDao;
    
    @Resource
	private DevStateRDao devStateRDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcgGatewayList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcgGatewayDao.selectList(queryMap));
		resultMap.put("total", dcgGatewayDao.selectCount(queryMap));
		return resultMap;
	}
	
	/**
	 * 查询列表总数
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int selectCount(Map<String,Object> params){
		return dcgGatewayDao.selectCount(params);
	}
       
	//新增记录
	public void addDcgGateway(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcgGateway temp=MapUtil.toObject(DcgGateway.class, params);
			dcgGatewayDao.insert(temp);
			DevStateR devStateR  = new DevStateR();
			devStateR.setDevStationId(temp.getStationId());
			devStateR.setDevState(0);
			devStateR.setDevId(temp.getId()+"");
			devStateR.setDevOnlineTime(DateUtils.getAfterDayDate("0"));
			devStateR.setDevOfflineTime(DateUtils.getAfterDayDate("0"));
			devStateRDao.insert(devStateR);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcgGateway(DcgGateway dcgGateway){
		dcgGatewayDao.update(dcgGateway);
	}
	
	//删除记录
	public int delDcgGateway(Map<String, Object> params){
		return dcgGatewayDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcgGatewayBat(String[] idArray){
		Map<String, Object> maps = new HashMap<>();
		for(String pk:idArray){
			dcgGatewayDao.deleteByPk(pk);
			maps.put("devId", pk);
			devStateRDao.deleteByMap(maps);
		}
		return idArray.length;
	}
	
	public List<DcgGateway> getAllDcgGateway(Map<String, Object> params){
		return dcgGatewayDao.selectList(params);
	}
	
	/**
	 * 查看
	 * @param pk
	 * @return
	 * @throws Exception
	 */
	public DcgGateway selectByPk(String pk){
		return dcgGatewayDao.selectByPk(pk);
	}
	
}
