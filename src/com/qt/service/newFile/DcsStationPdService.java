package com.qt.service.newFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.newFile.DcsStationPdDao;
import com.qt.entity.newFile.DcsStationPd;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-14 02:17:11
 */
@Service
public class DcsStationPdService{

	private Logger logger = LoggerFactory.getLogger(DcsStationPdService.class);
     
    @Resource
	private DcsStationPdDao dcsStationPdDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcsStationPdList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsStationPdDao.selectList(queryMap));
		resultMap.put("total", dcsStationPdDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDcsStationPd(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsStationPd temp=MapUtil.toObject(DcsStationPd.class, params);
			dcsStationPdDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcsStationPd(DcsStationPd dcsStationPd){
		dcsStationPdDao.update(dcsStationPd);
	}
	
	//删除记录
	public int delDcsStationPd(Map<String, Object> params){
		return dcsStationPdDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcsStationPdBat(String[] idArray){
		for(String pk:idArray){
			dcsStationPdDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DcsStationPd> getAllDcsStationPd(Map<String, Object> params){
		return dcsStationPdDao.selectList(params);
	}
	
}
