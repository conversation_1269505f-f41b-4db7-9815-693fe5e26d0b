package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.common.utils.base.UuidUtil;
import com.qt.entity.newFile.DcsStaitonElectricZtree;
import com.qt.entity.newFile.DcsStaitonEnergyZtree;
import com.qt.entity.newFile.DcsStationBase;
import com.qt.repository.newFile.DcsStaitonElectricZtreeDao;
import com.qt.repository.newFile.DcsStaitonEnergyZtreeDao;
import com.qt.repository.newFile.DcsStationBaseDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-14 11:52:53
 */
@Service
public class DcsStationBaseService{

	private Logger logger = LoggerFactory.getLogger(DcsStationBaseService.class);
     
    @Resource
	private DcsStationBaseDao dcsStationBaseDao;
    
    @Resource
	private DcsStaitonElectricZtreeDao dcsStaitonElectricZtreeDao;
    
    @Resource
	private DcsStaitonEnergyZtreeDao dcsStaitonEnergyZtreeDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcsStationBaseList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsStationBaseDao.selectList(queryMap));
		resultMap.put("total", dcsStationBaseDao.selectCount(queryMap));
		return resultMap;
	}
	
	/**
	 * 查询列表总数
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int selectCount(Map<String,Object> params) {
		return dcsStationBaseDao.selectCount(params);
	}
       
	public List<DcsStationBase> selectList(Map<String, Object> params){
		return dcsStationBaseDao.selectList(params);
	}
	
	//新增记录
	public void addDcsStationBase(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsStationBase temp=MapUtil.toObject(DcsStationBase.class, params);
			dcsStationBaseDao.insert(temp);
			DcsStaitonElectricZtree dsez = new DcsStaitonElectricZtree();
			DcsStaitonEnergyZtree dsezNH = new DcsStaitonEnergyZtree();
			String ztreeNodeId = UuidUtil.get32UUID();
			dsez.setId(temp.getStationId()+"_"+ztreeNodeId);
			dsez.setPid("-1");
			dsez.setName(temp.getStationName());
			dsez.setThisId(temp.getStationId());
			dsez.setStationId(temp.getStationId());
			dcsStaitonElectricZtreeDao.insert(dsez); //给节点表添加站点节点--监控拓扑
			dsezNH.setId(temp.getStationId()+"_"+ztreeNodeId);
			dsezNH.setPid("-1");
			dsezNH.setName(temp.getStationName());
			dsezNH.setThisId(temp.getStationId());
			dsezNH.setStationId(temp.getStationId());
			dcsStaitonEnergyZtreeDao.insert(dsezNH); //给节点表添加站点节点--能耗拓扑
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcsStationBase(DcsStationBase dcsStationBase){
		dcsStationBaseDao.update(dcsStationBase);
	}
	/**
	 * 查看
	 * @param pk
	 * @return
	 * @throws Exception
	 */
	public DcsStationBase selectByPk(String pk) {
		return dcsStationBaseDao.selectByPk(pk);
	}
	
	//删除记录
	public int delDcsStationBase(Map<String, Object> params){
		return dcsStationBaseDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcsStationBaseBat(String[] idArray){
		for(String pk:idArray){
			dcsStationBaseDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DcsStationBase> getAllDcsStationBase(Map<String, Object> params){
		return dcsStationBaseDao.selectList(params);
	}

	public List<Map<String,Object>> selStationRotationTabData(Map<String,Object> params){
		return dcsStationBaseDao.selStationRotationTabData(params);
	}


	public List<DcsStationBase> getLatitudeAndLongitude(String stationId) {
		return dcsStationBaseDao.getLatitudeAndLongitude(stationId);
	}
}
