package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.newFile.DcvVideo;
import com.qt.repository.newFile.DcvVideoDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-23 04:31:29
 */
@Service
public class DcvVideoService{

	private Logger logger = LoggerFactory.getLogger(DcvVideoService.class);
     
    @Resource
	private DcvVideoDao dcvVideoDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcvVideoList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcvVideoDao.selectList(queryMap));
		resultMap.put("total", dcvVideoDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addDcvVideo(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcvVideo temp=MapUtil.toObject(DcvVideo.class, params);
			dcvVideoDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcvVideo(DcvVideo dcvVideo){
		dcvVideoDao.update(dcvVideo);
	}
	
	//删除记录
	public int delDcvVideo(Map<String, Object> params){
		return dcvVideoDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcvVideoBat(String[] idArray){
		for(String pk:idArray){
			dcvVideoDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<DcvVideo> getAllDcvVideo(Map<String, Object> params){
		return dcvVideoDao.selectList(params);
	}
	
	/**
	 * 查看
	 * @param pk
	 * @return
	 * @throws Exception
	 */
	public DcvVideo selectByPk(String pk) {
		return dcvVideoDao.selectByPk(pk);
	}
	
}
