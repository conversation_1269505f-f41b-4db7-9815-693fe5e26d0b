package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.newFile.DcsStaitonElectricZtree;
import com.qt.repository.newFile.DcsStaitonElectricZtreeDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-15 03:35:47
 */
@Service
public class DcsStaitonElectricZtreeService{

	private Logger logger = LoggerFactory.getLogger(DcsStaitonElectricZtreeService.class);
     
    @Resource
	private DcsStaitonElectricZtreeDao dcsStaitonElectricZtreeDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcsStaitonElectricZtreeList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsStaitonElectricZtreeDao.selectList(queryMap));
		resultMap.put("total", dcsStaitonElectricZtreeDao.selectCount(queryMap));
		return resultMap;
	}
	
	public List<DcsStaitonElectricZtree> selectList(Map<String, Object> params){
		return dcsStaitonElectricZtreeDao.selectList(params);
	}
       
	//新增记录
	public void addDcsStaitonElectricZtree(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsStaitonElectricZtree temp=MapUtil.toObject(DcsStaitonElectricZtree.class, params);
			dcsStaitonElectricZtreeDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcsStaitonElectricZtree(DcsStaitonElectricZtree dcsStaitonElectricZtree){
		dcsStaitonElectricZtreeDao.update(dcsStaitonElectricZtree);
	}
	
	//删除记录
	public int delDcsStaitonElectricZtree(Map<String, Object> params){
		return dcsStaitonElectricZtreeDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcsStaitonElectricZtreeBat(String[] idArray){
		for(String pk:idArray){
			dcsStaitonElectricZtreeDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public void delForId(String id){
		dcsStaitonElectricZtreeDao.deleteByPk(id);
	}
	
	public List<DcsStaitonElectricZtree> getAllDcsStaitonElectricZtree(Map<String, Object> params){
		return dcsStaitonElectricZtreeDao.selectList(params);
	}
	
	public void updatePid(Map<String,Object> params){
		dcsStaitonElectricZtreeDao.updatePid(params);
	}
	
	/**
	 * 新增列表
	 * @param list
	 * @throws Exception
	 */
	public void insertList(List<DcsStaitonElectricZtree> list) {
		dcsStaitonElectricZtreeDao.insertList(list);
	}
	
	/**
	 * 查找树形节点
	 * @param params
	 * @return
	 */
	public List<DcsStaitonElectricZtree> selTreeForFloor(Map<String,Object> params) {
		return dcsStaitonElectricZtreeDao.selTreeForFloor(params);
	}
	
	/**
	 * 查找树形除一级二级外其他节点
	 * @param params
	 * @return
	 */
	public List<DcsStaitonElectricZtree> selTreeForOtherFloor(Map<String,Object> params) {
		return dcsStaitonElectricZtreeDao.selTreeForOtherFloor(params);
	}
	
	/**
	 * 删除除顶级节点外的其它节点
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int deleteForOhterFloor(Map<String,Object> params){
		return dcsStaitonElectricZtreeDao.deleteForOhterFloor(params);
	}
	
}
