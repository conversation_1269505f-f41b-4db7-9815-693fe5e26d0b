package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.newFile.DcsStaitonEnergyZtree;
import com.qt.repository.newFile.DcsStaitonEnergyZtreeDao;

/**
 * 能耗树-----
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-15 03:35:47
 */
@Service
public class DcsStaitonEnergyZtreeService{

	private Logger logger = LoggerFactory.getLogger(DcsStaitonEnergyZtreeService.class);
     
    @Resource
	private DcsStaitonEnergyZtreeDao dcsStaitonEnergyZtreeDao;
	
	//根据条件搜索记录
	public Map<String , Object> getDcsStaitonEnergyZtreeList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dcsStaitonEnergyZtreeDao.selectList(queryMap));
		resultMap.put("total", dcsStaitonEnergyZtreeDao.selectCount(queryMap));
		return resultMap;
	}
	
	public List<DcsStaitonEnergyZtree> selectList(Map<String, Object> params){
		return dcsStaitonEnergyZtreeDao.selectList(params);
	}
       
	//新增记录
	public void addDcsStaitonEnergyZtree(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DcsStaitonEnergyZtree temp=MapUtil.toObject(DcsStaitonEnergyZtree.class, params);
			dcsStaitonEnergyZtreeDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateDcsStaitonEnergyZtree(DcsStaitonEnergyZtree dcsStaitonEnergyZtree){
		dcsStaitonEnergyZtreeDao.update(dcsStaitonEnergyZtree);
	}
	
	//删除记录
	public int delDcsStaitonEnergyZtree(Map<String, Object> params){
		return dcsStaitonEnergyZtreeDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delDcsStaitonEnergyZtreeBat(String[] idArray){
		for(String pk:idArray){
			dcsStaitonEnergyZtreeDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public void delForId(String id){
		dcsStaitonEnergyZtreeDao.deleteByPk(id);
	}
	
	public List<DcsStaitonEnergyZtree> getAllDcsStaitonEnergyZtree(Map<String, Object> params){
		return dcsStaitonEnergyZtreeDao.selectList(params);
	}
	
	public void updatePid(Map<String,Object> params){
		dcsStaitonEnergyZtreeDao.updatePid(params);
	}
	
	/**
	 * 新增列表
	 * @param list
	 * @throws Exception
	 */
	public void insertList(List<DcsStaitonEnergyZtree> list) {
		dcsStaitonEnergyZtreeDao.insertList(list);
	}
	
	/**
	 * 查找树形节点
	 * @param params
	 * @return
	 */
	public List<DcsStaitonEnergyZtree> selTreeForFloor(Map<String,Object> params) {
		return dcsStaitonEnergyZtreeDao.selTreeForFloor(params);
	}
	
	/**
	 * 查找树形除一级二级外其他节点
	 * @param params
	 * @return
	 */
	public List<DcsStaitonEnergyZtree> selTreeForOtherFloor(Map<String,Object> params) {
		return dcsStaitonEnergyZtreeDao.selTreeForOtherFloor(params);
	}
	
	/**
	 * 删除除顶级节点外的其它节点
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int deleteForOhterFloor(Map<String,Object> params){
		return dcsStaitonEnergyZtreeDao.deleteForOhterFloor(params);
	}
	
	/**
	 * 查找顶层节点
	 * @return
	 */
	public DcsStaitonEnergyZtree selForQtBaseOrg(){
		return dcsStaitonEnergyZtreeDao.selForQtBaseOrg();
	}
	
}
