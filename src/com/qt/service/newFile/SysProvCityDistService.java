package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.newFile.SysProvCityDist;
import com.qt.repository.newFile.SysProvCityDistDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2019-09-12 09:30:53
 */
@Service
public class SysProvCityDistService{

	private Logger logger = LoggerFactory.getLogger(SysProvCityDistService.class);
     
    @Resource
	private SysProvCityDistDao sysProvCityDistDao;
	
	//根据条件搜索记录
	public Map<String , Object> getSysProvCityDistList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", sysProvCityDistDao.selectList(queryMap));
		resultMap.put("total", sysProvCityDistDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addSysProvCityDist(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			SysProvCityDist temp=MapUtil.toObject(SysProvCityDist.class, params);
			sysProvCityDistDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateSysProvCityDist(SysProvCityDist sysProvCityDist){
		sysProvCityDistDao.update(sysProvCityDist);
	}
	
	//删除记录
	public int delSysProvCityDist(Map<String, Object> params){
		return sysProvCityDistDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delSysProvCityDistBat(String[] idArray){
		for(String pk:idArray){
			sysProvCityDistDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<SysProvCityDist> getAllSysProvCityDist(Map<String, Object> params){
		return sysProvCityDistDao.selectList(params);
	}
	
	/**
	 * 查找省（省市区树用）
	 * @param params
	 * @return
	 */
	public List<SysProvCityDist> selLists(Map<String, Object> params){
		return sysProvCityDistDao.selLists(params);
	}
	
	/**
	 * 查找市（省市区树用）
	 * @param params
	 * @return
	 */
	public List<SysProvCityDist> selCitys(Map<String, Object> params){
		return sysProvCityDistDao.selCitys(params);
	}
	
	/**
	 * 查找区（省市区树用）
	 * @param params
	 * @return
	 */
	public List<SysProvCityDist> selDistrict(Map<String,Object> params){
		return sysProvCityDistDao.selDistrict(params);
	}
	
	/**
	 * 查找所有省或市
	 * @return
	 */
	public List<SysProvCityDist> queryProOrCity(Map<String,Object> params){
		return sysProvCityDistDao.queryProOrCity(params);
	}
	
}
