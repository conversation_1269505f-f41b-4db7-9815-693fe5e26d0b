package com.qt.service.newFile;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.newFile.StruExprRelation;
import com.qt.repository.newFile.StruExprRelationDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2020-07-23 06:10:11
 */
@Service
public class StruExprRelationService {

	private Logger logger = LoggerFactory.getLogger(StruExprRelationService.class);
     
    @Resource
	private StruExprRelationDao struExprRelationDao;

    //获取已关联的设备集合
	public List<StruExprRelation> getStruExprRelationList(Map<String, Object> params){
		return struExprRelationDao.selectList(params);
	}

	public List<StruExprRelation> selectCanRelation(Map<String,Object> params){
		return struExprRelationDao.selectCanRelation(params);
	}

	//批量增加
	public boolean insertList(List<StruExprRelation> list){
		try {
			struExprRelationDao.insertList(list);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}

	//新增记录
	public void addStruExprRelation(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			StruExprRelation temp= MapUtil.toObject(StruExprRelation.class, params);
			struExprRelationDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateStruExprRelation(StruExprRelation struExprRelation){
		struExprRelationDao.update(struExprRelation);
	}
	
	//删除记录
	public int delStruExprRelation(Map<String, Object> params){
		return struExprRelationDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delStruExprRelationBat(String[] idArray){
		for(String pk:idArray){
			struExprRelationDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<StruExprRelation> getAllStruExprRelation(Map<String, Object> params){
		return struExprRelationDao.selectList(params);
	}
	
}
