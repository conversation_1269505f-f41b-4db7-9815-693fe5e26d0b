package com.qt.service.newFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.qt.entity.statistics.StoDataRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.DateUtils;
import com.qt.common.utils.MapUtil;
import com.qt.entity.communicationMonitor.DelStateR;
import com.qt.entity.newFile.DceEquipment;
import com.qt.entity.newFile.DcgGateway;
import com.qt.repository.communicationMonitor.DelStateRDao;
import com.qt.repository.newFile.DceEquipmentDao;
import com.qt.repository.newFile.DcgGatewayDao;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2021-09-22 04:43:49
 */
@Service
public class DceEquipmentService{

	private Logger logger = LoggerFactory.getLogger(DceEquipmentService.class);

    @Resource
	private DceEquipmentDao dceEquipmentDao;

    @Resource
	private DelStateRDao delStateRDao;

    @Resource
	private DcgGatewayDao dcgGatewayDao;

	//根据条件搜索记录
	public Map<String , Object> getDceEquipmentList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", dceEquipmentDao.selectList(queryMap));
		resultMap.put("total", dceEquipmentDao.selectCount(queryMap));
		return resultMap;
	}

	//新增记录
	public void addDceEquipment(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			DceEquipment temp=MapUtil.toObject(DceEquipment.class, params);
			dceEquipmentDao.insert(temp);
			DcgGateway dcgGateway = dcgGatewayDao.selectByPk(temp.getGatewayId()+"");
			DelStateR delStateR = new DelStateR();
			delStateR.setDelStationId(dcgGateway.getStationId());
			delStateR.setDelLineId(temp.getEquId());
			delStateR.setDelOnlineTime(DateUtils.getAfterDayDate("0"));
			delStateR.setDelOfflineTime(DateUtils.getAfterDayDate("0"));
			delStateR.setDelState(0);
			delStateRDao.insert(delStateR);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}

	//更新记录
	public void updateDceEquipment(DceEquipment dceEquipment){
		dceEquipmentDao.update(dceEquipment);
	}

	//删除记录
	public int delDceEquipment(Map<String, Object> params){
		return dceEquipmentDao.deleteByMap(params);
	}

	//根据主键批量删除
	public int delDceEquipmentBat(String[] idArray){
		Map<String, Object> maps = new HashMap<>();
		for(String pk:idArray){
			dceEquipmentDao.deleteByPk(pk);
			maps.put("delLineId", pk);
			delStateRDao.deleteByMap(maps);
		}
		return idArray.length;
	}

	public List<DceEquipment> getAllDceEquipment(Map<String, Object> params){
		return dceEquipmentDao.selectList(params);
	}

	/**
	 * 查看
	 * @param pk
	 * @return
	 * @throws Exception
	 */
	public DceEquipment selectByPk(String pk) {
		return dceEquipmentDao.selectByPk(pk);
	}

	/**
	 * 查询列表总数
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int selectCount(Map<String,Object> params) {
		return dceEquipmentDao.selectCount(params);
	}

	public List<DceEquipment> selectEquByDcsId(String stationId){
		return dceEquipmentDao.selectEquByDcsId(stationId);
	}

	public List<DceEquipment> selectListDB(Map<String,Object> params){
		return dceEquipmentDao.selectListDB(params);
	}

	/**
	 * 根据设备类型名称查询设备列表
	 * @param params
	 * @return
	 */
	public Map<String , Object> selForEquTypeNameList(Map<String, Object> params){
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		resultMap.put("rows", dceEquipmentDao.selForEquTypeName(params));
		resultMap.put("total", dceEquipmentDao.selForEquTypeNameCount(params));
		return resultMap;
	}

	/**
	 * 查询设备类型名称查询设备列表总数
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int selForEquTypeNameCount(Map<String,Object> params){
		return dceEquipmentDao.selForEquTypeNameCount(params);
	}

	/**
	 * 查询del_state_r表设备是（种类名）的在线or离线总数
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int selForEquTypeNameAndDelStateRCount(Map<String,Object> params) {
		return dceEquipmentDao.selForEquTypeNameAndDelStateRCount(params);
	}

	/**
	 * 查询最大设备id（生成设备id用）
	 * @return
	 */
	public DceEquipment selOrderByEquId(){
		return dceEquipmentDao.selOrderByEquId();
	}

	/**
	 * 根据站点id查询其下设备类型所属的顶层设备类型信息
	 * @param params
	 * @return
	 */
	public List<DceEquipment> selDevTypeRootData(Map<String,Object> params){
		return dceEquipmentDao.selDevTypeRootData(params);
	}

	/**
	 * 根据站点id查找下面设备各类型的数量
	 * @param params
	 * @return
	 */
	public List<Map<String,Object>> selDevTypeRootCount(Map<String,Object> params){
		return dceEquipmentDao.selDevTypeRootCount(params);
	}

	/**
	 * 查找逆变器的设备数量
	 * @param params
	 * @return
	 */
	public int selNBQCount(Map<String,Object> params){
		return dceEquipmentDao.selNBQCount(params);
	}

	public Integer getInverterDayElec(String equ_name, String time) {
		return dceEquipmentDao.getInverterDayElec(equ_name,time);
	}

	public String getEquipmentId(String equ_name) {
		return dceEquipmentDao.getEquipmentId(equ_name);
	}

	public String getCurrentPowerByEquId(String equ_id,String tableName) {
		return dceEquipmentDao.getCurrentPowerByEquId(equ_id,tableName);
	}
}
