package com.qt.service.APP;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.entity.APP.AccountCID;
import com.qt.repository.APP.AccountCIDDao;


@Service
public class AccountCIDService{

	private Logger logger = LoggerFactory.getLogger(AccountCIDService.class);
     
    @Resource
	private AccountCIDDao accountCIDDao;
	
       
	//新增记录
	public boolean addAccountCID(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			AccountCID temp=MapUtil.toObject(AccountCID.class, params);
			accountCIDDao.insert(temp);
			return true;
		} catch (Exception e) {
			logger.error("添加失败",e);
			return false;
		}
	}
	
	//更新记录
	public boolean updateAccountCID(AccountCID accountCID){
		try {
			accountCIDDao.update(accountCID);
			return true;
		} catch (Exception e) {
			logger.error("修改失败",e);
			return false;
		}
	}
	
	//删除记录
	public int delAccountCID(Map<String, Object> params){
		return accountCIDDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delAccountCIDBat(String[] idArray){
		for(String pk:idArray){
			accountCIDDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public AccountCID getAccountCID(String accountId){
		return accountCIDDao.selectByPk(accountId);
	}
	
	//清空CID
	public int clearAccountCIDs(AccountCID accountCID){
		return accountCIDDao.clearAccountCIDs(accountCID);
	}
	
}
