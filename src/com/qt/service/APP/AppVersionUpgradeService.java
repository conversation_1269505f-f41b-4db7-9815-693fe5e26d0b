package com.qt.service.APP;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.qt.common.utils.MapUtil;
import com.qt.repository.APP.AppVersionUpgradeDao;
import com.qt.entity.APP.AppVersionUpgrade;

/**
 * TODO 本代码由代码生成工具生成
 * @date 2020-06-20 12:01:21
 */
@Service
public class AppVersionUpgradeService{

	private Logger logger = LoggerFactory.getLogger(AppVersionUpgradeService.class);
     
    @Resource
	private AppVersionUpgradeDao appVersionUpgradeDao;
	
	//根据条件搜索记录
	public Map<String , Object> getAppVersionUpgradeList(Map<String, Object> params){
		Map<String , Object> queryMap = new HashMap<String, Object>();
		Map<String , Object> resultMap = new HashMap<String, Object>();
		//TODO 根据需要封装查询需要的条件
		queryMap.putAll(params);
		resultMap.put("rows", appVersionUpgradeDao.selectList(queryMap));
		resultMap.put("total", appVersionUpgradeDao.selectCount(queryMap));
		return resultMap;
	}
       
	//新增记录
	public void addAppVersionUpgrade(Map<String, Object> params){
		try {
			//TODO 根据需要封装查询需要的条件
			AppVersionUpgrade temp=MapUtil.toObject(AppVersionUpgrade.class, params);
			appVersionUpgradeDao.insert(temp);
		} catch (Exception e) {
			logger.error("添加失败",e);
		}
	}
	
	//更新记录
	public void updateAppVersionUpgrade(AppVersionUpgrade appVersionUpgrade){
		appVersionUpgradeDao.update(appVersionUpgrade);
	}
	
	//删除记录
	public int delAppVersionUpgrade(Map<String, Object> params){
		return appVersionUpgradeDao.deleteByMap(params);
	}
	
	//根据主键批量删除
	public int delAppVersionUpgradeBat(String[] idArray){
		for(String pk:idArray){
			appVersionUpgradeDao.deleteByPk(pk);
		}
		return idArray.length;
	}
	
	public List<AppVersionUpgrade> getAllAppVersionUpgrade(Map<String, Object> params){
		return appVersionUpgradeDao.selectList(params);
	}
	
	public AppVersionUpgrade getAppVersionUpgrade(Map<String, Object> params){
		return appVersionUpgradeDao.selectOne(params);
	}
	
}
