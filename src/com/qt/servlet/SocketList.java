package com.qt.servlet;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.qt.entity.statistics.EvrAlarm;
import com.qt.entity.test.EvrBean;

public class SocketList {
	public static List<EvrBean> list = new ArrayList<EvrBean>();
	
	public static List<EvrAlarm> alarmList = new ArrayList<EvrAlarm>();
	
	public void add(String value1,String value2,String value3,String value4,String value5,String value6,String value7,String value8,String value9,String value10,String value11){
		EvrBean evrBean = new EvrBean();
		evrBean.setEvrStationId(value1);
		evrBean.setEvrLineId(value2);
		evrBean.setEvrBsaCode(value3);
		evrBean.setEvrDesc(value4);
		evrBean.setEvrOcTime(value5);
		evrBean.setEvrLevel(value6);
		evrBean.setEvrAlarmBianhao(value7);
		evrBean.setEvrTime(value8);
		evrBean.setEvrNowValue(value9);
		evrBean.setEvrAlarmValue(value10);
		evrBean.setEvrReturnValue(value11);
		list.add(evrBean);
		
		EvrAlarm evrAlarm = new EvrAlarm();
		evrAlarm.setEvrStationId(value1);
		evrAlarm.setEvrLineId(value2);
		evrAlarm.setEvrBsaCode(Integer.valueOf(value3));
		evrAlarm.setEvrDesc(value4);
		SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss");
		Date date = new Date();
		try {
			date = formatter.parse(value5);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		evrAlarm.setEvrOcTime(date);
		evrAlarm.setEvrLevel(Integer.valueOf(value6));
		evrAlarm.setEvrAlarmBianhao(value7);
		evrAlarm.setEvrTime(value8);
		evrAlarm.setEvrNowValue(value9);
		evrAlarm.setEvrAlarmValue(value10);
		evrAlarm.setEvrReturnValue(value11);
		alarmList.add(evrAlarm);
		System.out.println("List集合的长度："+list.size());
		System.out.println("跳过定制器");
		
	}
	
}
