package com.qt.servlet;
import java.sql.Connection;
import java.sql.DriverManager;



public class ConnManager {

    public ConnManager() {	
    }
    
    public static Connection getConnection() {
    
    String dbdriver =  Config.getProperty("jdbc.mysql.driver");
	String dburl    =  Config.getProperty("jdbc.mysql.url");
	String dbuser   =  Config.getProperty("jdbc.mysql.username");
	String dbpwd    =  Config.getProperty("jdbc.mysql.password");
	
	System.out.println(dbdriver);
	System.out.println(dburl);
	System.out.println(dbuser);
	System.out.println(dbpwd);
	
    Connection connection = null;
    try {
        Class.forName(dbdriver);
        connection = DriverManager.getConnection(dburl, dbuser, dbpwd);
    }
    catch(Exception ex) {
        ex.printStackTrace();
        System.out.println(ex.getMessage());
    }
        return connection;
    }

    public static void releaseConnection(Connection connection) {
    try {
        if(connection != null) {
             connection.close();
             connection = null;
             }
    }
    catch(Exception ex) {
        System.out.println(ex);
    }
    }
}