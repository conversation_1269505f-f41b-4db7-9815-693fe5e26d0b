package com.qt.servlet;

import java.sql.Connection;
import java.util.List;

import com.qt.entity.test.EvrBean;

public class GjInsertUtil extends DataBase {

	public Connection conn = null;

	/**
	 * 入口函数 
	 * 
	 * @param arg
	 */
//	public boolean save(String value1,String value2,String value3,String value4,String value5,String value6,String value7,String value8,String value9,String value10,String value11,String value12) {
//	public boolean save(String value1,String value2,String value3,String value4,String value5,String value6,String value7,String value8,String value9,String value10,String value11) {
	public boolean save(List<EvrBean> list){
		try {
			openDB();
//			String strSQL = "INSERT INTO Evh_Alarm (evh_station_id, evh_line_id,evh_bsa_code,evh_desc,evh_oc_time,evh_rc_time,evh_rc_type,evh_rc_id,evh_alarm_bianhao,evh_now_value,evh_alarm_value,evh_return_value) VALUES ('"+value1+"', '"+value2+"', '"+value3+"', '"+value4+"', '"+value5+"', '"+value6+"', '"+value7+"', '"+value8+"', '"+value9+"', '"+value10+"', '"+value11+"', '"+value12+"')";
//			String strSQL = "INSERT INTO Evr_Alarm (evr_station_id, evr_line_id,evr_bsa_code,evr_desc,evr_oc_time,evr_level,evr_alarm_bianhao,evr_time,evr_now_value,evr_alarm_value,evr_return_value) VALUES ('"+value1+"', '"+value2+"', '"+value3+"', '"+value4+"', '"+value5+"', '"+value6+"', '"+value7+"', '"+value8+"', '"+value9+"', '"+value10+"', '"+value11+"')"; 
			System.out.println("-------------------------------------------");
			System.out.println(list.size());
			System.out.println("-------------------------------------------");
			if (list.size()>0) {
				String strSQL = "";
				strSQL += "INSERT INTO Evr_Alarm (evr_station_id, evr_line_id,evr_bsa_code,evr_desc,evr_oc_time,evr_level,evr_alarm_bianhao,evr_time,evr_now_value,evr_alarm_value,evr_return_value) VALUES";
				for (EvrBean evrAlarm : list) {
					strSQL += "('"+evrAlarm.getEvrStationId()+"','"+evrAlarm.getEvrLineId()+"','"+evrAlarm.getEvrBsaCode()+"','"+evrAlarm.getEvrDesc()+"','"+evrAlarm.getEvrOcTime()+"','"+evrAlarm.getEvrLevel()+"','"+evrAlarm.getEvrAlarmBianhao()+"','"+evrAlarm.getEvrTime()+"','"+evrAlarm.getEvrNowValue()+"','"+evrAlarm.getEvrAlarmValue()+"','"+evrAlarm.getEvrReturnValue()+"'),";
				}
				strSQL = strSQL.substring(0, strSQL.length()-1);
				System.out.println("----------开始-------------");
				System.out.println(strSQL);
				System.out.println("----------结束-------------");
				return exeSQL(strSQL, "");
			} else {
				System.out.println("暂时没有数据！！！！");
				return false;
			}
		

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			closeDB();
		}

		return false;

	}
	
	public static void main(String[] args) {
		GjInsertUtil gjInsertUtil = new GjInsertUtil();
//		gjInsertUtil.save("444","555","1","2","3","4","5","6","7","8","9","10");
//		gjInsertUtil.save("444","555","1","2","3","4","5","6","7","8","9");
	}

}
