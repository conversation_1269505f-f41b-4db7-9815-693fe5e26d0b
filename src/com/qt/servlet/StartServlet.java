package com.qt.servlet;

 
import java.util.Timer;
import java.util.TimerTask;

import javax.servlet.*;
import javax.servlet.http.*;

import com.qt.entity.test.TcpData;




/**
 * System startup servlet 
 * doing some initiallizations by implements the method Startup.start
 */  
public class StartServlet extends HttpServlet {
	
	public static Integer temp = 0;
  /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
 //   private static final String CONTENT_TYPE = "text/html; charset=GBK";
    /**Initialize global variables*/
    public void init() throws ServletException {
    try {

        System.out.println("=====================程序后台正常启动=========================");
        
        //获取告警的参数
        
//        TcpData.getData();
        Timer timer = new Timer();  
        timer.schedule(new TimerTask() {  
            public void run() {  
                System.out.println("-------设定要指定任务--------");  
                TcpData.getData();
            }  
        }, 10000);
        
        //告警数据解析后转存到list中后   定时存储到数据库并且清空list集合
        Timer timer1 = new Timer();
        timer1.scheduleAtFixedRate(new TimerTask() {
          public void run() {
            System.out.println("-------设定要指定任务11111111111111111111--------");
            SocketList socketList = new SocketList();
            System.out.println("list长度："+socketList.list.size());
            GjInsertUtil gjInsertUtil = new GjInsertUtil();
            gjInsertUtil.save(socketList.list);
            socketList.list.clear();
            System.out.println("-------设定要指定任务11111111111111111111--------");
          }
        }, 20000, 10000);
        
      }
      catch (Exception ex) {
        System.err.println("系统启动失败");
        ex.printStackTrace();
        System.exit(1);
      }
      
      
  }  

    /**Clean up resources*/
    public void destroy() {
    }
}
