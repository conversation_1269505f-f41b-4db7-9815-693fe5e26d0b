package com.qt.servlet;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.List;
import com.qt.entity.statistics.EvrAlarm;

public class SocketWarnContent {
	public List<EvrAlarm> getAlarmList(){
//	public static void main(String[] args) {
		StringBuffer sb =new StringBuffer();
		BufferedReader br =null;
		String temp;
		Socket socket = null;
		try {
			socket  = new Socket("120.27.140.246",8900);
			br = new BufferedReader(new InputStreamReader(socket.getInputStream()));
			socket.setSoTimeout(1000*1000);
			while((temp=br.readLine())!= null){
				sb.append(temp);
			}
			if(sb!=null){
				System.out.println(sb.toString());
			}
		} catch (SocketTimeoutException e) {
			System.out.println("读取数据超时");
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			try {
				br.close();
				socket.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		
		}
		return null;
	}
	
}
