package com.qt.servlet;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.GregorianCalendar;
import java.text.DateFormat;
import java.util.Random;
/**
 * 常用基类
 * */
public class Bean {
	private static Bean tempBean=new Bean();
	private Bean(){}
	public static Bean getInstance(){
		return tempBean;
	}
	
	/**
	 * 将字符串转为数字
	 * @param value 字符类型的数�? 如："10"
	 * @return 数字类型的数�? 10
	 * */
	public int changeInt(String value) { // 将字符串转为数字 
		value = removeNull(value);
		int temp = 0;
		if(!value.equals("")){
			temp = Integer.parseInt(value);
		}
		return temp;
	}

	/**
	 * 将字符转为数�?
	 * @param cValue 传来char类型的参�?
	 * @return 返回 int 类型的数�?
	 * */
	public int changeInt(char value) { 
		int temp = changeInt(changeString(value));
		return temp;
	}

	/**
	 * 将浮点类型的转为正数类型
	 * @param dValue 类型的数
	 * @return int 类型 直截去了小数点后面的
	 * */
	public int changeInt(double value) { 
		int temp = (int) value;
		return temp;
	}
	/**
	 * String类型转为Int类型,字符�?""时返�?0
	 * @param value 字符�?
	 * @return 整型
	 * */
	public int changeIntOrZero(String value) { 
		int tempValue = 0;
		value = removeNull(value);
		if (value.equals("") == false){
			try {
				tempValue = changeInt(value);
			} catch (Exception ex) {
				return 0;
			}			
		}
		return tempValue;
	}

	/**
	 * int 转为 String
	 * @param nValue 数字类型�?
	 * @return 字符类型�?
	 * */
	public String changeString(int value) {
		return (Integer.toString(value));
	}

	/**
	 * String 转为 float类型
	 * @param value String类型的字�?
	 * @return float 类型的字�?
	 * */
	public float changeFloat(String value) {
		float fValue = 0;
		if (removeNull(value).equals("") == false) {
			fValue = Float.parseFloat(value);
		}
		return fValue;
	}

	/**
	 * float 转为String
	 * @param fValue float类型字符
	 * @return String 类型字符
	 * */
	public String changeString(float value) {
		return Float.toString(value);
	}

	public long changeLong(String value) {
		long lValue = 0;
		if (removeNull(value).equals("") == false) {
			lValue = Long.parseLong(value);
		}
		return lValue;
	}

	public String changeString(long value) {
		return Long.toString(value);
	}

	public double changeDouble(String value) {
		double dValue = 0;
		if (removeNull(value).equals("") == false) {
			dValue = Double.parseDouble(value);
		}
		return dValue;
	}

	public String changeString(double value) {
		return Double.toString(value);
	}

	public byte changeByte(String value) {
		return Byte.parseByte(value);
	}

	public String changeString(byte value) {
		return Byte.toString(value);
	}

	public String changeString(char value) {
		return Character.toString(value);
	}
	/**
	 * 得到明天的日�?
	 * @return 明天的日�? 如：当天为：2006-12-30
	 * @return 2006-12-31
	 * */
	public String getTomorrowDate() {
		String Tommrow = "";
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Date today = new java.util.Date();
		Date tommrow = new java.util.Date(today.getTime() + 24 * 3600 * 1000);
		Tommrow = df.format(tommrow);
		return Tommrow;
	}

	/**
	 * 去掉 null 并去前后空格
	 * @param value 字符�?
	 * @return �? null 的字符串
	 * */
	public String removeNull(String value) {
		if (value == null) {
			value = "";
		} else {
			value = value.trim();
		}
		if (value.equals("null")) {
			value = "";
		}
		return value;
	}

	/**
	 * 去掉 null 和前后空格以及脚本注�?(有防脚本注入之功�?)
	 * @param value 字符�?
	 * @return 去Null、前后空格�?�脚本注�?
	 * */
	public String removeNullAndSQL(String value) {
		value = removeNull(value);
//		value = value.toLowerCase();
		value = Bean.replace(value,"'", "''");
//		value = this.replace(value,"select ", "select_");
//		value = this.replace(value,"insert ", "insert_");
//		value = this.replace(value,"update ", "update_");
//		value = this.replace(value,"delete ", "delete_");
//		value = this.replace(value,"drop ", "drop_");
//		value = this.replace(value,"create ", "create_");
//		value = this.replace(value,"select ", "select_");
		return value;
	}

	/**
	 * 去掉 null 不去前后空格
	 * @param value 字符�?
	 * @return �? null 的字符串
	 * */
	public String removeNull_NotTrim(String value) {
		if (value == null) {
			value = "";
		}
		if (value.equals("null")) {
			value = "";
		}
		return value;
	}

	/**
	 * *取左边的多少个字�?
	 * @param strValue �?有的字符 
	 * @param nLength 取左边几�?
	 * @return 左边的字�?
	 * */
	public String leftString(String value, int length) {
		value = removeNull(value);
		if (value.length() > length) {
			value = value.substring(0, length); // 如果长度不够出现500�?
		} 
		return value;
	}

	/**
	 * 得到右边的多少个字符
	 * @param  strValue 原字符串
	 * @param nLength 要后面的多少�? 如：12345 当num=2 �? 结果�?45
	 * @return 右边的多个位字符
	 * */
	public String rightString(String value, int length) {
		value = removeNull(value);
		// 字符串�?�长�?
		int nValueLength = value.length();
		if (nValueLength > length) {
			value = value.substring(nValueLength - length, nValueLength); // 如果长度不够出现500�?
		}
		return value;
	}

	/**
	 * 取字符串右边的所有字�?
	 * @param value 源字符串 
	 * @param nStartLocation 从字符的�?么位置开�? 3 (表示从第3个位置开�?) 如： 123456
	 * @return 从某位置�?始之后的�?有内�? 结果为： 3456
	 * */
	public String rightAllString(String value, int startLocation) {
		value = removeNull(value);
		startLocation = startLocation - 1;
		if (startLocation >= 0 && value.length() > startLocation) {
			value = value.substring(startLocation, value.length());
		} 
		return value;
	}

	/**
	 * 取字符串右边的所有字�?
	 * @param value 源字符串 
	 * @param nStartLocation 从字符串的什么位置开�? 5 (表示从第5个位置之后开始也就是�?6�?)           
	 * @param nEndLocation 从字符串的什么位置结�?
	 * @return 从某位置�?始之后的�?有内�?
	 * */
	public String subString(String value, int startLocation, int endLocation) {
		value = removeNull(value);
		startLocation = startLocation - 1;
		if (startLocation >= 0 && value.length() > startLocation
				&& startLocation < endLocation) {
			value = value.substring(startLocation, endLocation);
		}
		return value;
	}

	/**
	 * 取左边的多少个字符多...
	 * @param  strValue �?有的字符 
	 * @param num 取左边几�?
	 * @return 左边的字符加...
	 * */

	public String leftString__(String value, int num) { // 取左边几�?
		value = removeNull(value);
		if (value.length() > num) {
			value = value.substring(0, num) + "..."; // 如果长度不够出现500�?
		}
		return value;
	}

	/**
	 * 在页面中弹出对话�?
	 * @param  sMessage 显示的语�? 
	 * @param location 跳转的位置连�?
	 * @return js弹出对话框的HTML代码
	 * */
	public String popMsg(String message, String location) {
		String sPopMsg = "<script language='javascript'>";
		sPopMsg += "window.alert('" + message + "');";
		if (location != null) {
			sPopMsg += "window.location='" + location + "';";
		}
		sPopMsg += "</script>";
		return sPopMsg;
	}

	/**
	 * 在页面中弹出对话�?
	 * @param  sMessage 显示的语�?
	 * @return js弹出对话框的HTML代码
	 * */
	public String popMsg(String message) {
		String sPopMsg = "<script language=\"JavaScript\">";
		sPopMsg += "window.alert('" + message + "');";
		sPopMsg += "</script>";
		return sPopMsg;
	}

	/**
	 * 得到文件的后�?名称
	 * @param fileName 文件路径或名�? 如： D:/sys.jpg �? sys.jpg 都可
	 * @return 后缀名称 如： .jpg
	 * */
	public String getFileNameSuffix(String fileName) /* 取文件的后缀�? * */{
		String sFileNameSuffix = "";
		fileName = removeNull(fileName);
		if (!fileName.equals("")) {
			int nLastLocation = fileName.lastIndexOf(".");
			int nLength = fileName.length();
			if (nLength >= nLastLocation) {
				sFileNameSuffix = fileName.substring(nLastLocation, nLength);
			}
		}
		return sFileNameSuffix; // 返回�? .doc .txt .rar �?
	}

	/**
	 * 得到文件的名称后�?无点
	 * @param fileName 文件路径或名�? 如： D:/sys.jpg �? sys.jpg 都可
	 * @return 后缀名称 如： jpg
	 * */
	public String getFileNameSuffixNotDot(String fileName) {
		String sFileNameSuffix = "";
		fileName = removeNull(fileName);
		if (!fileName.equals("")) {
			int lastLocation = fileName.lastIndexOf(".");
			lastLocation += 1; // 不要�?
			int length = fileName.length();
			if (length >= lastLocation) {
				sFileNameSuffix = fileName.substring(lastLocation, length);
			}
		}
		return sFileNameSuffix;
	}

	/**
	 * �? null �? "" 转换�? &nbsp;
	 * */
	public String changeNbsp(String value) {
		value = removeNull(value);
		if (value.equals("")) {
			value = "&nbsp;";
		}
		return value;
	}

	/**
	 * 获得网卡的MAC地址，如�?00-08-02-6A-B8-05
	 * */
	public String getMacAddress() {
		String sMacAddress = "";
		try {
			Process process = Runtime.getRuntime().exec("ipconfig -all");
			BufferedReader bufferedReader = new BufferedReader(
					new InputStreamReader(process.getInputStream()));
			String sTempLine = bufferedReader.readLine();
			for (; sTempLine != null;) {
				String sNextLine = bufferedReader.readLine();
				if (sTempLine.indexOf("Physical Address") > 0) {
					int i = sTempLine.indexOf("Physical Address") + 36;
					sMacAddress = sTempLine.substring(i);
					break;
				}
				sTempLine = sNextLine;
			}
			if (bufferedReader != null) {
				bufferedReader.close();
				bufferedReader = null;
			}
			if (process != null) {
				process.waitFor();
				process = null;
			}
		} catch (Exception ex) {
			sMacAddress="";
			this.systemOutException("自动取MAC地址出错了！" + ex.getMessage());
		}
		return sMacAddress;
	}

	/**
	 * 得到时间返回日期
	 * @param time 值类似于 1150094326718
	 * @return 2006-6-12
	 * */
	public String returnDate(long time) {
		GregorianCalendar dateTime = new GregorianCalendar();
		dateTime.setTimeInMillis(time);
		Date date = dateTime.getTime();
		DateFormat dateFormat = DateFormat.getDateInstance();
		return dateFormat.format(date);
	}

	/**
	 * 判断是否是日�?
	 * @param date 传来日期 �?:2006-07-31 (�? - �?)
	 * @return true �? false true
	 * */
	public boolean isDate(String date) {
		boolean bool = false;
		date = removeNull(date);
		if(date.indexOf("/")!=-1)
		date = Bean.replace(date, "/", "-");
		if(date.indexOf(".")!=-1)
		date = Bean.replace(date, ".", "-");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		sdf.setLenient(false);
		if (!date.equals("") && date.length() > 0) {
			try {
				Date dDate = (Date) (sdf.parse(date));
				String strCheck = sdf.format(dDate);
				if (date.equals(strCheck)) {
					bool = true;
				} else {
					bool = false;
				}
			} catch (Exception e) {
				bool = false;
			}
		}
		return bool;
	}

	/**
	 * 判断是否是日�?
	 * @param date 传来日期 �?:20060731 无中�?
	 * @return true �? false true
	 * */
	public boolean isDate_NoCenterLine(String date) {
		boolean bool = false;
		date = removeNull(date);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		sdf.setLenient(false);
		if (!date.equals("") && date.length() > 0) {
			try {
				Date dDate = (Date) (sdf.parse(date));
				String strCheck = sdf.format(dDate);
				if (date.equals(strCheck)) {
					bool = true;
				} else {
					bool = false;
				}
			} catch (Exception e) {
				bool = false;
			}
		}
		return bool;
	}

	/**
	 * 判断是否是时�?
	 * @param time 传来日期 �?:12:59:59 有冒�?
	 * @return true �? false true
	 * */
	public boolean isTime_Colon(String time) {
		boolean bool = false;
		time = removeNull(time);
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
		sdf.setLenient(false);
		if (!time.equals("") && time.length() > 0) {
			try {
				Date date = (Date) (sdf.parse(time));
				String strCheck = sdf.format(date);
				if (time.equals(strCheck)) {
					bool = true;
				} else {
					bool = false;
				}
			} catch (Exception e) {
				bool = false;
			}
		}
		return bool;
	}

	/**
	 * 判断是否是时�?
	 * @param time 传来日期 �?:125959 无冒�?
	 * @return true �? false true
	 * */
	public boolean isTime_NoColon(String time) {
		boolean bool = false;
		time = removeNull(time);
		SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
		sdf.setLenient(false);
		if (!time.equals("") && time.length() > 0) {
			try {
				Date date = (Date) (sdf.parse(time));
				String strCheck = sdf.format(date);
				if (time.equals(strCheck)) {
					bool = true;
				} else {
					bool = false;
				}
			} catch (Exception e) {
				bool = false;
			}
		}
		return bool;
	}

	/**
	 * 判断是否是日期与时间
	 * @param dateTime  传来日期与时�? �?:2006-08-01 12:59:59 (有线和冒�?)
	 * @return true �? false true
	 * */
	public boolean isDateTime_LineAndColon(String dateTime) {
		boolean bool = false;
		dateTime = removeNull(dateTime);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		sdf.setLenient(false);
		if (!dateTime.equals("") && dateTime.length() > 0) {
			try {
				Date date = (Date) (sdf.parse(dateTime));
				String strCheck = sdf.format(date);
				if (dateTime.equals(strCheck)) {
					bool = true;
				} else {
					bool = false;
				}
			} catch (Exception e) {
				bool = false;
			}
		}
		return bool;
	}

	/**
	 * 判断是否是日期与时间
	 * @param dateTime 传来日期与时�? �?:20060801125959 (有线和冒�?)
	 * @return true �? false true
	 * */
	public boolean isDateTime_NoLineAndColon(String dateTime) {
		boolean bool = false;
		dateTime = removeNull(dateTime);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		sdf.setLenient(false);
		if (!dateTime.equals("") && dateTime.length() > 0) {
			try {
				Date date = (Date) (sdf.parse(dateTime));
				String strCheck = sdf.format(date);
				if (dateTime.equals(strCheck)) {
					bool = true;
				} else {
					bool = false;
				}
			} catch (Exception e) {
				bool = false;
			}
		}
		return bool;
	}

	/**
	 * 得到日期部分
	 * @param dateTime 日期和时�? 如：2006-12-30 �? 2006/12/30 �?2006-12-30 12:12:12
	 *       �? 2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006-12-30
	 * */
	public String getDate(String dateTime) {
		return leftString(dateTime, 10);
	}

	/**
	 * 得到年份
	 * @param dateTime 日期和时�? 如：2006-12-30 �? 2006/12/30 �?2006-12-30 12:12:12
	 *       �? 2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006
	 * * */
	public String getYear(String dateTime) {
		return leftString(dateTime, 4);
	}

	/**
	 * 得到年份
	 * @param dateTime 日期和时�? 如：2006-12-30 �? 2006/12/30 �?2006-12-30 12:12:12
	 *       2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006
	 * */
	public int getYear_I(String dateTime) {
		return changeInt(getYear(dateTime));
	}

	/**
	 * 得到月份
	 * @param dateTime 日期和时�? 如：2006-12-30 �? 2006/12/30 �?2006-12-30 12:12:12
	 *        �? 2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006
	 * * */
	public String getMonth(String dateTime) {
		dateTime = dateTime.replaceAll("/", "-");
		if (dateTime.indexOf("-") != -1) {
			String[] arr = dateTime.split("-");
			dateTime = arr[1];
		} else {
			dateTime = "";
		}
		return dateTime;
	}

	/**
	 * 得到月份
	 * @param dateTime 日期和时�? 如：2006-12-30 �? 2006/12/30 �?2006-12-30 12:12:12
	 *        �? 2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006
	 * * */
	public int getMonth_I(String dateTime) {
		return changeInt(getMonth(dateTime));
	}

	/**
	 * 得到月份
	 * @param dateTime 日期和时�? 如：2006-12-30 �? 2006/12/30 �?2006-12-30 12:12:12
	 *        �? 2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006
	 * * */
	public String getDay(String dateTime) {
		dateTime = dateTime.replaceAll("/", "-");
		if (dateTime.indexOf("-") >= 2) {
			String[] arr = dateTime.split("-");
			dateTime = arr[2];
		} else {
			dateTime = "";
		}
		return dateTime;
	}

	/**
	 * 得到月份
	 * @param dateTime 日期和时�? 如：2006-12-30 �? 2006/12/30 �?2006-12-30 12:12:12
	 *            �? 2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006
	 * * */
	public int getDay_I(String dateTime) {
		return changeInt(getDay(dateTime));
	}

	/**
	 * 得到日期部分
	 * @param dateTime 日期和时�? 如：2006-12-30 12:12:12 �? 2006/12/30 12:12:12 的形�?
	 * @return 日期部分 如：2006-12-30
	 * * */
	public String getTime(String dateTime) {
		int nFirstLocation = dateTime.indexOf(":");
		nFirstLocation = nFirstLocation - 2;
		int nLength = dateTime.length();
		String str = "";
		if (nLength >= nFirstLocation) {
			if (nFirstLocation >= 0) {
				str = str.substring(nFirstLocation, nLength);
			}
		}
		return str;
	}

	/**
	 * 将错误信息打印成树型结构到控制台
	 * */
	public void systemOutException(Exception ex) {
		ex.printStackTrace();
	}

	/**
	 * 将错误信息打印到控制�?
	 * */
	public void systemOutException(String sMsg) {
		System.out.println(sMsg);
	}

	/**
	 * 得到数字的随机数 (有负数的)
	 * @return Int类型�?
	 * */
	public int getNumberRandom_I() {
		Random ran = new Random();
		int nValue = ran.nextInt();
		return nValue;
	}

	/**
	 * 得到数字的随机数(有负数的)
	 * @return String类型�?
	 * */
	public String getNumberRandom() {
		return changeString(getNumberRandom_I());
	}

	/**
	 * 将字符串中的某一段字符替换成新的字符 (JDK 1.4 以上自己就有�?)
	 * @param strValue 源字符串
	 * @param strOld   要替换的
	 * @param strNew   替换成的字符
	 * @return 替换后的新字符串
	 * */
	public static String replace(String strValue,String strOld,String strNew)   
	{   
	   int i=0;   
	   StringBuffer strBuffer=new StringBuffer(strValue);   
	   while((i=strValue.indexOf(strOld,i))>=0)   
	   {     
		   strBuffer.delete(i,i+strOld.length());   
		   strBuffer.insert(i,strNew);   
		   i=i+strNew.length();   
		   strValue=strBuffer.toString();   
	   }     
	   return strValue;   
	}     
	/**
	 * 数字转换成字符并在前面补足位数的 0
	 * @param Num 原数�?
	 * @param bit 组成字符的�?�位�? �?: 8�? 返回 00000010
	 * @return 组成后的字符�?
	 * */
	public String preAddZero(int Num,int bit)
	{
		String value=this.changeString(Num);
		int NumLength=bit-value.length();
		for(int i=0;i<NumLength;i++)
		{
			value="0"+value;
		}		
		return value;
	}
	/**
	 * 如果为空时，转换为一个默认日�?
	 */
	public String isNullDateToDefaultDate(String date) {
		date = this.removeNull(date);
		if (date.equals("")) {
			date = "1000-10-10";
		}
		return date;
	}

	/**
	 * 将存入时为空的日期转为空(默认日期设为�?)
	 */
	public String isDefaultDateToFreeDate(String date) {
		date = this.removeNull(date);
		if(!date.equals(""))
		{
			if (date.indexOf("1000-10-10") != -1) {
				date = "";
			}
		}
		return date;
	}
	/**
	 * 得到逗号对应的空格数(�?0000001,00000002, 返回�?&nbsp; &nbsp; &nbsp; &nbsp;)
	 * @param path 如：0000001,00000002,
	 * @return 如：&nbsp; &nbsp; &nbsp; &nbsp;
	 */
	 
	public String getAutoAddNbsp(String path) 
	{ 
		path=this.removeNull(path);
		String str="";
		if(path.indexOf(",")!=-1)
		{
			String [] PathArr=path.split(",");
			for(int i=0;i<PathArr.length-1;i++)
			{
				str+="&nbsp; &nbsp; ";
			}		
		}	
		return str;
	}
	/**
	 * 得到逗号对应的空格数(�?0000001,00000002, 返回�?&nbsp; &nbsp; &nbsp; &nbsp;)
	 * @param path 如：0000001,00000002,
	 * @return 如：&nbsp; &nbsp; &nbsp; &nbsp;
	 */
	 
	public String getAutoAddNbsp(String path,int startNum) 
	{ 
		path=this.removeNull(path);
		String str="";
		if(path.indexOf(",")!=-1)
		{
			String [] PathArr=path.split(",");
			for(int i=startNum;i<PathArr.length-1;i++)
			{
				str+="&nbsp; &nbsp; ";
			}		
		}	
		return str;
	}
	/**
	 * 转换特殊字符
	 * @param value 字符�?
	 * @return 回车转换�?<br>&nbsp;
	 */
	public String changeChar(String value){
		value=this.removeNull(value);	
		if(value.indexOf("<")!=-1)
		value=Bean.replace(value, "<", "&lt;");
		
		if(value.indexOf(">")!=-1)
		value=Bean.replace(value, ">", "&gt;");
		
		if(value.indexOf(">")!=-1)
		value=Bean.replace(value, ">", "&gt;");
		
		if(value.indexOf("\"")!=-1)
		value=Bean.replace(value, "\"", "&quot;");//&#34;	
		
		if(value.indexOf("'")!=-1)
		value=Bean.replace(value, "'", "&#39;");
		return value;
	}
	/**
	 * 将回车转换成<br>&nbsp;
	 * @param value 字符�?
	 * @return 回车转换�?<br>&nbsp;
	 */
	public String changeBR(String value){
		value=this.removeNull(value);
		if(value.indexOf("\r\n")!=-1)
		value=Bean.replace(value, "\r\n", "<br>&nbsp;");
		return value;
	}
}
