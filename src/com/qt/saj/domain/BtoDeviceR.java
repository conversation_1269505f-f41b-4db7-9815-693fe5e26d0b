package com.qt.saj.domain;




import java.util.Date;

/**
 * 逆变器实时数据
 * 
 * <AUTHOR> @date 2022-07-26 08:50:22
 */
public class BtoDeviceR {




	/**
	 * 电站uid
	 */
	private String plantUid;

	/**
	 * 逆变器对应的采集器SN
	 */
	private String dataloggerSn;

	/**
	 * 逆变器SN
	 */
	private String sn;

	/**
	 * 输入电流1路（A）
	 */
	private Float ipv1;

	/**
	 * 输入电流2路（A）
	 */
	private Float ipv2;

	/**
	 * 输入电流3路（A）
	 */
	private Float ipv3;

	/**
	 * 输入电压1路（V）
	 */
	private Float vpv1;

	/**
	 * 输入电压2路（V）
	 */
	private Float vpv2;

	/**
	 * 输入电压3路（V）
	 */
	private Float vpv3;

	/**
	 * 输出电流1路（A）
	 */
	private Float iac1;

	/**
	 * 输出电流2路（A）
	 */
	private Float iac2;

	/**
	 * 输出电流3路（A）
	 */
	private Float iac3;

	/**
	 * 输出电压1路（V）
	 */
	private Float vac1;

	/**
	 * 输出电压2路（V）
	 */
	private Float vac2;

	/**
	 * 输出电压3路（V）
	 */
	private Float vac3;

	/**
	 * 输出功率（W）
	 */
	private Float power;

	/**
	 * 
	 */
	private Float powerFactor;

	/**
	 * 当天发电量（kWh）
	 */
	private Float todayEnergy;

	/**
	 * 当月发电量（kWh）
	 */
	private Float monthEnergy;

	/**
	 * 当年发电量（kWh）
	 */
	private Float yearEnergy;

	/**
	 * 累计发电量（kWh）
	 */
	private Float totalEnergy;

	/**
	 * 温度（℃）
	 */
	private Float temperature;

	/**
	 * 频率（Hz）
	 */
	private Float fac;

	/**
	 * 频率（Hz）
	 */
	private Float fac1;

	/**
	 * 频率（Hz）
	 */
	private Float fac2;

	/**
	 * 数据时间
	 */
	private Date time;

	/**
	 * 
	 */
	private String status;


}
