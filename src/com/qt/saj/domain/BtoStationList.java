package com.qt.saj.domain;




import java.util.Date;

/**
 * 电站列表
 * 
 * <AUTHOR> @date 2022-07-23 10:47:36
 */
public class BtoStationList {

	/**
	 * 电站id
	 */
	private String plantId;

	/**
	 * 电站uid
	 */
	private String plantUid;

	/**
	 * 用户id
	 */
	private Integer userId;

	/**
	 * 电站名称
	 */
	private String name;

	/**
	 * 用户uid
	 */
	private String userUid;

	/**
	 * 电站当前状态（0：离线，1：正常运行，2：告警运行）
	 */
	private Integer status;

	/**
	 * 国家
	 */
	private String country;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 地址1
	 */
	private String address1;

	/**
	 * 地址2
	 */
	private String address2;

	/**
	 * 经度
	 */
	private Float longitude;

	/**
	 * 维度
	 */
	private Float latitude;

	/**
	 * 装机容量（kWp）
	 */
	private String peakPower;

	/**
	 * 建站日期
	 */
	private Date createDate;

	/**
	 * 当前功率（kW）
	 */
	private Float currentPower;

	/**
	 * 日发电量（kWh）
	 */
	private Float todayEnergy;

	/**
	 * 累计发电量（kWh）
	 */
	private Float totalEnergy;

	/**
	 * 电站类型（0：并网，1：储能，2：混合，3：交流耦合）
	 */
	private Integer planType;

	/**
	 * 安装商
	 */
	private String installer;

	/**
	 * 运维商
	 */
	private String operator;

	/**
	 * 用户名字
	 */
	private String owner;

	/**
	 * 用户电话
	 */
	private String ownerPhone;

	/**
	 * 运维器SN码
	 */
	private String wisdomDeviceSn;

	/**
	 * 图片 url
	 */
	private String imageUrl;

	/**
	 * 请求时的语言
	 */
	private String locale;

	/**
	 * 三晶电站id
	 */
	private String plantNo;

	/**
	 * 用户邮箱
	 */
	private String ownerEmail;

	/**
	 * 
	 */
	private String ownerName;


}
