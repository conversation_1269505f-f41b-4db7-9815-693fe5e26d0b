package com.qt.saj.controller.analysis;

import com.qt.saj.service.ElectricityService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Controller
@RequestMapping("/saj")
public class ElectricityController {

    @Resource
    private ElectricityService electricityService;


    @RequestMapping("dasd")
    @ResponseBody
    public void selectElectric() {
        electricityService.selectElectric();
    }


}
