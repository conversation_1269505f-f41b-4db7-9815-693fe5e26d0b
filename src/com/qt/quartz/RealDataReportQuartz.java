package com.qt.quartz;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.qt.bean.statistics.CacheRealData;
import com.qt.entity.file.DclLine;
import com.qt.entity.file.DcsStation;
import com.qt.entity.statistics.StoOriginal;
import com.qt.service.file.DclLineService;
import com.qt.service.file.DcsStationService;
import com.qt.service.statistics.StoOriginalService;

public class RealDataReportQuartz {

	@Autowired
	private DclLineService dclLineService;

	@Autowired
	private DcsStationService dcsStationService;

	@Autowired
	private StoOriginalService stoOriginalService;

	/**
	 * 存当天的时间 时报表
	 */
	public void RealDataLoad() {
		/*System.out.println("==============");
		System.out.println("时报表插入开始");
		// ====================================================
		// 获得是能源的参数编号

		// 获得所有的station_id
		System.out.println("========================================================");
		System.out.println("station缓存数据");
		实时数据表格部分
		//电流
		List<DcsStation> staList = dcsStationService.getAllDcsStation(null);
		for (DcsStation dcsStation : staList) {
			Map<String, Object> query = new HashMap<String, Object>();
			// 站点电流的数据
			int[] eleCurParams = new int[] { 10001, 10002, 10003 };
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.eleCurList.put(dcsStation.getDcsId() + "dianLiudcsStation", stoOriginalService.getStoOriginalTableList(query));
		}
		
		System.out.println("========================================================");
		System.out.println("line缓存数据");
		List<DclLine> lineList = dclLineService.getAllDclLine(null);
		for (DclLine dclLine : lineList) {
			int[] eleCurParams1 = new int[] { 10001, 10002, 10003 };
			for (DcsStation dcsStation : staList) {
				
				Map<String, Object> query1 = new HashMap<String, Object>();
				// 站点电流的数据
				int[] eleCurParams = new int[] { 10001, 10002, 10003 };
				query1.put("stoStationId", dcsStation.getDcsId());
				
				query1.put("stoLineId", dclLine.getDclId());
				query1.put("bsaDescs", eleCurParams1);
				CacheRealData.eleCurList.put(dcsStation.getDcsId() + "dianLiudcsStation"+dclLine.getDclId() + "dianLiudclLine", stoOriginalService.getStoOriginalTableList(query1));
			}
		}

		System.out.println("点点========================================================");
		//电压
		List<DcsStation> staList1 = dcsStationService.getAllDcsStation(null);
		for (DcsStation dcsStation : staList1) {
			Map<String, Object> query = new HashMap<String, Object>();
			// 站点电流的数据
			int[] eleCurParams = new int[] { 10004, 10005, 10006 };
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.voltageList.put(dcsStation.getDcsId() + "dianYadcsStation", stoOriginalService.getStoOriginalTableList(query));
		}
		

		System.out.println("========================================================");
		System.out.println("line缓存数据");
		List<DclLine> lineList1 = dclLineService.getAllDclLine(null);
		for (DclLine dclLine : lineList1) {
			Map<String, Object> query = new HashMap<String, Object>();
			for (DcsStation dcsStation : staList) {
			int[] eleCurParams = new int[] { 10004, 10005, 10006 };
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("stoLineId", dclLine.getDclId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.voltageList.put(dcsStation.getDcsId() + "dianYadcsStation"+dclLine.getDclId() + "dianYadclLine", stoOriginalService.getStoOriginalTableList(query));
			}
		}
		
		//负荷
		List<DcsStation> staList2 = dcsStationService.getAllDcsStation(null);
		for (DcsStation dcsStation : staList2) {
			Map<String, Object> query = new HashMap<String, Object>();
			// 站点电流的数据
			int[] eleCurParams = new int[] { 10017, 10019};
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.loadList.put(dcsStation.getDcsId() + "FuHedcsStation", stoOriginalService.getStoOriginalTableList(query));
		}
		
		System.out.println("========================================================");
		System.out.println("line缓存数据");
		List<DclLine> lineList2 = dclLineService.getAllDclLine(null);
		for (DclLine dclLine : lineList2) {
			for (DcsStation dcsStation : staList2) {
			Map<String, Object> query = new HashMap<String, Object>();
			int[] eleCurParams = new int[] { 10017, 10019 };
			query.put("stoLineId", dclLine.getDclId());
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.loadList.put(dcsStation.getDcsId() + "FuHedcsStation"+dclLine.getDclId() + "FuHedclLine", stoOriginalService.getStoOriginalTableList(query));
			}
		}
		
		//功率因数
		List<DcsStation> staList3 = dcsStationService.getAllDcsStation(null);
		for (DcsStation dcsStation : staList3) {
			Map<String, Object> query = new HashMap<String, Object>();
			// 站点电流的数据
			int[] eleCurParams = new int[] { 10018};
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.powerList.put(dcsStation.getDcsId() + "KldcsStation", stoOriginalService.getStoOriginalTableList(query));
		}
		
		System.out.println("========================================================");
		System.out.println("line缓存数据");
		List<DclLine> lineList3 = dclLineService.getAllDclLine(null);
		for (DclLine dclLine : lineList3) {
			for (DcsStation dcsStation : staList3) {
			Map<String, Object> query = new HashMap<String, Object>();
			int[] eleCurParams = new int[] { 10018 };
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("stoLineId", dclLine.getDclId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.powerList.put(dcsStation.getDcsId() + "KldcsStation"+dclLine.getDclId() + "KldclLine", stoOriginalService.getStoOriginalTableList(query));
			}
		}
		//谐波
		List<DcsStation> staList4 = dcsStationService.getAllDcsStation(null);
		for (DcsStation dcsStation : staList4) {
			Map<String, Object> query = new HashMap<String, Object>();
			// 站点电流的数据
			int[] eleCurParams = new int[] { 10035,10036,10037,10038,10039,10040};
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.harmonicList.put(dcsStation.getDcsId() + "xbdcsStation", stoOriginalService.getStoOriginalTableList(query));
		}
		
		System.out.println("========================================================");
		System.out.println("line缓存数据");
		List<DclLine> lineList4 = dclLineService.getAllDclLine(null);
		for (DclLine dclLine : lineList4) {
			for (DcsStation dcsStation : staList4) {
			Map<String, Object> query = new HashMap<String, Object>();
			int[] eleCurParams = new int[] { 10035,10036,10037,10038,10039,10040 };
			query.put("stoStationId", dcsStation.getDcsId());
			query.put("stoLineId", dclLine.getDclId());
			query.put("bsaDescs", eleCurParams);
			CacheRealData.harmonicList.put(dcsStation.getDcsId() + "xbdcsStation"+dclLine.getDclId() + "xbdclLine", stoOriginalService.getStoOriginalTableList(query));
			}
		}
		
		//温度
				List<DcsStation> staList5 = dcsStationService.getAllDcsStation(null);
				for (DcsStation dcsStation : staList5) {
					Map<String, Object> query = new HashMap<String, Object>();
					// 站点电流的数据
					int[] eleCurParams = new int[] { 10044};
					query.put("stoStationId", dcsStation.getDcsId());
					query.put("bsaDescs", eleCurParams);
					CacheRealData.tempList.put(dcsStation.getDcsId() + "wbdcsStation", stoOriginalService.getStoOriginalTableList(query));
				}
				
				System.out.println("========================================================");
				System.out.println("line缓存数据");
				List<DclLine> lineList5 = dclLineService.getAllDclLine(null);
				for (DclLine dclLine : lineList5) {
					for (DcsStation dcsStation : staList5) {
					Map<String, Object> query = new HashMap<String, Object>();
					int[] eleCurParams = new int[] { 10044 };
					query.put("stoStationId", dcsStation.getDcsId());
					query.put("stoLineId", dclLine.getDclId());
					query.put("bsaDescs", eleCurParams);
					CacheRealData.tempList.put(dcsStation.getDcsId() + "wbdcsStation"+dclLine.getDclId() + "wbdclLine", stoOriginalService.getStoOriginalTableList(query));
					}
				}
		
				// ----------------------------------------------------------------------------------------
				System.out.println("时报表插入结束");
				System.out.println("==============");*/
			
				
		System.out.println("时报表插入开始");
		System.out.println("==============");
				/*实时数据曲线部分*/	
		Calendar ca=Calendar.getInstance();
		ca.set(Calendar.HOUR_OF_DAY, 0);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		Date stoOrignalTime = ca.getTime();
		System.out.println(stoOrignalTime);
		
		
		ca.set(Calendar.HOUR_OF_DAY, 23);
		ca.set(Calendar.MINUTE, 59);
		ca.set(Calendar.SECOND, 59);
		Date stoOrignalTime1 = ca.getTime();
		System.out.println(stoOrignalTime1);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");		
				//String stoOrignalTime = s.toString();
				//电流
				
				List<DclLine> lineList6 = dclLineService.getAllDclLine(null);
				for (DclLine dclLine : lineList6) {
					List<StoOriginal> realData1;
					List<List<StoOriginal>> list = new ArrayList<List<StoOriginal>>();
					Map<String, Object> query1 = new HashMap<String, Object>();
					query1.put("begin",(sdf.format(stoOrignalTime)).substring(0,19));
					query1.put("end", (sdf.format(stoOrignalTime1)).substring(0,19));
					query1.put("stoStationId", dclLine.getDclDcsId());
					query1.put("stoLineId", dclLine.getDclId());
					
					realData1 = stoOriginalService.getRealTimeData(query1);
					
					
					
					
					Map<String, Object> map = new HashMap<String,Object>();
					map.put("line", realData1);
					CacheRealData.eleCurList1.put(dclLine.getDclDcsId() +"dlqxStation"+dclLine.getDclId() + "dlqxdclLine", map);
				}
				System.out.println("时报表插入结束");
				System.out.println("==============");
				/*//电压
				
				for (DclLine dclLine : lineList6) {
					for (DcsStation dcsStation : staList5) {
						Map<String, Object> realData1;
						Map<String, Object> realData2;
						Map<String, Object> realData3;
						List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
						List<Map<String,Object>> tableList = new ArrayList<Map<String,Object>>();
					Map<String, Object> query1 = new HashMap<String, Object>();
					query1.put("bsaDesc", (Integer)10004);
					query1.put("stoOrignalTime", stoOrignalTime);
					query1.put("stoStationId", dcsStation.getDcsId());
					query1.put("stoLineId", dclLine.getDclId());
					realData1 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableA = (List<Map<String, Object>>) realData1.get("tableData");
					tableList.addAll(tableA);
					list.add(0, realData1);
					
					query1.put("bsaDesc", (Integer)10005);
					
					realData2 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableB = (List<Map<String, Object>>) realData2.get("tableData");
					tableList.addAll(tableB);
					list.add(1, realData2);
					
					
					query1.put("bsaDesc", (Integer)10006);
					realData3 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableC = (List<Map<String, Object>>) realData3.get("tableData");
					tableList.addAll(tableC);
					list.add(2, realData3);
					
					Map<String, Object> map = new HashMap<String,Object>();
					map.put("line", list);
					map.put("table", tableList);
					CacheRealData.voltageList1.put(dcsStation.getDcsId() +"dyqxStation"+dclLine.getDclId() + "dyqxdclLine", map);
					}
				}
				
				
				//负荷
				
				for (DclLine dclLine : lineList6) {
					for (DcsStation dcsStation : staList5) {
						Map<String, Object> realData1;
						Map<String, Object> realData2;
						List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
						List<Map<String,Object>> tableList = new ArrayList<Map<String,Object>>();
					Map<String, Object> query1 = new HashMap<String, Object>();
					query1.put("bsaDesc", (Integer)10017);
					query1.put("stoOrignalTime", stoOrignalTime);
					query1.put("stoStationId", dcsStation.getDcsId());
					query1.put("stoLineId", dclLine.getDclId());
					realData1 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableA = (List<Map<String, Object>>) realData1.get("tableData");
					tableList.addAll(tableA);
					list.add(0, realData1);
					
					query1.put("bsaDesc", (Integer)10019);
					
					realData2 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableB = (List<Map<String, Object>>) realData2.get("tableData");
					tableList.addAll(tableB);
					list.add(1, realData2);
					
					
					
					
					Map<String, Object> map = new HashMap<String,Object>();
					map.put("line", list);
					map.put("table", tableList);
					CacheRealData.loadList1.put(dcsStation.getDcsId() +"fhqxStation"+dclLine.getDclId() + "fhqxdclLine", map);
					}
				}	
				
				
				//功率因数
				for (DclLine dclLine : lineList6) {
					for (DcsStation dcsStation : staList5) {
						Map<String, Object> realData1;
						List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
						List<Map<String,Object>> tableList = new ArrayList<Map<String,Object>>();
					Map<String, Object> query1 = new HashMap<String, Object>();
					query1.put("bsaDesc", (Integer)10018);
					query1.put("stoOrignalTime", stoOrignalTime);
					query1.put("stoStationId", dcsStation.getDcsId());
					query1.put("stoLineId", dclLine.getDclId());
					realData1 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableA = (List<Map<String, Object>>) realData1.get("tableData");
					tableList.addAll(tableA);
					list.add(0, realData1);
					
					Map<String, Object> map = new HashMap<String,Object>();
					map.put("line", list);
					map.put("table", tableList);
					CacheRealData.powerList1.put(dcsStation.getDcsId() +"glqxStation"+dclLine.getDclId() + "glqxdclLine", map);
					}
				}
				
				//谐波
				for (DclLine dclLine : lineList6) {
					for (DcsStation dcsStation : staList5) {
						Map<String, Object> realData1;
						Map<String, Object> realData2;
						Map<String, Object> realData3;
						Map<String, Object> realData4;
						Map<String, Object> realData5;
						Map<String, Object> realData6;
						List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
						List<Map<String,Object>> tableList = new ArrayList<Map<String,Object>>();
					Map<String, Object> query1 = new HashMap<String, Object>();
					query1.put("bsaDesc", (Integer)10035);
					query1.put("stoOrignalTime", stoOrignalTime);
					query1.put("stoStationId", dcsStation.getDcsId());
					query1.put("stoLineId", dclLine.getDclId());
					realData1 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableA = (List<Map<String, Object>>) realData1.get("tableData");
					tableList.addAll(tableA);
					list.add(0, realData1);
					
					query1.put("bsaDesc", (Integer)10036);
					
					realData2 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableB = (List<Map<String, Object>>) realData2.get("tableData");
					tableList.addAll(tableB);
					list.add(1, realData2);
					
					
					query1.put("bsaDesc", (Integer)10037);
					realData3 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableC = (List<Map<String, Object>>) realData3.get("tableData");
					tableList.addAll(tableC);
					list.add(2, realData3);
					
					query1.put("bsaDesc", (Integer)10038);
					realData4 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableD = (List<Map<String, Object>>) realData4.get("tableData");
					tableList.addAll(tableD);
					list.add(3, realData4);
					
					query1.put("bsaDesc", (Integer)10039);
					realData5 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableE = (List<Map<String, Object>>) realData5.get("tableData");
					tableList.addAll(tableE);
					list.add(4, realData5);
					
					query1.put("bsaDesc", (Integer)10040);
					realData6 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableF = (List<Map<String, Object>>) realData6.get("tableData");
					tableList.addAll(tableF);
					list.add(5, realData6);
					
					Map<String, Object> map = new HashMap<String,Object>();
					map.put("line", list);
					map.put("table", tableList);
					CacheRealData.harmonicList1.put(dcsStation.getDcsId() +"xbqxStation"+dclLine.getDclId() + "xbqxdclLine", map);
					}
				}
				
				//温度
				for (DclLine dclLine : lineList6) {
					for (DcsStation dcsStation : staList5) {
						Map<String, Object> realData1;
						List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
						List<Map<String,Object>> tableList = new ArrayList<Map<String,Object>>();
					Map<String, Object> query1 = new HashMap<String, Object>();
					query1.put("bsaDesc", (Integer)10044);
					query1.put("stoOrignalTime", stoOrignalTime);
					query1.put("stoStationId", dcsStation.getDcsId());
					query1.put("stoLineId", dclLine.getDclId());
					realData1 = stoOriginalService.getRealTimeData(query1);
					List<Map<String, Object>> tableA = (List<Map<String, Object>>) realData1.get("tableData");
					tableList.addAll(tableA);
					list.add(0, realData1);
					
					Map<String, Object> map = new HashMap<String,Object>();
					map.put("line", list);
					map.put("table", tableList);
					CacheRealData.tempList1.put(dcsStation.getDcsId() +"wdqxStation"+dclLine.getDclId() + "wdqxdclLine", map);
					}
				}
				*/
				
				
	}			
}
