package com.qt.bean.statistics;

public class ValueStaticsForm {
	private String time;// 时间段
	private String dclName;// 监测点

	// 负荷
	private Float stlOrignalValueA;// A相负荷
	private Float stlOrignalValueB;// B相负荷
	private Float stlOrignalValueC;// C相负荷
	private Float stlOrignalValueTotal;// 总负荷

	// 电量
	private Float stpElecValue;// 电量

	// 示数
	private Float stoOrignalValueZY;// 电量
	private Float stoOrignalValueFY;// 电量
	private Float stoOrignalValueZW;// 电量
	private Float stoOrignalValueFW;// 电量

	// 电压
	private Float stoOrignalValueDYA;// A电压
	private Float stoOrignalValueDYB;// B电压
	private Float stoOrignalValueDYC;// C电压

	// 电流
	private Float stoOrignalValueDLA;// A电压
	private Float stoOrignalValueDLB;// B电压
	private Float stoOrignalValueDLC;// C电压
	private Float stoOrignalValueDLTotal;// C电压

	// 功率因素
	private Float stoOrignalValueGLA;// A电压
	private Float stoOrignalValueGLB;// B电压
	private Float stoOrignalValueGLC;// C电压
	private Float stoOrignalValueGLTotal;// C电压
	
	// 回路电流
	private Float stoOrignalValueLDL1;
	private Float stoOrignalValueLDL2;
	private Float stoOrignalValueLDL3;
	private Float stoOrignalValueLDL4;
	private Float stoOrignalValueLDL5;
	private Float stoOrignalValueLDL6;
	private Float stoOrignalValueLDL7;
	private Float stoOrignalValueLDL8;
	private Float stoOrignalValueLDL9;
	
	
	public Float getStoOrignalValueWD() {
		return stoOrignalValueWD;
	}

	public void setStoOrignalValueWD(Float stoOrignalValueWD) {
		this.stoOrignalValueWD = stoOrignalValueWD;
	}

	// 温度
	private Float stoOrignalValueWD;// 
	private Float stoOrignalValueWDA;// A电压
	private Float stoOrignalValueWDB;// B电压
	private Float stoOrignalValueWDC;// C电压

	// 谐波
	private Float stoOrignalValueXBDLA;// B电压
	private Float stoOrignalValueXBDYA;// C电压
	private Float stoOrignalValueXBDLB;// B电压
	private Float stoOrignalValueXBDYB;// C电压
	private Float stoOrignalValueXBDLC;// B电压
	private Float stoOrignalValueXBDYC;// C电压

	
	public Float getStlOrignalValueTotal() {
		return stlOrignalValueTotal;
	}

	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getDclName() {
		return dclName;
	}

	public void setDclName(String dclName) {
		this.dclName = dclName;
	}

	public Float getStlOrignalValueA() {
		return stlOrignalValueA;
	}

	public void setStlOrignalValueA(Float stlOrignalValueA) {
		this.stlOrignalValueA = stlOrignalValueA;
	}

	public Float getStlOrignalValueB() {
		return stlOrignalValueB;
	}

	public void setStlOrignalValueB(Float stlOrignalValueB) {
		this.stlOrignalValueB = stlOrignalValueB;
	}

	public Float getStlOrignalValueC() {
		return stlOrignalValueC;
	}

	public void setStlOrignalValueC(Float stlOrignalValueC) {
		this.stlOrignalValueC = stlOrignalValueC;
	}

	public Float getStpElecValue() {
		return stpElecValue;
	}

	public void setStpElecValue(Float stpElecValue) {
		this.stpElecValue = stpElecValue;
	}

	public Float getStoOrignalValueZY() {
		return stoOrignalValueZY;
	}

	public void setStoOrignalValueZY(Float stoOrignalValueZY) {
		this.stoOrignalValueZY = stoOrignalValueZY;
	}

	public Float getStoOrignalValueFY() {
		return stoOrignalValueFY;
	}

	public void setStoOrignalValueFY(Float stoOrignalValueFY) {
		this.stoOrignalValueFY = stoOrignalValueFY;
	}

	public Float getStoOrignalValueZW() {
		return stoOrignalValueZW;
	}

	public void setStoOrignalValueZW(Float stoOrignalValueZW) {
		this.stoOrignalValueZW = stoOrignalValueZW;
	}

	public Float getStoOrignalValueFW() {
		return stoOrignalValueFW;
	}

	public void setStoOrignalValueFW(Float stoOrignalValueFW) {
		this.stoOrignalValueFW = stoOrignalValueFW;
	}

	public Float getStoOrignalValueDYA() {
		return stoOrignalValueDYA;
	}

	public void setStoOrignalValueDYA(Float stoOrignalValueDYA) {
		this.stoOrignalValueDYA = stoOrignalValueDYA;
	}

	public Float getStoOrignalValueDYB() {
		return stoOrignalValueDYB;
	}

	public void setStoOrignalValueDYB(Float stoOrignalValueDYB) {
		this.stoOrignalValueDYB = stoOrignalValueDYB;
	}

	public Float getStoOrignalValueDYC() {
		return stoOrignalValueDYC;
	}

	public void setStoOrignalValueDYC(Float stoOrignalValueDYC) {
		this.stoOrignalValueDYC = stoOrignalValueDYC;
	}

	public Float getStoOrignalValueDLA() {
		return stoOrignalValueDLA;
	}

	public void setStoOrignalValueDLA(Float stoOrignalValueDLA) {
		this.stoOrignalValueDLA = stoOrignalValueDLA;
	}

	public Float getStoOrignalValueDLB() {
		return stoOrignalValueDLB;
	}

	public void setStoOrignalValueDLB(Float stoOrignalValueDLB) {
		this.stoOrignalValueDLB = stoOrignalValueDLB;
	}

	public Float getStoOrignalValueDLC() {
		return stoOrignalValueDLC;
	}

	public void setStoOrignalValueDLC(Float stoOrignalValueDLC) {
		this.stoOrignalValueDLC = stoOrignalValueDLC;
	}

	public Float getStoOrignalValueDLTotal() {
		return stoOrignalValueDLTotal;
	}

	public void setStoOrignalValueDLTotal(Float stoOrignalValueDLTotal) {
		this.stoOrignalValueDLTotal = stoOrignalValueDLTotal;
	}

	public Float getStoOrignalValueGLA() {
		return stoOrignalValueGLA;
	}

	public void setStoOrignalValueGLA(Float stoOrignalValueGLA) {
		this.stoOrignalValueGLA = stoOrignalValueGLA;
	}

	public Float getStoOrignalValueGLB() {
		return stoOrignalValueGLB;
	}

	public void setStoOrignalValueGLB(Float stoOrignalValueGLB) {
		this.stoOrignalValueGLB = stoOrignalValueGLB;
	}

	public Float getStoOrignalValueGLC() {
		return stoOrignalValueGLC;
	}

	public void setStoOrignalValueGLC(Float stoOrignalValueGLC) {
		this.stoOrignalValueGLC = stoOrignalValueGLC;
	}

	public Float getStoOrignalValueGLTotal() {
		return stoOrignalValueGLTotal;
	}

	public void setStoOrignalValueGLTotal(Float stoOrignalValueGLTotal) {
		this.stoOrignalValueGLTotal = stoOrignalValueGLTotal;
	}

	public Float getStoOrignalValueWDA() {
		return stoOrignalValueWDA;
	}

	public void setStoOrignalValueWDA(Float stoOrignalValueWDA) {
		this.stoOrignalValueWDA = stoOrignalValueWDA;
	}

	public Float getStoOrignalValueWDB() {
		return stoOrignalValueWDB;
	}

	public void setStoOrignalValueWDB(Float stoOrignalValueWDB) {
		this.stoOrignalValueWDB = stoOrignalValueWDB;
	}

	public Float getStoOrignalValueWDC() {
		return stoOrignalValueWDC;
	}

	public void setStoOrignalValueWDC(Float stoOrignalValueWDC) {
		this.stoOrignalValueWDC = stoOrignalValueWDC;
	}

	public Float getStoOrignalValueXBDLA() {
		return stoOrignalValueXBDLA;
	}

	public void setStoOrignalValueXBDLA(Float stoOrignalValueXBDLA) {
		this.stoOrignalValueXBDLA = stoOrignalValueXBDLA;
	}

	public Float getStoOrignalValueXBDYA() {
		return stoOrignalValueXBDYA;
	}

	public void setStoOrignalValueXBDYA(Float stoOrignalValueXBDYA) {
		this.stoOrignalValueXBDYA = stoOrignalValueXBDYA;
	}

	public Float getStoOrignalValueXBDLB() {
		return stoOrignalValueXBDLB;
	}

	public void setStoOrignalValueXBDLB(Float stoOrignalValueXBDLB) {
		this.stoOrignalValueXBDLB = stoOrignalValueXBDLB;
	}

	public Float getStoOrignalValueXBDYB() {
		return stoOrignalValueXBDYB;
	}

	public void setStoOrignalValueXBDYB(Float stoOrignalValueXBDYB) {
		this.stoOrignalValueXBDYB = stoOrignalValueXBDYB;
	}

	public Float getStoOrignalValueXBDLC() {
		return stoOrignalValueXBDLC;
	}

	public void setStoOrignalValueXBDLC(Float stoOrignalValueXBDLC) {
		this.stoOrignalValueXBDLC = stoOrignalValueXBDLC;
	}

	public Float getStoOrignalValueXBDYC() {
		return stoOrignalValueXBDYC;
	}

	public void setStoOrignalValueXBDYC(Float stoOrignalValueXBDYC) {
		this.stoOrignalValueXBDYC = stoOrignalValueXBDYC;
	}

	public void setStlOrignalValueTotal(Float stlOrignalValueTotal) {
		this.stlOrignalValueTotal = stlOrignalValueTotal;
	}

	public Float getStoOrignalValueLDL1() {
		return stoOrignalValueLDL1;
	}

	public void setStoOrignalValueLDL1(Float stoOrignalValueLDL1) {
		this.stoOrignalValueLDL1 = stoOrignalValueLDL1;
	}

	public Float getStoOrignalValueLDL2() {
		return stoOrignalValueLDL2;
	}

	public void setStoOrignalValueLDL2(Float stoOrignalValueLDL2) {
		this.stoOrignalValueLDL2 = stoOrignalValueLDL2;
	}

	public Float getStoOrignalValueLDL3() {
		return stoOrignalValueLDL3;
	}

	public void setStoOrignalValueLDL3(Float stoOrignalValueLDL3) {
		this.stoOrignalValueLDL3 = stoOrignalValueLDL3;
	}

	public Float getStoOrignalValueLDL4() {
		return stoOrignalValueLDL4;
	}

	public void setStoOrignalValueLDL4(Float stoOrignalValueLDL4) {
		this.stoOrignalValueLDL4 = stoOrignalValueLDL4;
	}

	public Float getStoOrignalValueLDL5() {
		return stoOrignalValueLDL5;
	}

	public void setStoOrignalValueLDL5(Float stoOrignalValueLDL5) {
		this.stoOrignalValueLDL5 = stoOrignalValueLDL5;
	}

	public Float getStoOrignalValueLDL6() {
		return stoOrignalValueLDL6;
	}

	public void setStoOrignalValueLDL6(Float stoOrignalValueLDL6) {
		this.stoOrignalValueLDL6 = stoOrignalValueLDL6;
	}

	public Float getStoOrignalValueLDL7() {
		return stoOrignalValueLDL7;
	}

	public void setStoOrignalValueLDL7(Float stoOrignalValueLDL7) {
		this.stoOrignalValueLDL7 = stoOrignalValueLDL7;
	}

	public Float getStoOrignalValueLDL8() {
		return stoOrignalValueLDL8;
	}

	public void setStoOrignalValueLDL8(Float stoOrignalValueLDL8) {
		this.stoOrignalValueLDL8 = stoOrignalValueLDL8;
	}

	public Float getStoOrignalValueLDL9() {
		return stoOrignalValueLDL9;
	}

	public void setStoOrignalValueLDL9(Float stoOrignalValueLDL9) {
		this.stoOrignalValueLDL9 = stoOrignalValueLDL9;
	}

	
}
