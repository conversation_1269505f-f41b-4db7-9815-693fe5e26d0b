package com.qt.bean.statistics;

public class MonitorConstrastForm {
	//站点名称
	private String stationName;
	
	//回路名称
	private String lineName;
	
	//回路名称1
	private String lineNameA;
	
	//回路名称2
	private String lineNameB;
	
	//对比时间
	private String time1;
	
	//最大值
	private Float stpElecValueA;
	
	//最大值时间
	private String maxTime;
	
	//最小值
	private Float stpElecValueB;
	
	//最小值时间
	private String minTime;
	
	//平均值
	private Float averValue;

	public String getLineNameA() {
		return lineNameA;
	}

	public void setLineNameA(String lineNameA) {
		this.lineNameA = lineNameA;
	}

	public String getLineNameB() {
		return lineNameB;
	}

	public void setLineNameB(String lineNameB) {
		this.lineNameB = lineNameB;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getLineName() {
		return lineName;
	}

	public void setLineName(String lineName) {
		this.lineName = lineName;
	}

	public String getTime1() {
		return time1;
	}

	public void setTime1(String time1) {
		this.time1 = time1;
	}

	public Float getStpElecValueA() {
		return stpElecValueA;
	}

	public void setStpElecValueA(Float stpElecValueA) {
		this.stpElecValueA = stpElecValueA;
	}

	public String getMaxTime() {
		return maxTime;
	}

	public void setMaxTime(String maxTime) {
		this.maxTime = maxTime;
	}

	public Float getStpElecValueB() {
		return stpElecValueB;
	}

	public void setStpElecValueB(Float stpElecValueB) {
		this.stpElecValueB = stpElecValueB;
	}

	public String getMinTime() {
		return minTime;
	}

	public void setMinTime(String minTime) {
		this.minTime = minTime;
	}

	public Float getAverValue() {
		return averValue;
	}

	public void setAverValue(Float averValue) {
		this.averValue = averValue;
	}
	
	
}
