package com.qt.bean.statistics;

public class MonitorConstrastFXForm {
	//站点名称
	private String stationName;
	
	//回路名称
	private String lineName;
	
	//对比时间
	private String time1;
	
	//A相电压
	private Float stoOrignalValueDLA;
	
	//B相电压
	private Float stoOrignalValueDLB;
	
	//C相电压
	private Float stoOrignalValueDLC;
		
	//A相功率因数
	private Float stoOrignalValueGLA;
	
	//B相功率因数
	private Float stoOrignalValueGLB;
	
	//C相功率因数
	private Float stoOrignalValueGLC;
	
	//A相温度
	private Float stoOrignalValueWDA;
	
	//B相温度
	private Float stoOrignalValueWDB;
	
	//C相温度
	private Float stoOrignalValueWDC;

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getLineName() {
		return lineName;
	}

	public void setLineName(String lineName) {
		this.lineName = lineName;
	}

	public String getTime1() {
		return time1;
	}

	public void setTime1(String time1) {
		this.time1 = time1;
	}

	public Float getStoOrignalValueDLA() {
		return stoOrignalValueDLA;
	}

	public void setStoOrignalValueDLA(Float stoOrignalValueDLA) {
		this.stoOrignalValueDLA = stoOrignalValueDLA;
	}

	public Float getStoOrignalValueDLB() {
		return stoOrignalValueDLB;
	}

	public void setStoOrignalValueDLB(Float stoOrignalValueDLB) {
		this.stoOrignalValueDLB = stoOrignalValueDLB;
	}

	public Float getStoOrignalValueDLC() {
		return stoOrignalValueDLC;
	}

	public void setStoOrignalValueDLC(Float stoOrignalValueDLC) {
		this.stoOrignalValueDLC = stoOrignalValueDLC;
	}

	public Float getStoOrignalValueGLA() {
		return stoOrignalValueGLA;
	}

	public void setStoOrignalValueGLA(Float stoOrignalValueGLA) {
		this.stoOrignalValueGLA = stoOrignalValueGLA;
	}

	public Float getStoOrignalValueGLB() {
		return stoOrignalValueGLB;
	}

	public void setStoOrignalValueGLB(Float stoOrignalValueGLB) {
		this.stoOrignalValueGLB = stoOrignalValueGLB;
	}

	public Float getStoOrignalValueGLC() {
		return stoOrignalValueGLC;
	}

	public void setStoOrignalValueGLC(Float stoOrignalValueGLC) {
		this.stoOrignalValueGLC = stoOrignalValueGLC;
	}

	public Float getStoOrignalValueWDA() {
		return stoOrignalValueWDA;
	}

	public void setStoOrignalValueWDA(Float stoOrignalValueWDA) {
		this.stoOrignalValueWDA = stoOrignalValueWDA;
	}

	public Float getStoOrignalValueWDB() {
		return stoOrignalValueWDB;
	}

	public void setStoOrignalValueWDB(Float stoOrignalValueWDB) {
		this.stoOrignalValueWDB = stoOrignalValueWDB;
	}

	public Float getStoOrignalValueWDC() {
		return stoOrignalValueWDC;
	}

	public void setStoOrignalValueWDC(Float stoOrignalValueWDC) {
		this.stoOrignalValueWDC = stoOrignalValueWDC;
	}
	
	
}
