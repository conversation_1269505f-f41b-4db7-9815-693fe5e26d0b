package com.qt.bean.statistics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.qt.entity.system.resources.Resources;

/** 
 * <AUTHOR>
 * @date 创建时间：2016年8月29日 下午7:16:17
 * @version v1.0
 * 实时数据的缓存
 */
public class CacheRealData {
	
	public static String testWord = "初始化1";
	//表格部分
	public static Map<String,Object> eleCurList = new HashMap<String,Object>();	//电流的数据
	public static Map<String,Object> voltageList = new HashMap<String,Object>();	//电压的数据
	public static Map<String,Object> loadList = new HashMap<String,Object>();	//负荷的数据
	public static Map<String,Object> powerList = new HashMap<String,Object>();	//功率因素的数据	`
	public static Map<String,Object> harmonicList = new HashMap<String,Object>();//谐波的数据
	public static Map<String,Object> tempList = new HashMap<String,Object>();	//温度的数据
	//曲线部分
	public static Map<String,Object> eleCurList1 = new HashMap<String,Object>();	//电流的数据
	public static Map<String,Object> voltageList1 = new HashMap<String,Object>();	//电压的数据
	public static Map<String,Object> loadList1 = new HashMap<String,Object>();	//负荷的数据
	public static Map<String,Object> powerList1 = new HashMap<String,Object>();	//功率因素的数据	`
	public static Map<String,Object> harmonicList1 = new HashMap<String,Object>();//谐波的数据
	public static Map<String,Object> tempList1 = new HashMap<String,Object>();	//温度的数据
	
	//登录用户名，判断是否需要查询菜单
	public static String LogoingName="";
	
	public static List<Resources> stationArchivesList = new ArrayList<Resources>();

    public static List<Resources> stationArchivesList5 = new ArrayList<Resources>();//企业档案页面跳转时，按钮权限使用
	
	public static List<Resources> stationArchivesList6 = new ArrayList<Resources>();//变压器档案页面跳转时，按钮权限使用
	public static List<Resources> stationArchivesList7 = new ArrayList<Resources>();//回路档案页面跳转时，按钮权限使用
	public static List<Resources> stationArchivesList15 = new ArrayList<Resources>();//指标码管理页面跳转时，按钮权限使用
	public static List<Resources> stationBaseList = new ArrayList<Resources>();//站点档案页面跳转时，按钮权限使用
	
	public static List<Resources> gatewayList = new ArrayList<Resources>();//网关档案页面跳转时，按钮权限使用
	
	public static List<Resources> equipmentList = new ArrayList<Resources>();//设备档案页面跳转时，按钮权限使用
	
	public static List<Resources> videoList = new ArrayList<Resources>();//视频档案页面跳转时，按钮权限使用
}
